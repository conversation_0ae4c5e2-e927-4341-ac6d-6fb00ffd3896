# Compliance & Certification Management API

## Overview

The Compliance & Certification Management system provides comprehensive tools for managing product compliance, certifications, export restrictions, and regulatory requirements. This system ensures that factories can properly track and validate their products' compliance status across different markets and regulatory frameworks.

## Core Features

### 1. Certification Management
- Track multiple certifications per product (CE, FDA, ISO, etc.)
- Monitor certification expiry dates and renewal requirements
- Store certification documents and reference numbers
- Validate certification status and authenticity

### 2. Export Compliance
- HS Code validation and classification
- Country-specific export restrictions
- Export license tracking and management
- Tariff classification and duty rate information

### 3. Regulatory Compliance
- Compliance standards tracking (RoHS, REACH, CPSIA, etc.)
- Regulatory approval management
- Testing certificate storage
- Quality control integration

### 4. Market-Specific Requirements
- Generate compliance checklists for target markets
- Validate country codes and market access
- Track market-specific certifications
- Monitor regulatory changes and updates

## API Endpoints

### Product Compliance Management

#### Update Product Compliance
```typescript
updateCompliance: protectedProcedure
  .input(z.object({
    productId: z.string().cuid(),
    compliance: z.object({
      certifications: z.array(z.object({
        name: z.string().min(1),
        number: z.string().optional(),
        issuedBy: z.string().optional(),
        issuedDate: z.string().datetime().optional(),
        expiryDate: z.string().datetime().optional(),
        documentUrl: z.string().url().optional(),
        status: z.enum(['ACTIVE', 'EXPIRED', 'PENDING', 'REVOKED']),
      })),
      complianceStandards: z.array(z.object({
        standard: z.string().min(1),
        version: z.string().optional(),
        complianceDate: z.string().datetime().optional(),
        testingLab: z.string().optional(),
        reportNumber: z.string().optional(),
        reportUrl: z.string().url().optional(),
      })),
      exportRestrictions: z.array(z.object({
        country: z.string().length(2),
        restriction: z.string().min(1),
        restrictionType: z.enum(['PROHIBITED', 'RESTRICTED', 'LICENSE_REQUIRED', 'QUOTA']),
        licenseRequired: z.boolean(),
        notes: z.string().optional(),
      })),
      hsCode: z.string().regex(/^\d{6,10}$/),
      countryOfOrigin: z.string().length(2),
      exportLicense: z.object({
        required: z.boolean(),
        licenseNumber: z.string().optional(),
        issuingAuthority: z.string().optional(),
        validFrom: z.string().datetime().optional(),
        validUntil: z.string().datetime().optional(),
        documentUrl: z.string().url().optional(),
      }),
      // ... more fields
    }),
  }))
```

#### Get Product Compliance
```typescript
getCompliance: protectedProcedure
  .input(z.object({
    productId: z.string().cuid(),
  }))
  .query(async ({ input, ctx }) => {
    // Returns complete compliance data for a product
  })
```

### Validation Services

#### Validate HS Code
```typescript
validateHSCode: protectedProcedure
  .input(z.object({
    hsCode: z.string().min(1),
  }))
  .query(async ({ input }) => {
    // Returns validation result with code breakdown
    return {
      valid: boolean,
      message: string,
      details: {
        chapter: string,
        heading: string,
        subheading: string,
        tariffItem?: string,
      }
    }
  })
```

#### Validate Country Code
```typescript
validateCountryCode: protectedProcedure
  .input(z.object({
    countryCode: z.string().length(2),
  }))
  .query(async ({ input }) => {
    // Returns country validation and name
    return {
      valid: boolean,
      message: string,
      countryName?: string,
    }
  })
```

#### Check Export Restrictions
```typescript
checkExportRestrictions: protectedProcedure
  .input(z.object({
    productId: z.string().cuid(),
    targetCountry: z.string().length(2),
  }))
  .query(async ({ input, ctx }) => {
    // Returns export restriction analysis
    return {
      productId: string,
      productName: string,
      targetCountry: string,
      targetCountryName: string,
      allowed: boolean,
      restrictions: Array<{
        restrictionType: string,
        licenseRequired: boolean,
      }>,
    }
  })
```

### Compliance Analytics

#### Get Compliance Summary
```typescript
getComplianceSummary: protectedProcedure
  .query(async ({ ctx }) => {
    // Returns factory-wide compliance analytics
    return {
      summary: {
        totalProducts: number,
        productsWithCompliance: number,
        productsWithCertifications: number,
        productsWithHSCode: number,
        productsWithExportRestrictions: number,
        expiringSoon: number,
        complianceRate: number,
      },
      insights: {
        certificationTypes: string[],
        complianceStandards: string[],
        countriesOfOrigin: string[],
      },
    }
  })
```

#### Generate Compliance Checklist
```typescript
generateComplianceChecklist: protectedProcedure
  .input(z.object({
    targetMarkets: z.array(z.string().length(2)),
    productCategory: z.string().optional(),
  }))
  .query(async ({ input }) => {
    // Returns market-specific compliance checklist
    return {
      targetMarkets: Array<{
        code: string,
        name: string,
      }>,
      checklist: Array<{
        category: string,
        items: Array<{
          requirement: string,
          mandatory: boolean,
          description: string,
        }>,
      }>,
      generatedAt: string,
    }
  })
```

### Reference Data

#### Get Certification Types
```typescript
getCertificationTypes: protectedProcedure
  .query(async () => {
    // Returns available certification types
    return {
      certificationTypes: Array<{
        code: string,
        name: string,
        description: string,
      }>,
    }
  })
```

#### Get Compliance Standards
```typescript
getComplianceStandards: protectedProcedure
  .query(async () => {
    // Returns available compliance standards
    return {
      complianceStandards: Array<{
        code: string,
        name: string,
        description: string,
      }>,
    }
  })
```

#### Get Supported Countries
```typescript
getSupportedCountries: protectedProcedure
  .query(async () => {
    // Returns supported country codes
    return {
      countries: Array<{
        code: string,
        name: string,
      }>,
    }
  })
```

## Data Models

### Certification Object
```typescript
interface Certification {
  name: string;                    // e.g., "CE", "FDA", "ISO 9001"
  number?: string;                 // Certificate number
  issuedBy?: string;              // Issuing authority
  issuedDate?: string;            // ISO datetime
  expiryDate?: string;            // ISO datetime
  documentUrl?: string;           // Link to certificate document
  status: 'ACTIVE' | 'EXPIRED' | 'PENDING' | 'REVOKED';
}
```

### Compliance Standard Object
```typescript
interface ComplianceStandard {
  standard: string;               // e.g., "RoHS", "REACH", "FCC"
  version?: string;               // Standard version
  complianceDate?: string;        // ISO datetime
  testingLab?: string;           // Testing laboratory
  reportNumber?: string;         // Test report number
  reportUrl?: string;            // Link to test report
}
```

### Export Restriction Object
```typescript
interface ExportRestriction {
  country: string;                // ISO 3166-1 alpha-2 country code
  restriction: string;           // Description of restriction
  restrictionType: 'PROHIBITED' | 'RESTRICTED' | 'LICENSE_REQUIRED' | 'QUOTA';
  licenseRequired: boolean;      // Whether export license is required
  notes?: string;                // Additional notes
}
```

### Export License Object
```typescript
interface ExportLicense {
  required: boolean;             // Whether license is required
  licenseNumber?: string;        // License number if obtained
  issuingAuthority?: string;     // Authority that issued license
  validFrom?: string;            // ISO datetime
  validUntil?: string;           // ISO datetime
  documentUrl?: string;          // Link to license document
}
```

## Supported Standards and Certifications

### Safety & Quality Certifications
- **CE**: Conformité Européenne (European Conformity)
- **FCC**: Federal Communications Commission
- **UL**: Underwriters Laboratories
- **CSA**: Canadian Standards Association
- **PSE**: Product Safety Electrical Appliance and Material Safety Law
- **CCC**: China Compulsory Certification
- **KC**: Korea Certification
- **BSMI**: Bureau of Standards, Metrology and Inspection

### Quality Management Systems
- **ISO 9001**: Quality Management Systems
- **ISO 14001**: Environmental Management Systems
- **ISO 45001**: Occupational Health and Safety
- **ISO 27001**: Information Security Management

### Compliance Standards
- **RoHS**: Restriction of Hazardous Substances Directive
- **REACH**: Registration, Evaluation, Authorisation and Restriction of Chemicals
- **WEEE**: Waste Electrical and Electronic Equipment Directive
- **CPSIA**: Consumer Product Safety Improvement Act
- **EMC**: Electromagnetic Compatibility
- **FCC Part 15**: Radio Frequency Devices

## Usage Examples

### Update Product Compliance
```typescript
const result = await trpc.products.updateCompliance.mutate({
  productId: "product-123",
  compliance: {
    certifications: [
      {
        name: "CE",
        number: "CE-2024-001",
        issuedBy: "Notified Body 1234",
        issuedDate: "2024-01-01T00:00:00Z",
        expiryDate: "2027-01-01T00:00:00Z",
        status: "ACTIVE",
      }
    ],
    hsCode: "123456",
    countryOfOrigin: "CN",
    exportRestrictions: [
      {
        country: "US",
        restriction: "Export license required",
        restrictionType: "LICENSE_REQUIRED",
        licenseRequired: true,
      }
    ],
  },
});
```

### Check Export Compliance
```typescript
const restrictions = await trpc.products.checkExportRestrictions.query({
  productId: "product-123",
  targetCountry: "US",
});

if (!restrictions.allowed) {
  console.log("Export prohibited to", restrictions.targetCountryName);
} else if (restrictions.restrictions.length > 0) {
  console.log("Export restrictions apply:", restrictions.restrictions);
}
```

### Generate Market Checklist
```typescript
const checklist = await trpc.products.generateComplianceChecklist.query({
  targetMarkets: ["US", "DE", "JP"],
  productCategory: "electronics",
});

checklist.checklist.forEach(category => {
  console.log(`${category.category}:`);
  category.items.forEach(item => {
    console.log(`- ${item.requirement} (${item.mandatory ? 'Required' : 'Optional'})`);
  });
});
```

## Security and Permissions

All compliance management endpoints require:
- **Authentication**: Valid user session
- **Factory Access**: User must belong to a factory
- **Permissions**: Appropriate PRODUCT_READ or PRODUCT_WRITE permissions
- **Data Isolation**: Multi-tenant security ensures factory data separation

## Error Handling

The API provides comprehensive error handling for:
- Invalid HS codes and country codes
- Missing or expired certifications
- Export restriction violations
- Permission and access control issues
- Data validation errors

## Integration Notes

The compliance system integrates with:
- **Product Management**: Embedded in product data structure
- **Quality Control**: Links with quality grading and testing
- **Order Management**: Validates compliance before order processing
- **Analytics**: Provides compliance metrics and reporting
- **ERP Systems**: Exports compliance data for external systems
