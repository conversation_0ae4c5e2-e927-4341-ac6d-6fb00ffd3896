# Advanced Pricing Structure API

## Overview

The Advanced Pricing Structure system provides comprehensive dynamic pricing capabilities for products, including volume discounts, regional pricing, seasonal adjustments, and cost breakdown analysis. This system builds upon the existing price breaks functionality to offer enterprise-grade pricing management.

## Core Features

### 1. Volume Pricing (Price Breaks)
- Quantity-based pricing tiers
- Automatic price calculation based on order volume
- Support for multiple currency pricing
- Flexible minimum quantity requirements

### 2. Regional Pricing
- Market-specific pricing strategies
- Country and region-based price adjustments
- Currency conversion support
- Percentage markup and fixed adjustments
- Time-based validity periods

### 3. Seasonal Pricing
- Time-based pricing adjustments
- Holiday and promotional pricing
- Priority-based adjustment system
- Quantity-specific seasonal offers
- Automatic activation and deactivation

### 4. Cost Breakdown Analysis
- Detailed cost component tracking
- Margin analysis and optimization
- Suggested pricing calculations
- Multi-component cost structure
- Profitability insights

### 5. Comprehensive Price Calculation
- Multi-layered pricing logic
- Real-time price computation
- Currency conversion integration
- Historical pricing analysis
- Pricing analytics and insights

## Database Schema

### Regional Pricing Model
```sql
CREATE TABLE product_regional_pricing (
  id VARCHAR PRIMARY KEY,
  region VARCHAR NOT NULL,
  country VARCHAR(2),
  base_price DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  markup DECIMAL(5,2),
  fixed_adjustment DECIMAL(10,2),
  valid_from TIMESTAMP NOT NULL,
  valid_until TIMESTAMP,
  product_id VARCHAR NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  created_by VARCHAR
);
```

### Seasonal Pricing Model
```sql
CREATE TABLE product_seasonal_pricing (
  id VARCHAR PRIMARY KEY,
  name VARCHAR NOT NULL,
  description TEXT,
  start_date TIMESTAMP NOT NULL,
  end_date TIMESTAMP NOT NULL,
  adjustment_type VARCHAR NOT NULL, -- 'PERCENTAGE' or 'FIXED'
  adjustment_value DECIMAL(10,2) NOT NULL,
  min_quantity INTEGER,
  max_quantity INTEGER,
  is_active BOOLEAN DEFAULT TRUE,
  priority INTEGER DEFAULT 0,
  product_id VARCHAR NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  created_by VARCHAR
);
```

### Cost Breakdown Model
```sql
CREATE TABLE product_cost_breakdown (
  id VARCHAR PRIMARY KEY,
  material_cost DECIMAL(10,2) NOT NULL,
  labor_cost DECIMAL(10,2) NOT NULL,
  overhead_cost DECIMAL(10,2) NOT NULL,
  packaging_cost DECIMAL(10,2),
  shipping_cost DECIMAL(10,2),
  marketing_cost DECIMAL(10,2),
  target_margin DECIMAL(5,2) NOT NULL,
  actual_margin DECIMAL(5,2),
  total_cost DECIMAL(10,2) NOT NULL,
  suggested_price DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  quantity_basis INTEGER DEFAULT 1,
  valid_from TIMESTAMP NOT NULL,
  valid_until TIMESTAMP,
  product_id VARCHAR NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  created_by VARCHAR
);
```

## API Endpoints

### Regional Pricing Management

#### Update Regional Pricing
```typescript
updateRegionalPricing: protectedProcedure
  .input(z.object({
    productId: z.string().cuid(),
    regionalPricing: z.array(z.object({
      region: z.string().min(1),
      country: z.string().length(2).optional(),
      basePrice: z.number().positive(),
      currency: z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']),
      markup: z.number().min(-100).max(1000).optional(),
      fixedAdjustment: z.number().optional(),
      validFrom: z.string().datetime(),
      validUntil: z.string().datetime().optional(),
    })),
  }))
```

**Example Usage:**
```typescript
const result = await trpc.products.updateRegionalPricing.mutate({
  productId: "product-123",
  regionalPricing: [
    {
      region: "US",
      basePrice: 100.00,
      currency: "USD",
      markup: 15, // 15% markup
      validFrom: "2024-01-01T00:00:00Z",
      validUntil: "2024-12-31T23:59:59Z",
    },
    {
      region: "EU",
      country: "DE",
      basePrice: 85.00,
      currency: "EUR",
      fixedAdjustment: 5.00, // €5 additional charge
      validFrom: "2024-01-01T00:00:00Z",
    }
  ],
});
```

### Seasonal Pricing Management

#### Update Seasonal Pricing
```typescript
updateSeasonalPricing: protectedProcedure
  .input(z.object({
    productId: z.string().cuid(),
    seasonalPricing: z.array(z.object({
      name: z.string().min(1),
      description: z.string().optional(),
      startDate: z.string().datetime(),
      endDate: z.string().datetime(),
      adjustmentType: z.enum(['PERCENTAGE', 'FIXED']),
      adjustmentValue: z.number(),
      minQuantity: z.number().int().positive().optional(),
      maxQuantity: z.number().int().positive().optional(),
      isActive: z.boolean().default(true),
      priority: z.number().int().min(0).default(0),
    })),
  }))
```

**Example Usage:**
```typescript
const result = await trpc.products.updateSeasonalPricing.mutate({
  productId: "product-123",
  seasonalPricing: [
    {
      name: "Black Friday Sale",
      description: "25% discount for Black Friday weekend",
      startDate: "2024-11-29T00:00:00Z",
      endDate: "2024-12-01T23:59:59Z",
      adjustmentType: "PERCENTAGE",
      adjustmentValue: -25, // 25% discount
      minQuantity: 10,
      isActive: true,
      priority: 10,
    },
    {
      name: "Holiday Premium",
      description: "Premium pricing during holiday season",
      startDate: "2024-12-15T00:00:00Z",
      endDate: "2024-01-05T23:59:59Z",
      adjustmentType: "FIXED",
      adjustmentValue: 20.00, // $20 additional charge
      priority: 5,
    }
  ],
});
```

### Cost Breakdown Management

#### Update Cost Breakdown
```typescript
updateCostBreakdown: protectedProcedure
  .input(z.object({
    productId: z.string().cuid(),
    costBreakdown: z.object({
      materialCost: z.number().min(0),
      laborCost: z.number().min(0),
      overheadCost: z.number().min(0),
      packagingCost: z.number().min(0).optional(),
      shippingCost: z.number().min(0).optional(),
      marketingCost: z.number().min(0).optional(),
      targetMargin: z.number().min(0).max(1000),
      currency: z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']),
      quantityBasis: z.number().int().positive().default(1),
      validFrom: z.string().datetime(),
      validUntil: z.string().datetime().optional(),
    }),
  }))
```

**Example Usage:**
```typescript
const result = await trpc.products.updateCostBreakdown.mutate({
  productId: "product-123",
  costBreakdown: {
    materialCost: 45.00,
    laborCost: 25.00,
    overheadCost: 15.00,
    packagingCost: 5.00,
    shippingCost: 10.00,
    targetMargin: 30, // 30% target margin
    currency: "USD",
    quantityBasis: 100, // Cost per 100 units
    validFrom: "2024-01-01T00:00:00Z",
  },
});

// Response includes calculated breakdown
{
  success: true,
  message: "Cost breakdown updated successfully",
  breakdown: {
    totalCost: 100.00,
    suggestedPrice: 142.86,
    actualMargin: 30.00,
    costPerUnit: 1.00,
    breakdown: {
      material: { amount: 45.00, percentage: 45.0 },
      labor: { amount: 25.00, percentage: 25.0 },
      overhead: { amount: 15.00, percentage: 15.0 },
      packaging: { amount: 5.00, percentage: 5.0 },
      shipping: { amount: 10.00, percentage: 10.0 },
    }
  }
}
```

### Comprehensive Price Calculation

#### Calculate Price
```typescript
calculatePrice: protectedProcedure
  .input(z.object({
    productId: z.string().cuid(),
    quantity: z.number().int().positive(),
    region: z.string().optional(),
    country: z.string().length(2).optional(),
    currency: z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']).optional(),
    calculationDate: z.string().datetime().optional(),
  }))
```

**Example Usage:**
```typescript
const pricing = await trpc.products.calculatePrice.query({
  productId: "product-123",
  quantity: 500,
  region: "US",
  currency: "USD",
  calculationDate: "2024-11-30T12:00:00Z", // During Black Friday
});

// Response includes comprehensive pricing breakdown
{
  productId: "product-123",
  productName: "Premium Widget",
  quantity: 500,
  currency: "USD",
  calculationDate: "2024-11-30T12:00:00Z",
  pricing: {
    basePrice: 100.00,
    unitPrice: 86.25, // After all adjustments
    totalPrice: 43125.00,
    breakdown: {
      volumeDiscount: {
        unitPrice: 95.00,
        appliedBreak: { minQuantity: 500, unitPrice: 95.00 }
      },
      regionalAdjustment: {
        adjustedPrice: 109.25 // 15% markup applied
      },
      seasonalAdjustment: {
        adjustedPrice: 86.25, // 25% Black Friday discount
        appliedAdjustments: [
          {
            adjustmentType: "PERCENTAGE",
            adjustmentValue: -25,
            priority: 10
          }
        ]
      }
    }
  },
  appliedPricing: {
    region: "US",
    seasonalAdjustments: 1,
    priceBreaks: 3
  }
}
```

## Pricing Analytics

### Get Pricing Analytics
```typescript
getPricingAnalytics: protectedProcedure
  .query(async ({ ctx }) => {
    // Returns factory-wide pricing analytics
    return {
      summary: {
        totalProducts: 150,
        productsWithPriceBreaks: 120,
        productsWithRegionalPricing: 45,
        productsWithSeasonalPricing: 30,
        productsWithCostBreakdown: 75,
        averageMargin: 28.5,
        pricingCoverage: {
          priceBreaks: 80.0,
          regionalPricing: 30.0,
          seasonalPricing: 20.0,
          costBreakdown: 50.0,
        },
      },
    };
  })
```

## Utility Functions

### Currency Conversion
```typescript
convertCurrencyAmount: protectedProcedure
  .input(z.object({
    amount: z.number(),
    fromCurrency: z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']),
    toCurrency: z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']),
  }))
```

### Get Exchange Rates
```typescript
getExchangeRates: protectedProcedure
  .query(async () => {
    return {
      rates: {
        'USD': 1.0,
        'EUR': 0.85,
        'GBP': 0.73,
        'CNY': 7.2,
        'JPY': 110.0,
        'KRW': 1200.0,
        'CAD': 1.25,
        'AUD': 1.35,
      },
      baseCurrency: 'USD',
      lastUpdated: '2024-01-15T10:30:00Z',
    };
  })
```

### Get Pricing Regions
```typescript
getPricingRegions: protectedProcedure
  .query(async () => {
    return {
      regions: [
        { code: 'US', name: 'United States' },
        { code: 'EU', name: 'European Union' },
        { code: 'APAC', name: 'Asia-Pacific' },
        { code: 'LATAM', name: 'Latin America' },
        { code: 'MENA', name: 'Middle East & North Africa' },
        { code: 'GLOBAL', name: 'Global Default' },
      ],
    };
  })
```

## Pricing Logic Flow

### 1. Base Price Determination
- Start with product's base price
- Apply currency conversion if needed

### 2. Volume Pricing Application
- Check quantity against price breaks
- Apply highest applicable volume discount
- Use price break unit price if applicable

### 3. Regional Pricing Adjustment
- Check for active regional pricing
- Apply percentage markup or fixed adjustment
- Consider region and country specificity

### 4. Seasonal Pricing Application
- Filter active seasonal pricing by date
- Apply quantity-based filters
- Process adjustments by priority order
- Support both percentage and fixed adjustments

### 5. Final Price Calculation
- Combine all adjustments
- Ensure minimum price constraints
- Return comprehensive breakdown

## Integration Examples

### E-commerce Integration
```typescript
// Get real-time pricing for product page
const productPricing = await trpc.products.calculatePrice.query({
  productId: selectedProduct.id,
  quantity: cartQuantity,
  region: userRegion,
  currency: userCurrency,
});

// Display pricing breakdown to user
displayPricing({
  unitPrice: productPricing.pricing.unitPrice,
  totalPrice: productPricing.pricing.totalPrice,
  savings: productPricing.pricing.basePrice - productPricing.pricing.unitPrice,
  appliedDiscounts: productPricing.pricing.breakdown,
});
```

### Quote Generation
```typescript
// Generate quote with comprehensive pricing
const quoteItems = await Promise.all(
  selectedProducts.map(async (product) => {
    const pricing = await trpc.products.calculatePrice.query({
      productId: product.id,
      quantity: product.quantity,
      region: customer.region,
      currency: quote.currency,
      calculationDate: quote.validUntil,
    });
    
    return {
      product: product,
      pricing: pricing,
      lineTotal: pricing.pricing.totalPrice,
    };
  })
);
```

### Analytics Dashboard
```typescript
// Display pricing analytics
const analytics = await trpc.products.getPricingAnalytics.query();

renderDashboard({
  totalProducts: analytics.summary.totalProducts,
  averageMargin: analytics.summary.averageMargin,
  pricingCoverage: analytics.summary.pricingCoverage,
  recommendations: generatePricingRecommendations(analytics),
});
```

## Security and Permissions

All pricing management endpoints require:
- **Authentication**: Valid user session
- **Factory Access**: User must belong to a factory
- **Permissions**: Appropriate PRODUCT_READ or PRODUCT_WRITE permissions
- **Data Isolation**: Multi-tenant security ensures factory data separation

## Performance Considerations

- **Caching**: Price calculations are optimized for real-time performance
- **Indexing**: Database indexes on key fields (productId, region, dates)
- **Batch Operations**: Support for bulk pricing updates
- **Currency Rates**: Cached exchange rates with periodic updates
- **Query Optimization**: Efficient database queries for complex pricing logic

## Error Handling

The pricing system provides comprehensive error handling for:
- Invalid product IDs or access permissions
- Date range validation for seasonal and regional pricing
- Currency conversion errors
- Quantity and pricing validation
- Cost breakdown calculation errors
- Conflicting pricing rules resolution
