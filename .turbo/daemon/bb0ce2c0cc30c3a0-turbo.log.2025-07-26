2025-07-26T07:23:10.080808Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/.turbo-cookie"), AnchoredSystemPathBuf(".turbo/cookies/1.cookie")}
2025-07-26T07:23:10.080819Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-26T07:23:15.482114Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/@radix-ui.js"), AnchoredSystemPathBuf("apps/web/.next/server/_error.js"), AnchoredSystemPathBuf("apps/web/.next/server/app/dashboard/messages"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/app"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/app/layout.fe629538f61f26a8.hot-update.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/pages"), AnchoredSystemPathBuf("apps/web/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/web/.next/server/app/api"), AnchoredSystemPathBuf("apps/web/.next/routes-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/react-refresh.js"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/lucide-react.js"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/react-remove-scroll-bar.js"), AnchoredSystemPathBuf("apps/web/.next/server/app/dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/not-found.js"), AnchoredSystemPathBuf("apps/web/.next/fallback-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/media/747892c23ea88013-s.woff2"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/react-style-singleton.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/661d4a46c511e931.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/web/.next/types/app/dashboard/products"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/api/auth/me"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/web/.next/server/app/dashboard/messages/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/dashboard/page.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/main-app.js"), AnchoredSystemPathBuf("apps/web/.next/server/app/api/auth/me"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/dashboard/products/page.js"), AnchoredSystemPathBuf("apps/web/.next/static/development"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/main-app.js"), AnchoredSystemPathBuf("apps/web/.next/server/app/api/auth/me/route_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/web/.next/static/media"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/pages/_error.js"), AnchoredSystemPathBuf("apps/web/.next/types/app/dashboard/messages"), AnchoredSystemPathBuf("apps/web/.next/server/interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/polyfills.js"), AnchoredSystemPathBuf("apps/web/.next/static/media/93f479601ee12b01-s.p.woff2"), AnchoredSystemPathBuf("apps/web/.next/server/pages"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/dashboard/products"), AnchoredSystemPathBuf("apps/web/.next/static/media/569ce4b8f30dc480-s.p.woff2"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app-pages-internals.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/app/layout.661d4a46c511e931.hot-update.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/app/layout.a9845b69f5f97959.hot-update.js"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback"), AnchoredSystemPathBuf("apps/web/.next/static/media/9610d9e46709d722-s.woff2"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/webpack.a6d636fb2e24d02f.hot-update.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/webpack.661d4a46c511e931.hot-update.js"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/is-what.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/633457081244afec._.hot-update.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/webpack.b0fff2d23cf24704.hot-update.js"), AnchoredSystemPathBuf("apps/web/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/api/auth/me/route.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/pages"), AnchoredSystemPathBuf("apps/web/.next/server/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/@tanstack.js"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/app/_not-found/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/get-nonce.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/1c6bce252fb359f4.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/3422e6509e219f79.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/web/.next/types/app/dashboard/messages/page.ts"), AnchoredSystemPathBuf("apps/web/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/@floating-ui.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/main.js"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/@trpc.js"), AnchoredSystemPathBuf("apps/web/.next/types/package.json"), AnchoredSystemPathBuf("apps/web/.next/server/app/dashboard/products/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/@swc.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/loading.js"), AnchoredSystemPathBuf("apps/web/.next/server/pages/_error.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/_not-found"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/a6d636fb2e24d02f.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/web/.next/server/app/dashboard/messages/page.js"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/react-remove-scroll.js"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/tailwind-merge.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/webpack.fe629538f61f26a8.hot-update.js"), AnchoredSystemPathBuf("apps/web/.next/server/pages/_app.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/b0fff2d23cf24704.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/amp.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/webpack.a83cae656d7a7c2c.hot-update.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/dashboard/messages/page.js"), AnchoredSystemPathBuf("apps/web/.next/static/media/ba015fad6dcf6784-s.woff2"), AnchoredSystemPathBuf("apps/web/.next/static/webpack"), AnchoredSystemPathBuf("apps/web/.next/server/app/dashboard"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/aria-hidden.js"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/use-sync-external-store.js"), AnchoredSystemPathBuf("apps/web/.next/static/css"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/app/dashboard"), AnchoredSystemPathBuf("apps/web/.next/server/app/dashboard/page.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/_error.js"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/clsx.js"), AnchoredSystemPathBuf("apps/web/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/app"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/api/auth"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/dashboard/messages"), AnchoredSystemPathBuf("apps/web/.next/static/css/app/layout.css"), AnchoredSystemPathBuf("apps/web/.next/types/app/api/auth/me/route.ts"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/use-callback-ref.js"), AnchoredSystemPathBuf("apps/web/.next/server/pages/_document.js"), AnchoredSystemPathBuf("apps/web/.next/trace"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/fe629538f61f26a8.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/api"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/webpack.1c6bce252fb359f4.hot-update.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/webpack.3422e6509e219f79.hot-update.js"), AnchoredSystemPathBuf("apps/web/.next/types/app"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/superjson.js"), AnchoredSystemPathBuf("apps/web/.next/static/css/app"), AnchoredSystemPathBuf("apps/web/.next/package.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/pages/_error.js"), AnchoredSystemPathBuf("apps/web/.next/types/app/api"), AnchoredSystemPathBuf("apps/web/.next/static/media/8d697b304b401681-s.woff2"), AnchoredSystemPathBuf("apps/web/.next/types/app/dashboard/page.ts"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/app/dashboard/messages/page.a83cae656d7a7c2c.hot-update.js"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/app/dashboard/messages"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/use-sidecar.js"), AnchoredSystemPathBuf("apps/web/.next/types/app/dashboard"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/next.js"), AnchoredSystemPathBuf("apps/web/.next/prerender-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/react-refresh.js"), AnchoredSystemPathBuf("apps/web/.next/types/app/api/auth"), AnchoredSystemPathBuf("apps/web/.next/types/app/api/auth/me"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/a9845b69f5f97959.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/web/.next/types/app/dashboard/products/page.ts"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/dashboard"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/class-variance-authority.js"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/tslib.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/webpack.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/app/error.js"), AnchoredSystemPathBuf("apps/web/.next/server/app/api/auth/me/route.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/pages/_app.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/main.js"), AnchoredSystemPathBuf("apps/web/.next/static/chunks"), AnchoredSystemPathBuf("apps/web/.next/server/app/_not-found"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/webpack.a9845b69f5f97959.hot-update.js"), AnchoredSystemPathBuf("apps/web/.next/server/app/dashboard/products/page.js"), AnchoredSystemPathBuf("apps/web/.next/server/app/api/auth"), AnchoredSystemPathBuf("apps/web/.next/types"), AnchoredSystemPathBuf("apps/web/.next/static/webpack/a83cae656d7a7c2c.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/fallback/pages/_app.js"), AnchoredSystemPathBuf("apps/web/.next/server"), AnchoredSystemPathBuf("apps/web/.next/server/vendor-chunks/copy-anything.js"), AnchoredSystemPathBuf("apps/web/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/app/dashboard/products")}
2025-07-26T07:23:15.482141Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-26T07:23:15.681599Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/.next/static/development"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/types/package.json"), AnchoredSystemPathBuf("apps/web/.next/prerender-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks/polyfills.js"), AnchoredSystemPathBuf("apps/web/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/web/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/web/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/web/.next/types"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/trace"), AnchoredSystemPathBuf("apps/web/.next/server/interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/static"), AnchoredSystemPathBuf("apps/web/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/static/chunks"), AnchoredSystemPathBuf("apps/web/.next/server/middleware-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/routes-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server"), AnchoredSystemPathBuf("apps/web/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/web/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/web/.next/server/server-reference-manifest.js")}
2025-07-26T07:23:15.681625Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-26T07:23:15.695586Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
