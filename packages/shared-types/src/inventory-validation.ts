import { z } from 'zod';

// Base enums and schemas
export const CurrencySchema = z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']);
export const MovementTypeSchema = z.enum([
  'INBOUND', 'OUTBOUND', 'ADJUSTMENT', 'TRANSFER', 'RETURN', 'DAMAGED', 'EXPIRED'
]);
export const ReservationTypeSchema = z.enum([
  'ORDER', 'QUOTE', 'MANUAL', 'TRANSFER', 'QUALITY_HOLD'
]);
export const ReservationStatusSchema = z.enum([
  'ACTIVE', 'FULFILLED', 'CANCELLED', 'EXPIRED'
]);
export const AlertSeveritySchema = z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']);
export const AlertTypeSchema = z.enum([
  'LOW_STOCK', 'OUT_OF_STOCK', 'OVERSTOCK', 'EXPIRY_WARNING', 'QUALITY_ISSUE'
]);

// Inventory location validation schema
export const InventoryLocationSchema = z.object({
  locationName: z.string().min(1, 'Location name is required').max(100, 'Location name is too long'),
  locationCode: z.string().max(20, 'Location code is too long').optional(),
  locationAddress: z.string().max(500, 'Location address is too long').optional(),
  locationManager: z.string().max(100, 'Location manager name is too long').optional(),
  
  // Stock quantities
  stockQuantity: z.number().int().min(0, 'Stock quantity must be non-negative'),
  reservedQuantity: z.number().int().min(0, 'Reserved quantity must be non-negative').default(0),
  inTransitQuantity: z.number().int().min(0, 'In-transit quantity must be non-negative').default(0),
  
  // Thresholds
  reorderPoint: z.number().int().min(0, 'Reorder point must be non-negative').optional(),
  maxStockLevel: z.number().int().min(0, 'Max stock level must be non-negative').optional(),
  minStockLevel: z.number().int().min(0, 'Min stock level must be non-negative').optional(),
  safetyStock: z.number().int().min(0, 'Safety stock must be non-negative').optional(),
  
  // Cost tracking
  averageCost: z.number().min(0, 'Average cost must be non-negative').optional(),
  lastCost: z.number().min(0, 'Last cost must be non-negative').optional(),
  currency: CurrencySchema.default('USD'),
  
  // Lead times
  leadTime: z.number().int().min(0, 'Lead time must be non-negative').optional(),
  supplierLeadTime: z.number().int().min(0, 'Supplier lead time must be non-negative').optional(),
  
  // Settings
  isActive: z.boolean().default(true),
  allowBackorders: z.boolean().default(false),
  trackingEnabled: z.boolean().default(true),
}).refine(data => {
  // Min stock level should be <= reorder point <= max stock level
  if (data.minStockLevel && data.reorderPoint && data.minStockLevel > data.reorderPoint) {
    return false;
  }
  if (data.reorderPoint && data.maxStockLevel && data.reorderPoint > data.maxStockLevel) {
    return false;
  }
  if (data.minStockLevel && data.maxStockLevel && data.minStockLevel > data.maxStockLevel) {
    return false;
  }
  return true;
}, {
  message: "Stock levels must follow: min ≤ reorder point ≤ max",
}).refine(data => {
  // Safety stock should be reasonable compared to other levels
  if (data.safetyStock && data.reorderPoint && data.safetyStock > data.reorderPoint) {
    return false;
  }
  return true;
}, {
  message: "Safety stock should not exceed reorder point",
  path: ["safetyStock"],
});

// Stock movement validation schema
export const StockMovementSchema = z.object({
  movementType: MovementTypeSchema,
  quantity: z.number().int().positive('Quantity must be positive'),
  unitCost: z.number().min(0, 'Unit cost must be non-negative').optional(),
  totalCost: z.number().min(0, 'Total cost must be non-negative').optional(),
  currency: CurrencySchema.default('USD'),
  
  // Reference information
  referenceId: z.string().optional(), // Order ID, Transfer ID, etc.
  referenceType: z.enum(['ORDER', 'QUOTE', 'TRANSFER', 'ADJUSTMENT', 'RETURN', 'MANUAL']).optional(),
  
  // Batch and tracking
  batchNumber: z.string().max(50, 'Batch number is too long').optional(),
  serialNumber: z.string().max(50, 'Serial number is too long').optional(),
  expiryDate: z.string().datetime().optional(),
  
  // Additional details
  reason: z.string().max(500, 'Reason is too long').optional(),
  notes: z.string().max(1000, 'Notes are too long').optional(),
  
  // Location information
  fromLocationId: z.string().cuid().optional(),
  toLocationId: z.string().cuid().optional(),
  
  // Approval and tracking
  approvedBy: z.string().optional(),
  approvalDate: z.string().datetime().optional(),
  isApproved: z.boolean().default(false),
}).refine(data => {
  // For transfers, both from and to locations are required
  if (data.movementType === 'TRANSFER') {
    return data.fromLocationId && data.toLocationId && data.fromLocationId !== data.toLocationId;
  }
  return true;
}, {
  message: "Transfer movements require different from and to locations",
}).refine(data => {
  // If total cost is provided, unit cost should be consistent
  if (data.totalCost && data.unitCost && data.quantity) {
    const calculatedTotal = data.unitCost * data.quantity;
    const tolerance = 0.01; // Allow small rounding differences
    return Math.abs(data.totalCost - calculatedTotal) <= tolerance;
  }
  return true;
}, {
  message: "Total cost must equal unit cost × quantity",
  path: ["totalCost"],
});

// Inventory reservation validation schema
export const InventoryReservationSchema = z.object({
  reservationType: ReservationTypeSchema,
  quantity: z.number().int().positive('Quantity must be positive'),
  
  // Reference information
  referenceId: z.string().optional(), // Order ID, Quote ID, etc.
  referenceType: z.enum(['ORDER', 'QUOTE', 'MANUAL', 'TRANSFER', 'QUALITY_HOLD']).optional(),
  
  // Timing
  reservedUntil: z.string().datetime().optional(),
  expiryDate: z.string().datetime().optional(),
  
  // Status and tracking
  status: ReservationStatusSchema.default('ACTIVE'),
  priority: z.enum(['LOW', 'NORMAL', 'HIGH', 'URGENT']).default('NORMAL'),
  
  // Additional information
  reason: z.string().max(500, 'Reason is too long').optional(),
  notes: z.string().max(1000, 'Notes are too long').optional(),
  
  // Fulfillment tracking
  fulfilledQuantity: z.number().int().min(0, 'Fulfilled quantity must be non-negative').default(0),
  fulfilledAt: z.string().datetime().optional(),
  fulfilledBy: z.string().optional(),
  
  // Cancellation tracking
  cancelledAt: z.string().datetime().optional(),
  cancelledBy: z.string().optional(),
  cancellationReason: z.string().max(500, 'Cancellation reason is too long').optional(),
}).refine(data => {
  // Fulfilled quantity cannot exceed reserved quantity
  return data.fulfilledQuantity <= data.quantity;
}, {
  message: "Fulfilled quantity cannot exceed reserved quantity",
  path: ["fulfilledQuantity"],
}).refine(data => {
  // If status is FULFILLED, fulfilledQuantity should equal quantity
  if (data.status === 'FULFILLED') {
    return data.fulfilledQuantity === data.quantity;
  }
  return true;
}, {
  message: "Fulfilled reservations must have fulfilled quantity equal to reserved quantity",
  path: ["fulfilledQuantity"],
}).refine(data => {
  // Expiry date should be in the future for active reservations
  if (data.status === 'ACTIVE' && data.expiryDate) {
    return new Date(data.expiryDate) > new Date();
  }
  return true;
}, {
  message: "Expiry date must be in the future for active reservations",
  path: ["expiryDate"],
});

// Inventory alert validation schema
export const InventoryAlertSchema = z.object({
  alertType: AlertTypeSchema,
  severity: AlertSeveritySchema,
  message: z.string().min(1, 'Alert message is required').max(500, 'Alert message is too long'),
  
  // Threshold information
  currentValue: z.number().min(0, 'Current value must be non-negative'),
  thresholdValue: z.number().min(0, 'Threshold value must be non-negative').optional(),
  
  // Status and tracking
  isActive: z.boolean().default(true),
  isAcknowledged: z.boolean().default(false),
  acknowledgedAt: z.string().datetime().optional(),
  acknowledgedBy: z.string().optional(),
  
  // Resolution tracking
  isResolved: z.boolean().default(false),
  resolvedAt: z.string().datetime().optional(),
  resolvedBy: z.string().optional(),
  resolutionNotes: z.string().max(1000, 'Resolution notes are too long').optional(),
  
  // Additional context
  locationId: z.string().cuid().optional(),
  batchNumber: z.string().max(50, 'Batch number is too long').optional(),
  expiryDate: z.string().datetime().optional(),
  
  // Escalation
  escalationLevel: z.number().int().min(0).max(5, 'Escalation level must be 0-5').default(0),
  escalatedAt: z.string().datetime().optional(),
  escalatedTo: z.string().optional(),
}).refine(data => {
  // If acknowledged, acknowledgedAt and acknowledgedBy should be provided
  if (data.isAcknowledged) {
    return data.acknowledgedAt && data.acknowledgedBy;
  }
  return true;
}, {
  message: "Acknowledged alerts must have acknowledgment timestamp and user",
}).refine(data => {
  // If resolved, resolvedAt and resolvedBy should be provided
  if (data.isResolved) {
    return data.resolvedAt && data.resolvedBy;
  }
  return true;
}, {
  message: "Resolved alerts must have resolution timestamp and user",
});

// Inventory analytics validation schema
export const InventoryAnalyticsSchema = z.object({
  // Time period
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
  
  // Stock metrics
  averageStockLevel: z.number().min(0, 'Average stock level must be non-negative').optional(),
  stockTurnover: z.number().min(0, 'Stock turnover must be non-negative').optional(),
  daysOfInventory: z.number().min(0, 'Days of inventory must be non-negative').optional(),
  
  // Movement metrics
  totalInbound: z.number().int().min(0, 'Total inbound must be non-negative').optional(),
  totalOutbound: z.number().int().min(0, 'Total outbound must be non-negative').optional(),
  totalAdjustments: z.number().int().min(0, 'Total adjustments must be non-negative').optional(),
  
  // Cost metrics
  totalValue: z.number().min(0, 'Total value must be non-negative').optional(),
  averageCost: z.number().min(0, 'Average cost must be non-negative').optional(),
  costVariance: z.number().optional(), // Can be negative
  
  // Performance metrics
  stockoutEvents: z.number().int().min(0, 'Stockout events must be non-negative').optional(),
  overstockEvents: z.number().int().min(0, 'Overstock events must be non-negative').optional(),
  accuracyRate: z.number().min(0).max(100, 'Accuracy rate must be between 0-100%').optional(),
  
  // Alert metrics
  totalAlerts: z.number().int().min(0, 'Total alerts must be non-negative').optional(),
  criticalAlerts: z.number().int().min(0, 'Critical alerts must be non-negative').optional(),
  resolvedAlerts: z.number().int().min(0, 'Resolved alerts must be non-negative').optional(),
  
  // Efficiency metrics
  orderFulfillmentRate: z.number().min(0).max(100, 'Fulfillment rate must be between 0-100%').optional(),
  averageLeadTime: z.number().min(0, 'Average lead time must be non-negative').optional(),
  supplierPerformance: z.number().min(0).max(100, 'Supplier performance must be between 0-100%').optional(),
}).refine(data => {
  // End date should be after start date
  return new Date(data.endDate) >= new Date(data.startDate);
}, {
  message: "End date must be after or equal to start date",
  path: ["endDate"],
});

// Form validation schemas for UI components
export const LocationFormSchema = InventoryLocationSchema.pick({
  locationName: true,
  locationCode: true,
  locationAddress: true,
  locationManager: true,
  stockQuantity: true,
  reorderPoint: true,
  maxStockLevel: true,
  minStockLevel: true,
  safetyStock: true,
  averageCost: true,
  lastCost: true,
  leadTime: true,
  allowBackorders: true,
  trackingEnabled: true,
});

export const MovementFormSchema = StockMovementSchema.pick({
  movementType: true,
  quantity: true,
  unitCost: true,
  reason: true,
  notes: true,
  batchNumber: true,
  expiryDate: true,
  fromLocationId: true,
  toLocationId: true,
});

export const ReservationFormSchema = InventoryReservationSchema.pick({
  reservationType: true,
  quantity: true,
  referenceId: true,
  reservedUntil: true,
  priority: true,
  reason: true,
  notes: true,
});

// Type exports
export type InventoryLocation = z.infer<typeof InventoryLocationSchema>;
export type StockMovement = z.infer<typeof StockMovementSchema>;
export type InventoryReservation = z.infer<typeof InventoryReservationSchema>;
export type InventoryAlert = z.infer<typeof InventoryAlertSchema>;
export type InventoryAnalytics = z.infer<typeof InventoryAnalyticsSchema>;
export type LocationForm = z.infer<typeof LocationFormSchema>;
export type MovementForm = z.infer<typeof MovementFormSchema>;
export type ReservationForm = z.infer<typeof ReservationFormSchema>;

// Validation helper functions
export function validateStockLevels(min?: number, reorder?: number, max?: number): boolean {
  if (min !== undefined && reorder !== undefined && min > reorder) return false;
  if (reorder !== undefined && max !== undefined && reorder > max) return false;
  if (min !== undefined && max !== undefined && min > max) return false;
  return true;
}

export function validateMovementQuantity(
  movementType: string,
  quantity: number,
  currentStock: number
): boolean {
  if (movementType === 'OUTBOUND' && quantity > currentStock) {
    return false; // Cannot remove more than available
  }
  return true;
}

export function validateReservationQuantity(
  quantity: number,
  availableStock: number,
  existingReservations: number
): boolean {
  return quantity <= (availableStock - existingReservations);
}

export function calculateAvailableStock(
  totalStock: number,
  reservedQuantity: number,
  inTransitQuantity: number = 0
): number {
  return Math.max(0, totalStock - reservedQuantity);
}

export function isStockLevelCritical(
  currentStock: number,
  reorderPoint?: number,
  minStockLevel?: number
): boolean {
  if (reorderPoint && currentStock <= reorderPoint) return true;
  if (minStockLevel && currentStock <= minStockLevel) return true;
  return false;
}
