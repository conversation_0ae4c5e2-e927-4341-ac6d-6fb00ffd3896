import { z } from 'zod';

// Base validation schemas
export const CurrencySchema = z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']);
export const ProductStatusSchema = z.enum(['DRAFT', 'ACTIVE', 'INACTIVE', 'OUT_OF_STOCK', 'DISCONTINUED']);
export const StockStatusSchema = z.enum(['IN_STOCK', 'LOW_STOCK', 'OUT_OF_STOCK', 'DISCONTINUED']);

// Dimensions validation schema
export const DimensionsSchema = z.object({
  length: z.number().positive('Length must be positive'),
  width: z.number().positive('Width must be positive'),
  height: z.number().positive('Height must be positive'),
  unit: z.enum(['cm', 'inch', 'mm', 'm']),
  weightUnit: z.enum(['kg', 'lb', 'g', 'oz']).optional(),
}).refine(data => data.length > 0 && data.width > 0 && data.height > 0, {
  message: "All dimensions must be greater than 0",
});

// Manufacturing specifications validation
export const SpecificationsSchema = z.object({
  manufacturingTime: z.number().int().positive('Manufacturing time must be positive').optional(),
  productionCapacity: z.number().int().positive('Production capacity must be positive').optional(),
  leadTime: z.number().int().positive('Lead time must be positive').optional(),
  customizationOptions: z.array(z.string().min(1)).optional(),
  materialComposition: z.string().min(1, 'Material composition is required').optional(),
  technicalDataSheet: z.string().url('Must be a valid URL').optional(),
  userManual: z.string().url('Must be a valid URL').optional(),
  warrantyTerms: z.string().min(1).optional(),
  dimensions: DimensionsSchema.optional(),
  weight: z.number().positive('Weight must be positive').optional(),
  weightUnit: z.enum(['kg', 'lb', 'g', 'oz']).optional(),
}).refine(data => {
  // If weight is provided, weightUnit must be provided
  if (data.weight && !data.weightUnit) {
    return false;
  }
  return true;
}, {
  message: "Weight unit is required when weight is specified",
  path: ["weightUnit"],
});

// Compliance validation schema
export const ComplianceSchema = z.object({
  certifications: z.array(z.object({
    name: z.string().min(1, 'Certification name is required'),
    issuingBody: z.string().min(1, 'Issuing body is required'),
    certificateNumber: z.string().optional(),
    issueDate: z.string().datetime().optional(),
    expiryDate: z.string().datetime().optional(),
    documentUrl: z.string().url('Must be a valid URL').optional(),
    status: z.enum(['ACTIVE', 'EXPIRED', 'PENDING', 'REVOKED']).default('ACTIVE'),
    version: z.string().optional(),
    complianceDate: z.string().datetime().optional(),
    testingLab: z.string().optional(),
    reportNumber: z.string().optional(),
    reportUrl: z.string().url('Must be a valid URL').optional(),
  })).optional(),
  exportRestrictions: z.array(z.object({
    country: z.string().length(2, 'Must be ISO country code'),
    restriction: z.string().min(1, 'Restriction description is required'),
    restrictionType: z.enum(['PROHIBITED', 'RESTRICTED', 'LICENSE_REQUIRED', 'QUOTA']),
    licenseRequired: z.boolean().default(false),
    notes: z.string().optional(),
  })).optional(),
  hsCode: z.string().regex(/^\d{6,10}$/, 'HS Code must be 6-10 digits').optional(),
  countryOfOrigin: z.string().length(2, 'Must be ISO country code').optional(),
  exportLicense: z.object({
    required: z.boolean(),
    licenseNumber: z.string().optional(),
    issuingAuthority: z.string().optional(),
    validFrom: z.string().datetime().optional(),
    validUntil: z.string().datetime().optional(),
    documentUrl: z.string().url('Must be a valid URL').optional(),
  }).optional(),
  restrictedCountries: z.array(z.string().length(2, 'Must be ISO country code')).optional(),
  tariffClassification: z.object({
    code: z.string().min(1, 'Tariff code is required'),
    description: z.string().optional(),
    dutyRate: z.number().min(0).max(100, 'Duty rate must be between 0-100%').optional(),
    preferentialTreatment: z.boolean().default(false),
  }).optional(),
  regulatoryApprovals: z.array(z.object({
    agency: z.string().min(1, 'Agency name is required'),
    approvalType: z.string().min(1, 'Approval type is required'),
    approvalNumber: z.string().optional(),
    approvalDate: z.string().datetime().optional(),
    expiryDate: z.string().datetime().optional(),
    documentUrl: z.string().url('Must be a valid URL').optional(),
    status: z.enum(['ACTIVE', 'EXPIRED', 'PENDING', 'REVOKED']).default('ACTIVE'),
  })).optional(),
}).refine(data => {
  // If export license is required, license number should be provided
  if (data.exportLicense?.required && !data.exportLicense.licenseNumber) {
    return false;
  }
  return true;
}, {
  message: "License number is required when export license is required",
  path: ["exportLicense", "licenseNumber"],
});

// Quality data validation schema
export const QualityDataSchema = z.object({
  qualityGrade: z.enum(['A', 'B', 'C']).optional(),
  defectRate: z.number().min(0).max(100, 'Defect rate must be between 0-100%').optional(),
  testingProtocols: z.array(z.object({
    name: z.string().min(1, 'Protocol name is required'),
    description: z.string().optional(),
    frequency: z.enum(['BATCH', 'DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY']),
    lastTested: z.string().datetime().optional(),
    nextTestDue: z.string().datetime().optional(),
    testingLab: z.string().optional(),
    certificateUrl: z.string().url('Must be a valid URL').optional(),
  })).optional(),
  supplierInfo: z.object({
    supplierId: z.string().cuid().optional(),
    supplierName: z.string().min(1, 'Supplier name is required'),
    supplierRating: z.number().min(1).max(5, 'Rating must be between 1-5').optional(),
    certificationLevel: z.enum(['BASIC', 'STANDARD', 'PREMIUM', 'ENTERPRISE']).optional(),
    lastAuditDate: z.string().datetime().optional(),
    nextAuditDue: z.string().datetime().optional(),
    contactInfo: z.object({
      email: z.string().email('Must be a valid email').optional(),
      phone: z.string().optional(),
      address: z.string().optional(),
    }).optional(),
  }).optional(),
  qualityMetrics: z.object({
    passRate: z.number().min(0).max(100, 'Pass rate must be between 0-100%').optional(),
    returnRate: z.number().min(0).max(100, 'Return rate must be between 0-100%').optional(),
    customerSatisfaction: z.number().min(1).max(5, 'Satisfaction must be between 1-5').optional(),
    averageRating: z.number().min(1).max(5, 'Rating must be between 1-5').optional(),
    totalReviews: z.number().int().min(0, 'Total reviews must be non-negative').optional(),
  }).optional(),
});

// Manufacturing data validation schema
export const ManufacturingDataSchema = z.object({
  productionCapacity: z.object({
    daily: z.number().int().positive('Daily capacity must be positive').optional(),
    monthly: z.number().int().positive('Monthly capacity must be positive').optional(),
    annual: z.number().int().positive('Annual capacity must be positive').optional(),
    unit: z.string().min(1, 'Capacity unit is required').optional(),
  }).optional(),
  leadTimes: z.object({
    standard: z.number().int().positive('Standard lead time must be positive').optional(),
    rush: z.number().int().positive('Rush lead time must be positive').optional(),
    custom: z.number().int().positive('Custom lead time must be positive').optional(),
    unit: z.enum(['days', 'weeks', 'months']).default('days'),
  }).optional(),
  manufacturingProcess: z.array(z.object({
    step: z.number().int().positive('Step number must be positive'),
    name: z.string().min(1, 'Process step name is required'),
    description: z.string().optional(),
    duration: z.number().positive('Duration must be positive').optional(),
    durationUnit: z.enum(['minutes', 'hours', 'days']).optional(),
    equipment: z.array(z.string()).optional(),
    skillLevel: z.enum(['BASIC', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
  })).optional(),
  qualityCheckpoints: z.array(z.object({
    checkpoint: z.string().min(1, 'Checkpoint name is required'),
    stage: z.enum(['RAW_MATERIAL', 'IN_PROCESS', 'FINAL_INSPECTION', 'PACKAGING']),
    criteria: z.string().min(1, 'Quality criteria is required'),
    tolerance: z.string().optional(),
    testMethod: z.string().optional(),
    frequency: z.enum(['EVERY_UNIT', 'SAMPLE', 'BATCH', 'PERIODIC']),
  })).optional(),
  equipmentRequired: z.array(z.object({
    name: z.string().min(1, 'Equipment name is required'),
    type: z.string().optional(),
    specifications: z.string().optional(),
    quantity: z.number().int().positive('Quantity must be positive').optional(),
    isRequired: z.boolean().default(true),
  })).optional(),
});

// Trading data validation schema
export const TradingDataSchema = z.object({
  priceBreaks: z.array(z.object({
    minQuantity: z.number().int().positive('Minimum quantity must be positive'),
    maxQuantity: z.number().int().positive('Maximum quantity must be positive').optional(),
    unitPrice: z.number().positive('Unit price must be positive'),
    currency: CurrencySchema,
    validFrom: z.string().datetime().optional(),
    validUntil: z.string().datetime().optional(),
  })).optional(),
  regionalPricing: z.array(z.object({
    region: z.string().min(1, 'Region is required'),
    countries: z.array(z.string().length(2, 'Must be ISO country code')),
    priceMultiplier: z.number().positive('Price multiplier must be positive'),
    currency: CurrencySchema,
    minimumOrder: z.number().int().positive('Minimum order must be positive').optional(),
    shippingCost: z.number().min(0, 'Shipping cost must be non-negative').optional(),
    taxRate: z.number().min(0).max(100, 'Tax rate must be between 0-100%').optional(),
  })).optional(),
  seasonalAdjustments: z.array(z.object({
    season: z.string().min(1, 'Season name is required'),
    startDate: z.string().datetime(),
    endDate: z.string().datetime(),
    priceAdjustment: z.number(), // Can be negative for discounts
    adjustmentType: z.enum(['PERCENTAGE', 'FIXED_AMOUNT']),
    description: z.string().optional(),
  })).optional(),
  costBreakdown: z.object({
    materialCost: z.number().min(0, 'Material cost must be non-negative').optional(),
    laborCost: z.number().min(0, 'Labor cost must be non-negative').optional(),
    overheadCost: z.number().min(0, 'Overhead cost must be non-negative').optional(),
    shippingCost: z.number().min(0, 'Shipping cost must be non-negative').optional(),
    profitMargin: z.number().min(0).max(100, 'Profit margin must be between 0-100%').optional(),
    currency: CurrencySchema,
    lastUpdated: z.string().datetime().optional(),
  }).optional(),
});

// Marketing data validation schema
export const MarketingDataSchema = z.object({
  seoData: z.object({
    metaTitle: z.string().max(60, 'Meta title should be under 60 characters').optional(),
    metaDescription: z.string().max(160, 'Meta description should be under 160 characters').optional(),
    keywords: z.array(z.string().min(1)).optional(),
    slug: z.string().regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, 'Slug must be URL-friendly').optional(),
    canonicalUrl: z.string().url('Must be a valid URL').optional(),
  }).optional(),
  positioning: z.object({
    targetMarket: z.array(z.string().min(1)).optional(),
    competitiveAdvantages: z.array(z.string().min(1)).optional(),
    valueProposition: z.string().min(1, 'Value proposition is required').optional(),
    brandPosition: z.enum(['PREMIUM', 'MID_RANGE', 'BUDGET', 'LUXURY']).optional(),
    marketSegment: z.array(z.string().min(1)).optional(),
  }).optional(),
  promotions: z.array(z.object({
    name: z.string().min(1, 'Promotion name is required'),
    type: z.enum(['DISCOUNT', 'BUNDLE', 'FREE_SHIPPING', 'BOGO', 'SEASONAL']),
    discountPercentage: z.number().min(0).max(100, 'Discount must be between 0-100%').optional(),
    discountAmount: z.number().min(0, 'Discount amount must be non-negative').optional(),
    startDate: z.string().datetime(),
    endDate: z.string().datetime(),
    conditions: z.string().optional(),
    isActive: z.boolean().default(true),
  })).optional(),
  analytics: z.object({
    viewCount: z.number().int().min(0, 'View count must be non-negative').optional(),
    inquiryCount: z.number().int().min(0, 'Inquiry count must be non-negative').optional(),
    conversionRate: z.number().min(0).max(100, 'Conversion rate must be between 0-100%').optional(),
    averageRating: z.number().min(1).max(5, 'Rating must be between 1-5').optional(),
    totalReviews: z.number().int().min(0, 'Total reviews must be non-negative').optional(),
    lastUpdated: z.string().datetime().optional(),
  }).optional(),
});

// Basic product information validation
export const BasicProductInfoSchema = z.object({
  name: z.string().min(1, 'Product name is required').max(200, 'Product name is too long'),
  description: z.string().optional(),
  shortDescription: z.string().max(500, 'Short description is too long').optional(),
  sku: z.string().optional(),
  model: z.string().optional(),
  brand: z.string().optional(),
  categoryId: z.string().cuid('Invalid category ID'),
  status: ProductStatusSchema.default('DRAFT'),
  isFeatured: z.boolean().default(false),
});

// Pricing and inventory validation
export const PricingInventorySchema = z.object({
  basePrice: z.number().positive('Base price must be positive'),
  currency: CurrencySchema.default('USD'),
  minOrderQty: z.number().int().positive('Minimum order quantity must be positive').default(1),
  maxOrderQty: z.number().int().positive('Maximum order quantity must be positive').optional(),
  stockQuantity: z.number().int().min(0, 'Stock quantity must be non-negative').default(0),
  stockStatus: StockStatusSchema.default('IN_STOCK'),
}).refine(data => {
  // If maxOrderQty is provided, it should be >= minOrderQty
  if (data.maxOrderQty && data.maxOrderQty < data.minOrderQty) {
    return false;
  }
  return true;
}, {
  message: "Maximum order quantity must be greater than or equal to minimum order quantity",
  path: ["maxOrderQty"],
});

// Complete product validation schema
export const ProductValidationSchema = z.object({
  // Basic information
  ...BasicProductInfoSchema.shape,
  
  // Pricing and inventory
  ...PricingInventorySchema.shape,
  
  // Enterprise fields
  specifications: SpecificationsSchema.optional(),
  compliance: ComplianceSchema.optional(),
  qualityData: QualityDataSchema.optional(),
  manufacturingData: ManufacturingDataSchema.optional(),
  tradingData: TradingDataSchema.optional(),
  marketingData: MarketingDataSchema.optional(),
  
  // Additional fields
  materials: z.array(z.string().min(1)).default([]),
  colors: z.array(z.string().min(1)).default([]),
  tags: z.array(z.string().min(1)).default([]),
  weight: z.number().positive('Weight must be positive').optional(),
  dimensions: DimensionsSchema.optional(),
});

// Type exports
export type ProductValidation = z.infer<typeof ProductValidationSchema>;
export type BasicProductInfo = z.infer<typeof BasicProductInfoSchema>;
export type PricingInventory = z.infer<typeof PricingInventorySchema>;
export type Specifications = z.infer<typeof SpecificationsSchema>;
export type Compliance = z.infer<typeof ComplianceSchema>;
export type QualityData = z.infer<typeof QualityDataSchema>;
export type ManufacturingData = z.infer<typeof ManufacturingDataSchema>;
export type TradingData = z.infer<typeof TradingDataSchema>;
export type MarketingData = z.infer<typeof MarketingDataSchema>;
