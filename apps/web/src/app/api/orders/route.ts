import { NextRequest, NextResponse } from 'next/server';
import { prisma, ensurePrismaConnection, withDatabaseRetry } from '../../../lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Ensure database connection
    await ensurePrismaConnection();

    const { searchParams } = new URL(request.url);
    const factoryId = searchParams.get('factoryId');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search');

    if (!factoryId) {
      return NextResponse.json(
        { error: 'Factory ID is required' },
        { status: 400 }
      );
    }

    const skip = (page - 1) * limit;

    // Build where clause
    const whereClause: any = {
      factoryId,
    };

    if (status) {
      whereClause.status = status;
    }

    if (search) {
      whereClause.OR = [
        { orderNumber: { contains: search, mode: 'insensitive' } },
        { customerName: { contains: search, mode: 'insensitive' } },
        { customerEmail: { contains: search, mode: 'insensitive' } },
        { customerCompany: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Use retry wrapper for database operations
    const result = await withDatabaseRetry(async () => {
      // Get orders with related data
      const [orders, total] = await Promise.all([
        prisma.order.findMany({
          where: whereClause,
          include: {
            items: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    images: {
                      where: { isMain: true },
                      take: 1,
                    },
                  }
                },
                variant: {
                  select: {
                    id: true,
                    name: true,
                    attributes: true,
                  }
                }
              }
            },
            factory: {
              select: {
                id: true,
                name: true,
                slug: true,
              }
            },
            assignedTo: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              }
            }
          },
          orderBy: { orderDate: 'desc' },
          skip,
          take: limit,
        }),
        prisma.order.count({ where: whereClause })
      ]);

      return { orders, total };
    }, 'Orders retrieval');

    // Transform orders for frontend
    const transformedOrders = result.orders.map(order => ({
      id: order.id,
      orderNumber: order.orderNumber,
      status: order.status,
      totalAmount: Number(order.totalAmount),
      currency: order.currency,
      customerName: order.customerName,
      customerEmail: order.customerEmail,
      customerPhone: order.customerPhone,
      customerCompany: order.customerCompany,
      orderDate: order.orderDate.toISOString(),
      requiredDate: order.requiredDate?.toISOString(),
      shippedDate: order.shippedDate?.toISOString(),
      deliveredDate: order.deliveredDate?.toISOString(),
      itemCount: order.items.length,
      items: order.items.map(item => ({
        id: item.id,
        quantity: item.quantity,
        unitPrice: Number(item.unitPrice),
        totalPrice: Number(item.totalPrice),
        product: {
          ...item.product,
          mainImage: item.product.images[0]?.url || null,
        },
        variant: item.variant,
        specifications: item.specifications,
      })),
      factory: order.factory,
      assignedTo: order.assignedTo,
      createdAt: order.createdAt.toISOString(),
    }));

    console.log('✅ Orders retrieved successfully:', {
      factoryId,
      count: transformedOrders.length,
      total: result.total
    });

    return NextResponse.json({
      data: transformedOrders,
      pagination: {
        page,
        limit,
        total: result.total,
        totalPages: Math.ceil(result.total / limit),
        hasNext: page * limit < result.total,
        hasPrev: page > 1,
      }
    });

  } catch (error) {
    console.error('❌ Error retrieving orders:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve orders' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Ensure database connection
    await ensurePrismaConnection();

    const body = await request.json();
    
    console.log('🔄 Creating order:', {
      customerName: body.customerName,
      factoryId: body.factoryId,
      itemsCount: body.items?.length || 0,
    });

    // Validate required fields
    if (!body.customerName || !body.customerEmail || !body.factoryId || !body.items || body.items.length === 0) {
      return NextResponse.json(
        { error: 'Missing required fields: customerName, customerEmail, factoryId, items' },
        { status: 400 }
      );
    }

    // Verify factory exists and is active
    const factory = await prisma.factory.findFirst({
      where: {
        id: body.factoryId,
        status: 'ACTIVE',
      },
    });

    if (!factory) {
      return NextResponse.json(
        { error: 'Factory not found or inactive' },
        { status: 404 }
      );
    }

    // Verify all products belong to the factory
    const productIds = body.items.map((item: any) => item.productId);
    const products = await prisma.product.findMany({
      where: {
        id: { in: productIds },
        factoryId: body.factoryId,
        isActive: true,
      },
    });

    if (products.length !== productIds.length) {
      return NextResponse.json(
        { error: 'Some products are not available' },
        { status: 400 }
      );
    }

    // Calculate total amount
    const totalAmount = body.items.reduce((sum: number, item: any) => sum + (item.unitPrice * item.quantity), 0);

    // Generate order number
    const orderNumber = `ORD-${Date.now()}-${Math.random().toString(36).substring(7).toUpperCase()}`;

    // Use retry wrapper for database operations
    const order = await withDatabaseRetry(async () => {
      return await prisma.order.create({
        data: {
          orderNumber,
          status: 'PENDING',
          totalAmount,
          currency: body.currency || 'USD',
          customerName: body.customerName,
          customerEmail: body.customerEmail,
          customerPhone: body.customerPhone || null,
          customerCompany: body.customerCompany || null,
          shippingStreet: body.shippingAddress?.street || null,
          shippingCity: body.shippingAddress?.city || null,
          shippingState: body.shippingAddress?.state || null,
          shippingPostalCode: body.shippingAddress?.postalCode || null,
          shippingCountry: body.shippingAddress?.country || null,
          orderDate: new Date(),
          requiredDate: body.requiredDate ? new Date(body.requiredDate) : null,
          notes: body.notes || null,
          factoryId: body.factoryId,
          items: {
            create: body.items.map((item: any) => ({
              productId: item.productId,
              variantId: item.variantId || null,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              totalPrice: item.unitPrice * item.quantity,
              specifications: item.specifications || null,
            }))
          }
        },
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                }
              },
              variant: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                }
              }
            }
          },
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
            }
          }
        }
      });
    }, 'Order creation');

    console.log('✅ Order created successfully:', {
      id: order.id,
      orderNumber: order.orderNumber,
      total: order.totalAmount,
      itemsCount: order.items.length,
    });

    return NextResponse.json({
      id: order.id,
      orderNumber: order.orderNumber,
      status: order.status,
      totalAmount: Number(order.totalAmount),
      currency: order.currency,
      items: order.items,
      factory: order.factory,
      createdAt: order.createdAt.toISOString(),
    }, { status: 201 });

  } catch (error) {
    console.error('❌ Error creating order:', error);
    return NextResponse.json(
      { error: 'Failed to create order' },
      { status: 500 }
    );
  }
}
