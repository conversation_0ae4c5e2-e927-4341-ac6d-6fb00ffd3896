import { NextRequest, NextResponse } from 'next/server';
import { prisma, ensurePrismaConnection, withDatabaseRetry } from '../../../../lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Ensure database connection
    await ensurePrismaConnection();

    const { searchParams } = new URL(request.url);
    const factoryId = searchParams.get('factoryId');

    if (!factoryId) {
      return NextResponse.json(
        { error: 'Factory ID is required' },
        { status: 400 }
      );
    }

    console.log('🔍 Retrieving order:', {
      orderId: params.id,
      factoryId
    });

    // Use retry wrapper for database operations
    const order = await withDatabaseRetry(async () => {
      return await prisma.order.findFirst({
        where: {
          id: params.id,
          factoryId,
        },
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  images: {
                    where: { isMain: true },
                    take: 1,
                  },
                }
              },
              variant: {
                select: {
                  id: true,
                  name: true,
                  attributes: true,
                }
              }
            }
          },
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
            }
          },
          assignedTo: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            }
          }
        }
      });
    }, 'Order retrieval');

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Transform order for frontend
    const transformedOrder = {
      id: order.id,
      orderNumber: order.orderNumber,
      status: order.status,
      totalAmount: Number(order.totalAmount),
      currency: order.currency,
      customerName: order.customerName,
      customerEmail: order.customerEmail,
      customerPhone: order.customerPhone,
      customerCompany: order.customerCompany,
      shippingAddress: {
        street: order.shippingStreet || '',
        city: order.shippingCity || '',
        state: order.shippingState || '',
        postalCode: order.shippingPostalCode || '',
        country: order.shippingCountry || '',
      },
      orderDate: order.orderDate.toISOString(),
      requiredDate: order.requiredDate?.toISOString(),
      shippedDate: order.shippedDate?.toISOString(),
      deliveredDate: order.deliveredDate?.toISOString(),
      trackingNumber: order.trackingNumber,
      items: order.items.map(item => ({
        id: item.id,
        quantity: item.quantity,
        unitPrice: Number(item.unitPrice),
        totalPrice: Number(item.totalPrice),
        product: {
          ...item.product,
          mainImage: item.product.images[0]?.url || null,
        },
        variant: item.variant,
        specifications: item.specifications,
      })),
      factory: order.factory,
      assignedTo: order.assignedTo,
      notes: order.notes,
      createdAt: order.createdAt.toISOString(),
      updatedAt: order.updatedAt.toISOString(),
    };

    console.log('✅ Order retrieved successfully:', {
      orderId: order.id,
      orderNumber: order.orderNumber,
    });

    return NextResponse.json(transformedOrder);

  } catch (error) {
    console.error('❌ Error retrieving order:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve order' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Ensure database connection
    await ensurePrismaConnection();

    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const factoryId = searchParams.get('factoryId');

    if (!factoryId) {
      return NextResponse.json(
        { error: 'Factory ID is required' },
        { status: 400 }
      );
    }

    console.log('🔄 Updating order:', {
      orderId: params.id,
      factoryId,
      status: body.status
    });

    // Verify order exists and belongs to factory
    const existingOrder = await prisma.order.findFirst({
      where: {
        id: params.id,
        factoryId,
      },
    });

    if (!existingOrder) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {};

    if (body.status) updateData.status = body.status;
    if (body.trackingNumber !== undefined) updateData.trackingNumber = body.trackingNumber;
    if (body.notes !== undefined) updateData.notes = body.notes;
    if (body.assignedToId !== undefined) updateData.assignedToId = body.assignedToId;
    if (body.requiredDate !== undefined) {
      updateData.requiredDate = body.requiredDate ? new Date(body.requiredDate) : null;
    }

    // Set status-specific dates
    if (body.status === 'SHIPPED' && !existingOrder.shippedDate) {
      updateData.shippedDate = new Date();
    }
    if (body.status === 'DELIVERED' && !existingOrder.deliveredDate) {
      updateData.deliveredDate = new Date();
    }

    // Use retry wrapper for database operations
    const updatedOrder = await withDatabaseRetry(async () => {
      return await prisma.order.update({
        where: { id: params.id },
        data: updateData,
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  images: {
                    where: { isMain: true },
                    take: 1,
                  },
                }
              },
              variant: {
                select: {
                  id: true,
                  name: true,
                  attributes: true,
                }
              }
            }
          },
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
            }
          },
          assignedTo: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            }
          }
        }
      });
    }, 'Order update');

    console.log('✅ Order updated successfully:', {
      orderId: updatedOrder.id,
      orderNumber: updatedOrder.orderNumber,
      status: updatedOrder.status,
    });

    // Transform order for frontend
    const transformedOrder = {
      id: updatedOrder.id,
      orderNumber: updatedOrder.orderNumber,
      status: updatedOrder.status,
      totalAmount: Number(updatedOrder.totalAmount),
      currency: updatedOrder.currency,
      customerName: updatedOrder.customerName,
      customerEmail: updatedOrder.customerEmail,
      customerPhone: updatedOrder.customerPhone,
      customerCompany: updatedOrder.customerCompany,
      shippingAddress: {
        street: updatedOrder.shippingStreet || '',
        city: updatedOrder.shippingCity || '',
        state: updatedOrder.shippingState || '',
        postalCode: updatedOrder.shippingPostalCode || '',
        country: updatedOrder.shippingCountry || '',
      },
      orderDate: updatedOrder.orderDate.toISOString(),
      requiredDate: updatedOrder.requiredDate?.toISOString(),
      shippedDate: updatedOrder.shippedDate?.toISOString(),
      deliveredDate: updatedOrder.deliveredDate?.toISOString(),
      trackingNumber: updatedOrder.trackingNumber,
      items: updatedOrder.items.map(item => ({
        id: item.id,
        quantity: item.quantity,
        unitPrice: Number(item.unitPrice),
        totalPrice: Number(item.totalPrice),
        product: {
          ...item.product,
          mainImage: item.product.images[0]?.url || null,
        },
        variant: item.variant,
        specifications: item.specifications,
      })),
      factory: updatedOrder.factory,
      assignedTo: updatedOrder.assignedTo,
      notes: updatedOrder.notes,
      createdAt: updatedOrder.createdAt.toISOString(),
      updatedAt: updatedOrder.updatedAt.toISOString(),
    };

    return NextResponse.json(transformedOrder);

  } catch (error) {
    console.error('❌ Error updating order:', error);
    return NextResponse.json(
      { error: 'Failed to update order' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Ensure database connection
    await ensurePrismaConnection();

    const { searchParams } = new URL(request.url);
    const factoryId = searchParams.get('factoryId');

    if (!factoryId) {
      return NextResponse.json(
        { error: 'Factory ID is required' },
        { status: 400 }
      );
    }

    console.log('🗑️ Deleting order:', {
      orderId: params.id,
      factoryId
    });

    // Verify order exists and belongs to factory
    const existingOrder = await prisma.order.findFirst({
      where: {
        id: params.id,
        factoryId,
      },
    });

    if (!existingOrder) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Check if order can be deleted (only pending orders)
    if (existingOrder.status !== 'PENDING') {
      return NextResponse.json(
        { error: 'Only pending orders can be deleted' },
        { status: 400 }
      );
    }

    // Use retry wrapper for database operations
    await withDatabaseRetry(async () => {
      // Delete order items first (cascade should handle this, but being explicit)
      await prisma.orderItem.deleteMany({
        where: { orderId: params.id }
      });

      // Delete the order
      await prisma.order.delete({
        where: { id: params.id }
      });
    }, 'Order deletion');

    console.log('✅ Order deleted successfully:', {
      orderId: params.id,
    });

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('❌ Error deleting order:', error);
    return NextResponse.json(
      { error: 'Failed to delete order' },
      { status: 500 }
    );
  }
}
