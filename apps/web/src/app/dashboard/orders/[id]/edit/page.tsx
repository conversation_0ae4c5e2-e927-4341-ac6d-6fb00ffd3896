'use client';

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '../../../../../contexts/auth-context';
import { ProtectedRoute } from '../../../../../components/auth/protected-route';
import { DashboardSidebar } from '../../../../../components/dashboard/dashboard-sidebar';
import { Button } from '../../../../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../../../../components/ui/card';
import { Input } from '../../../../../components/ui/input';
import { Label } from '../../../../../components/ui/label';
import { Textarea } from '../../../../../components/ui/textarea';
import { Badge } from '../../../../../components/ui/badge';
import { 
  ArrowLeft,
  Save,
  Package,
  User,
  MapPin,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
  Truck
} from 'lucide-react';

interface OrderDetail {
  id: string;
  orderNumber: string;
  status: 'PENDING' | 'CONFIRMED' | 'IN_PRODUCTION' | 'READY_TO_SHIP' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED' | 'REFUNDED';
  totalAmount: number;
  currency: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  customerCompany?: string;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  orderDate: string;
  requiredDate?: string;
  shippedDate?: string;
  deliveredDate?: string;
  trackingNumber?: string;
  notes?: string;
  assignedTo?: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

export default function EditOrderPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const [order, setOrder] = useState<OrderDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const orderId = params.id as string;

  // Form state
  const [formData, setFormData] = useState({
    status: '',
    trackingNumber: '',
    notes: '',
    requiredDate: '',
  });

  useEffect(() => {
    const loadOrder = async () => {
      if (!user?.factoryId || !orderId) return;

      setLoading(true);
      try {
        const response = await fetch(`/api/orders/${orderId}?factoryId=${user.factoryId}`);
        if (response.ok) {
          const orderData = await response.json();
          setOrder(orderData);
          
          // Initialize form data
          setFormData({
            status: orderData.status,
            trackingNumber: orderData.trackingNumber || '',
            notes: orderData.notes || '',
            requiredDate: orderData.requiredDate ? orderData.requiredDate.split('T')[0] : '',
          });
          
          console.log('✅ Order loaded for editing:', orderData.orderNumber);
        } else if (response.status === 404) {
          setError('Order not found.');
        } else {
          setError('Failed to load order details.');
        }
      } catch (error) {
        console.error('❌ Error loading order:', error);
        setError('Failed to load order details.');
      } finally {
        setLoading(false);
      }
    };

    loadOrder();
  }, [user?.factoryId, orderId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear messages when user starts typing
    if (error) setError(null);
    if (success) setSuccess(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.factoryId || !order) {
      setError('No factory ID or order found. Please refresh the page.');
      return;
    }

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('🔄 Updating order:', {
        orderId: order.id,
        status: formData.status,
      });

      const response = await fetch(`/api/orders/${order.id}?factoryId=${user.factoryId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: formData.status,
          trackingNumber: formData.trackingNumber || null,
          notes: formData.notes || null,
          requiredDate: formData.requiredDate || null,
        }),
      });

      if (response.ok) {
        const updatedOrder = await response.json();
        console.log('✅ Order updated successfully:', updatedOrder.orderNumber);
        setSuccess('Order updated successfully!');
        setOrder(updatedOrder);

        // Redirect after a short delay
        setTimeout(() => {
          router.push(`/dashboard/orders/${order.id}`);
        }, 1500);
      } else {
        const errorData = await response.json();
        console.error('❌ Order update failed:', response.status, errorData);
        setError(`Failed to update order: ${errorData.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('❌ Order update error:', error);
      setError('Failed to update order. Please check your connection and try again.');
    } finally {
      setSaving(false);
    }
  };

  const getStatusBadge = (status: OrderDetail['status']) => {
    const statusConfig = {
      PENDING: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      CONFIRMED: { color: 'bg-blue-100 text-blue-800', icon: CheckCircle },
      IN_PRODUCTION: { color: 'bg-purple-100 text-purple-800', icon: Package },
      READY_TO_SHIP: { color: 'bg-orange-100 text-orange-800', icon: Package },
      SHIPPED: { color: 'bg-indigo-100 text-indigo-800', icon: Truck },
      DELIVERED: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      CANCELLED: { color: 'bg-red-100 text-red-800', icon: AlertCircle },
      REFUNDED: { color: 'bg-gray-100 text-gray-800', icon: AlertCircle },
    };

    const config = statusConfig[status];
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="w-3 h-3" />
        {status.replace('_', ' ')}
      </Badge>
    );
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="flex h-screen bg-gray-50">
          <DashboardSidebar />
          <div className="flex-1 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (error && !order) {
    return (
      <ProtectedRoute>
        <div className="flex h-screen bg-gray-50">
          <DashboardSidebar />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <AlertCircle className="mx-auto h-12 w-12 text-red-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Error</h3>
              <p className="mt-1 text-sm text-gray-500">{error}</p>
              <div className="mt-6">
                <Button onClick={() => router.back()}>
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Go Back
                </Button>
              </div>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50">
        <DashboardSidebar />
        
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <div className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button variant="ghost" onClick={() => router.back()}>
                  <ArrowLeft className="w-4 h-4" />
                </Button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Edit Order</h1>
                  <p className="text-gray-600 mt-1">{order?.orderNumber}</p>
                </div>
                {order && getStatusBadge(order.status)}
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-auto p-6">
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2 text-red-700">
                <AlertCircle className="w-5 h-5" />
                {error}
              </div>
            )}

            {success && (
              <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-2 text-green-700">
                <CheckCircle className="w-5 h-5" />
                {success}
              </div>
            )}

            {order && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Order Information */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Package className="w-5 h-5" />
                      Order Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-900">Order Number:</span>
                        <p className="text-gray-600">{order.orderNumber}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-900">Total Amount:</span>
                        <p className="text-gray-600">{formatCurrency(order.totalAmount, order.currency)}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-900">Customer:</span>
                        <p className="text-gray-600">{order.customerName}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-900">Email:</span>
                        <p className="text-gray-600">{order.customerEmail}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Edit Form */}
                <Card>
                  <CardHeader>
                    <CardTitle>Update Order</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleSubmit} className="space-y-4">
                      <div>
                        <Label htmlFor="status">Order Status</Label>
                        <select
                          id="status"
                          name="status"
                          value={formData.status}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          aria-label="Order status"
                          required
                        >
                          <option value="PENDING">Pending</option>
                          <option value="CONFIRMED">Confirmed</option>
                          <option value="IN_PRODUCTION">In Production</option>
                          <option value="READY_TO_SHIP">Ready to Ship</option>
                          <option value="SHIPPED">Shipped</option>
                          <option value="DELIVERED">Delivered</option>
                          <option value="CANCELLED">Cancelled</option>
                          <option value="REFUNDED">Refunded</option>
                        </select>
                      </div>

                      <div>
                        <Label htmlFor="trackingNumber">Tracking Number</Label>
                        <Input
                          id="trackingNumber"
                          name="trackingNumber"
                          value={formData.trackingNumber}
                          onChange={handleInputChange}
                          placeholder="Enter tracking number..."
                        />
                      </div>

                      <div>
                        <Label htmlFor="requiredDate">Required Date</Label>
                        <Input
                          id="requiredDate"
                          name="requiredDate"
                          type="date"
                          value={formData.requiredDate}
                          onChange={handleInputChange}
                        />
                      </div>

                      <div>
                        <Label htmlFor="notes">Notes</Label>
                        <Textarea
                          id="notes"
                          name="notes"
                          value={formData.notes}
                          onChange={handleInputChange}
                          rows={4}
                          placeholder="Add any notes or comments..."
                        />
                      </div>

                      <div className="flex justify-end gap-4 pt-4">
                        <Button type="button" variant="outline" onClick={() => router.back()}>
                          Cancel
                        </Button>
                        <Button type="submit" disabled={saving} className="flex items-center gap-2">
                          {saving ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          ) : (
                            <Save className="w-4 h-4" />
                          )}
                          {saving ? 'Saving...' : 'Save Changes'}
                        </Button>
                      </div>
                    </form>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
