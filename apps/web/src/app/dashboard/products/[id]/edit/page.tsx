'use client';

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '../../../../../contexts/auth-context';
import { ProtectedRoute } from '../../../../../components/auth/protected-route';
import { Card, CardContent, CardHeader, CardTitle } from '../../../../../components/ui/card';
import { <PERSON><PERSON> } from '../../../../../components/ui/button';
import { Input } from '../../../../../components/ui/input';
import { Textarea } from '../../../../../components/ui/textarea';
import { Label } from '../../../../../components/ui/label';
import { Badge } from '../../../../../components/ui/badge';
import {
  ArrowLeft,
  Save,
  Loader2,
  Image as ImageIcon
} from 'lucide-react';
import Link from 'next/link';
import { ProfessionalImageManager } from '../../../../../components/product/professional-image-manager';
import { InventoryLocationManager } from '../../../../../components/product/inventory-location-manager';
import { StockMovementManager } from '../../../../../components/product/stock-movement-manager';
import { InventoryReservationManager } from '../../../../../components/product/inventory-reservation-manager';

interface Product {
  id: string;
  name: string;
  slug: string;
  description?: string;
  shortDescription?: string;
  basePrice: number;
  currency: string;
  minOrderQty: number;
  sku?: string;
  status: 'DRAFT' | 'ACTIVE' | 'INACTIVE' | 'OUT_OF_STOCK' | 'DISCONTINUED';
  isFeatured: boolean;
  stockQuantity: number;
  stockStatus: string;
  categoryId: string;
  factoryId: string;
  images: Array<{
    id: string;
    url: string;
    isMain: boolean;
    sortOrder: number;
  }>;
}

interface Category {
  id: string;
  name: string;
  slug: string;
}

export default function EditProductPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const [product, setProduct] = useState<Product | null>(null);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [inventoryLocations, setInventoryLocations] = useState<any[]>([]);

  const productId = params.id as string;

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    shortDescription: '',
    description: '',
    basePrice: '',
    currency: 'USD',
    minOrderQty: '',
    sku: '',
    status: 'DRAFT' as const,
    isFeatured: false,
    stockQuantity: '',
    stockStatus: 'IN_STOCK',
    categoryId: '',
  });

  useEffect(() => {
    const loadData = async () => {
      if (!user?.factoryId) {
        setError('No factory ID found');
        setLoading(false);
        return;
      }

      try {
        // Load product and categories in parallel
        const [productResponse, categoriesResponse] = await Promise.all([
          fetch(`/api/products/${productId}?factoryId=${user.factoryId}`),
          fetch(`/api/categories?factoryId=${user.factoryId}`)
        ]);

        if (productResponse.ok) {
          const productData = await productResponse.json();
          setProduct(productData);
          
          // Populate form with existing data
          setFormData({
            name: productData.name || '',
            shortDescription: productData.shortDescription || '',
            description: productData.description || '',
            basePrice: Number(productData.basePrice).toString(),
            currency: productData.currency || 'USD',
            minOrderQty: productData.minOrderQty?.toString() || '',
            sku: productData.sku || '',
            status: productData.status || 'DRAFT',
            isFeatured: productData.isFeatured || false,
            stockQuantity: productData.stockQuantity?.toString() || '',
            stockStatus: productData.stockStatus || 'IN_STOCK',
            categoryId: productData.categoryId || '',
          });
        } else {
          setError('Product not found');
        }

        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json();
          setCategories(categoriesData.categories || []);
        }
      } catch (error) {
        console.error('Error loading data:', error);
        setError('Failed to load product data');
      } finally {
        setLoading(false);
      }
    };

    if (user?.factoryId) {
      loadData();
    }
  }, [user?.factoryId, productId]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user?.factoryId || !product) {
      return;
    }

    setSaving(true);
    setError(null);

    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          basePrice: parseFloat(formData.basePrice),
          minOrderQty: parseInt(formData.minOrderQty),
          stockQuantity: parseInt(formData.stockQuantity),
          factoryId: user.factoryId,
        }),
      });

      if (response.ok) {
        router.push(`/dashboard/products/${productId}`);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to update product');
      }
    } catch (error) {
      console.error('Error updating product:', error);
      setError('Failed to update product');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading product...</div>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-lg text-red-600 mb-4">{error || 'Product not found'}</div>
            <Link href="/dashboard/products">
              <Button>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Products
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute>
      <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Link href={`/dashboard/products/${productId}`}>
            <Button variant="outline">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Product
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Edit Product</h1>
            <p className="text-gray-600">{product.name}</p>
          </div>
        </div>
      </div>

      {/* Professional Image Management */}
      {product && user?.factoryId && (
        <div className="mb-6">
          <ProfessionalImageManager
            productId={product.id}
            factoryId={user.factoryId}
            maxImages={10}
            maxFileSize={5}
            showUpload={true}
            showMetadata={true}
          />
        </div>
      )}

      {/* Edit Form */}
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="name">Product Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="sku">SKU</Label>
                  <Input
                    id="sku"
                    value={formData.sku}
                    onChange={(e) => handleInputChange('sku', e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="shortDescription">Short Description</Label>
                  <Input
                    id="shortDescription"
                    value={formData.shortDescription}
                    onChange={(e) => handleInputChange('shortDescription', e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={4}
                  />
                </div>

                <div>
                  <Label htmlFor="categoryId">Category *</Label>
                  <select
                    id="categoryId"
                    value={formData.categoryId}
                    onChange={(e) => handleInputChange('categoryId', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Select a category</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>
              </CardContent>
            </Card>

            {/* Pricing & Inventory */}
            <Card>
              <CardHeader>
                <CardTitle>Pricing & Inventory</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="basePrice">Base Price *</Label>
                  <Input
                    id="basePrice"
                    type="number"
                    step="0.01"
                    value={formData.basePrice}
                    onChange={(e) => handleInputChange('basePrice', e.target.value)}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="currency">Currency</Label>
                  <select
                    id="currency"
                    value={formData.currency}
                    onChange={(e) => handleInputChange('currency', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="CNY">CNY</option>
                  </select>
                </div>

                <div>
                  <Label htmlFor="minOrderQty">Minimum Order Quantity *</Label>
                  <Input
                    id="minOrderQty"
                    type="number"
                    value={formData.minOrderQty}
                    onChange={(e) => handleInputChange('minOrderQty', e.target.value)}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="stockQuantity">Stock Quantity *</Label>
                  <Input
                    id="stockQuantity"
                    type="number"
                    value={formData.stockQuantity}
                    onChange={(e) => handleInputChange('stockQuantity', e.target.value)}
                    required
                  />
                </div>
              </CardContent>
            </Card>

            {/* Multi-Location Inventory Management */}
            <InventoryLocationManager
              productId={params.id as string}
              factoryId={user?.factoryId || ''}
              onLocationsChange={setInventoryLocations}
            />

            {/* Stock Movement History */}
            <StockMovementManager
              productId={params.id as string}
              factoryId={user?.factoryId || ''}
              inventoryLocations={inventoryLocations}
            />

            {/* Inventory Reservations */}
            <InventoryReservationManager
              productId={params.id as string}
              factoryId={user?.factoryId || ''}
              inventoryLocations={inventoryLocations}
            />
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Status */}
            <Card>
              <CardHeader>
                <CardTitle>Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="status">Product Status</Label>
                  <select
                    id="status"
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="DRAFT">Draft</option>
                    <option value="ACTIVE">Active</option>
                    <option value="INACTIVE">Inactive</option>
                    <option value="OUT_OF_STOCK">Out of Stock</option>
                    <option value="DISCONTINUED">Discontinued</option>
                  </select>
                </div>

                <div>
                  <Label htmlFor="stockStatus">Stock Status</Label>
                  <select
                    id="stockStatus"
                    value={formData.stockStatus}
                    onChange={(e) => handleInputChange('stockStatus', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="IN_STOCK">In Stock</option>
                    <option value="LOW_STOCK">Low Stock</option>
                    <option value="OUT_OF_STOCK">Out of Stock</option>
                  </select>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isFeatured"
                    checked={formData.isFeatured}
                    onChange={(e) => handleInputChange('isFeatured', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <Label htmlFor="isFeatured">Featured Product</Label>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <Button 
                    type="submit" 
                    className="w-full"
                    disabled={saving}
                  >
                    {saving ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 mr-2" />
                        Save Changes
                      </>
                    )}
                  </Button>
                  
                  <Button 
                    type="button" 
                    variant="outline" 
                    className="w-full"
                    asChild
                  >
                    <Link href={`/dashboard/products/${productId}`}>
                      Cancel
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600">{error}</p>
          </div>
        )}
      </form>
      </div>
    </ProtectedRoute>
  );
}
