import { PrismaClient } from '@prisma/client';

// Prevent multiple instances of Prisma Client in development
declare global {
  var __prisma: PrismaClient | undefined;
}

// Create a production-ready Prisma client with proper connection pooling
const createPrismaClient = () => {
  return new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['warn', 'error'] : ['error'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  });
};

// Create a singleton instance of Prisma Client
let prismaInstance = globalThis.__prisma || createPrismaClient();

// In development, save the instance to prevent hot reload issues
if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prismaInstance;
}

// Export the prisma instance
export const prisma = prismaInstance;

// Function to recreate the Prisma client when needed
export const recreatePrismaClient = async () => {
  try {
    // Disconnect the old client
    await prismaInstance.$disconnect();
  } catch (error) {
    console.log('Error disconnecting old Prisma client:', error);
  }

  // Clear the global reference
  if (process.env.NODE_ENV === 'development') {
    globalThis.__prisma = undefined;
  }

  // Create a new client
  prismaInstance = createPrismaClient();

  // Update the global reference
  if (process.env.NODE_ENV === 'development') {
    globalThis.__prisma = prismaInstance;
  }

  // Force a connection to ensure it's ready
  try {
    await prismaInstance.$connect();
    await prismaInstance.$queryRaw`SELECT 1`;
  } catch (error) {
    console.log('Error testing new Prisma connection:', error);
  }

  console.log('✅ Prisma client recreated successfully');
  return prismaInstance;
};

// Production-ready connection management with proper error handling
export const ensurePrismaConnection = async (retries = 3) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      // Test connection with a simple query
      await prismaInstance.$queryRaw`SELECT 1 as test`;
      return prismaInstance;
    } catch (error: any) {
      console.log(`🔄 Database connection attempt ${attempt}/${retries} failed:`, error.message);

      // Check for connection-related errors that require client recreation
      const isConnectionError = error.message?.includes('prepared statement') ||
                               error.message?.includes('does not exist') ||
                               error.message?.includes('connection') ||
                               error.code === '26000' ||
                               error.code === 'P2010' ||
                               error.code === '42P05';

      if (isConnectionError && attempt < retries) {
        try {
          console.log(`🔄 Connection error detected, recreating Prisma client...`);

          // Recreate the client for connection errors
          await recreatePrismaClient();

          // Brief wait for connection to stabilize
          await new Promise(resolve => setTimeout(resolve, 500));

          // Test the new connection
          await prismaInstance.$queryRaw`SELECT 1 as test`;
          console.log(`✅ Database reconnected successfully on attempt ${attempt}`);
          return prismaInstance;
        } catch (retryError: any) {
          console.log(`❌ Reconnection attempt ${attempt} failed:`, retryError.message);

          if (attempt === retries) {
            console.error('❌ All database reconnection attempts failed');
            throw new Error(`Database connection failed after ${retries} attempts: ${retryError.message}`);
          }
        }
      } else if (attempt === retries) {
        throw error;
      }
    }
  }

  throw new Error('Database connection failed after all retry attempts');
};

// Wrapper function for database operations with automatic retry
export const withDatabaseRetry = async <T>(
  operation: () => Promise<T>,
  operationName: string = 'Database operation',
  maxRetries: number = 2
): Promise<T> => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await ensurePrismaConnection();
      return await operation();
    } catch (error: any) {
      const isPreparedStatementError = error.message?.includes('prepared statement') ||
                                     error.message?.includes('does not exist') ||
                                     error.code === '26000';

      console.log(`🔄 ${operationName} attempt ${attempt}/${maxRetries} failed:`, error.message);

      if (isPreparedStatementError && attempt < maxRetries) {
        console.log(`🔄 Retrying ${operationName} due to connection issue...`);
        // Small delay before retry
        await new Promise(resolve => setTimeout(resolve, 500));
        continue;
      }

      // If it's the last attempt or not a connection error, throw
      throw error;
    }
  }

  throw new Error(`${operationName} failed after ${maxRetries} attempts`);
};

// Graceful shutdown
process.on('beforeExit', async () => {
  try {
    await prismaInstance.$disconnect();
  } catch (error) {
    console.error('Error during Prisma cleanup:', error);
  }
});

export default prismaInstance;
