import React, { useState, useCallback, useMemo } from 'react';
import { z } from 'zod';

// Generic form validation hook
export interface ValidationError {
  field: string;
  message: string;
  code?: string;
}

export interface FormValidationOptions<T> {
  schema: z.ZodSchema<T>;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  debounceMs?: number;
}

export interface FormValidationResult<T> {
  values: Partial<T>;
  errors: Record<string, string>;
  isValid: boolean;
  isValidating: boolean;
  hasErrors: boolean;
  setValue: (field: keyof T, value: any) => void;
  setValues: (values: Partial<T>) => void;
  setError: (field: keyof T, message: string) => void;
  clearError: (field: keyof T) => void;
  clearErrors: () => void;
  validate: (field?: keyof T) => Promise<boolean>;
  validateAll: () => Promise<boolean>;
  reset: (values?: Partial<T>) => void;
  getFieldError: (field: keyof T) => string | undefined;
  hasFieldError: (field: keyof T) => boolean;
}

export function useFormValidation<T extends Record<string, any>>(
  initialValues: Partial<T>,
  options: FormValidationOptions<T>
): FormValidationResult<T> {
  const { schema, validateOnChange = false, validateOnBlur = false, debounceMs = 300 } = options;

  const [values, setValuesState] = useState<Partial<T>>(initialValues);
  const [errors, setErrorsState] = useState<Record<string, string>>({});
  const [isValidating, setIsValidating] = useState(false);
  const [touchedFields, setTouchedFields] = useState<Set<string>>(new Set());

  // Debounced validation
  const [validationTimeout, setValidationTimeout] = useState<NodeJS.Timeout | null>(null);

  const clearValidationTimeout = useCallback(() => {
    if (validationTimeout) {
      clearTimeout(validationTimeout);
      setValidationTimeout(null);
    }
  }, [validationTimeout]);

  const validateField = useCallback(async (field: keyof T, value: any): Promise<boolean> => {
    try {
      // Validate the entire object but only check for errors on the specific field
      const testData = { ...values, [field]: value };
      await schema.parseAsync(testData);

      // Clear error if validation passes
      setErrorsState(prev => {
        const newErrors = { ...prev };
        delete newErrors[field as string];
        return newErrors;
      });

      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldError = error.errors.find(err =>
          err.path.length > 0 && err.path[0] === field
        );
        if (fieldError) {
          setErrorsState(prev => ({
            ...prev,
            [field as string]: fieldError.message
          }));
        }
      }
      return false;
    }
  }, [schema, values]);

  const validateAll = useCallback(async (): Promise<boolean> => {
    setIsValidating(true);
    try {
      await schema.parseAsync(values);
      setErrorsState({});
      setIsValidating(false);
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.errors.forEach(err => {
          const field = err.path.join('.');
          newErrors[field] = err.message;
        });
        setErrorsState(newErrors);
      }
      setIsValidating(false);
      return false;
    }
  }, [schema, values]);

  const validate = useCallback(async (field?: keyof T): Promise<boolean> => {
    if (field) {
      return validateField(field, values[field]);
    } else {
      return validateAll();
    }
  }, [validateField, validateAll, values]);

  const setValue = useCallback((field: keyof T, value: any) => {
    setValuesState(prev => ({ ...prev, [field]: value }));
    setTouchedFields(prev => new Set(prev).add(field as string));

    // Clear existing validation timeout
    clearValidationTimeout();

    // Validate on change if enabled
    if (validateOnChange) {
      const timeout = setTimeout(() => {
        validateField(field, value);
      }, debounceMs);
      setValidationTimeout(timeout);
    }
  }, [validateOnChange, debounceMs, validateField, clearValidationTimeout]);

  const setValues = useCallback((newValues: Partial<T>) => {
    setValuesState(prev => ({ ...prev, ...newValues }));
    
    // Mark all new fields as touched
    setTouchedFields(prev => {
      const newTouched = new Set(prev);
      Object.keys(newValues).forEach(key => newTouched.add(key));
      return newTouched;
    });

    // Validate all if enabled
    if (validateOnChange) {
      clearValidationTimeout();
      const timeout = setTimeout(() => {
        validateAll();
      }, debounceMs);
      setValidationTimeout(timeout);
    }
  }, [validateOnChange, debounceMs, validateAll, clearValidationTimeout]);

  const setError = useCallback((field: keyof T, message: string) => {
    setErrorsState(prev => ({ ...prev, [field as string]: message }));
  }, []);

  const clearError = useCallback((field: keyof T) => {
    setErrorsState(prev => {
      const newErrors = { ...prev };
      delete newErrors[field as string];
      return newErrors;
    });
  }, []);

  const clearErrors = useCallback(() => {
    setErrorsState({});
  }, []);

  const reset = useCallback((resetValues?: Partial<T>) => {
    setValuesState(resetValues || initialValues);
    setErrorsState({});
    setTouchedFields(new Set());
    clearValidationTimeout();
  }, [initialValues, clearValidationTimeout]);

  const getFieldError = useCallback((field: keyof T): string | undefined => {
    return errors[field as string];
  }, [errors]);

  const hasFieldError = useCallback((field: keyof T): boolean => {
    return Boolean(errors[field as string]);
  }, [errors]);

  // Computed values
  const isValid = useMemo(() => {
    return Object.keys(errors).length === 0 && Object.keys(values).length > 0;
  }, [errors, values]);

  const hasErrors = useMemo(() => {
    return Object.keys(errors).length > 0;
  }, [errors]);

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      clearValidationTimeout();
    };
  }, [clearValidationTimeout]);

  return {
    values,
    errors,
    isValid,
    isValidating,
    hasErrors,
    setValue,
    setValues,
    setError,
    clearError,
    clearErrors,
    validate,
    validateAll,
    reset,
    getFieldError,
    hasFieldError,
  };
}

// Specialized hooks for common validation scenarios

// Product form validation hook
export function useProductFormValidation(initialValues: any) {
  // Create a basic product validation schema
  const ProductValidationSchema = z.object({
    name: z.string().min(1, 'Product name is required'),
    sku: z.string().min(1, 'SKU is required'),
    description: z.string().optional(),
    price: z.number().min(0, 'Price must be non-negative'),
    currency: z.string().default('USD'),
    moq: z.number().int().min(1, 'MOQ must be at least 1'),
    status: z.enum(['DRAFT', 'ACTIVE', 'INACTIVE', 'DISCONTINUED']).default('DRAFT'),
    category: z.string().optional(),
    brand: z.string().optional(),
    model: z.string().optional(),
    weight: z.number().min(0).optional(),
    weightUnit: z.enum(['kg', 'lb', 'g', 'oz']).optional(),
    manufacturingTime: z.number().int().min(0).optional(),
    productionCapacity: z.number().int().min(0).optional(),
    leadTime: z.number().int().min(0).optional(),
    materialComposition: z.string().optional(),
    technicalDataSheet: z.string().url().optional(),
    userManual: z.string().url().optional(),
    ceCertified: z.boolean().optional(),
    fccCertified: z.boolean().optional(),
    rohsCompliant: z.boolean().optional(),
    isoCertified: z.boolean().optional(),
    complianceNotes: z.string().optional(),
    marketingDescription: z.string().optional(),
    keyFeatures: z.string().optional(),
  });

  return useFormValidation(initialValues, {
    schema: ProductValidationSchema,
    validateOnChange: true,
    validateOnBlur: true,
    debounceMs: 500,
  });
}

// Inventory location form validation hook
export function useInventoryLocationValidation(initialValues: any) {
  const LocationFormSchema = z.object({
    locationName: z.string().min(1, 'Location name is required'),
    locationCode: z.string().optional(),
    locationAddress: z.string().optional(),
    locationManager: z.string().optional(),
    stockQuantity: z.number().int().min(0, 'Stock quantity must be non-negative'),
    reorderPoint: z.number().int().min(0).optional(),
    maxStockLevel: z.number().int().min(0).optional(),
    minStockLevel: z.number().int().min(0).optional(),
    safetyStock: z.number().int().min(0).optional(),
    averageCost: z.number().min(0).optional(),
    lastCost: z.number().min(0).optional(),
    leadTime: z.number().int().min(0).optional(),
    allowBackorders: z.boolean().default(false),
    trackingEnabled: z.boolean().default(true),
  });

  return useFormValidation(initialValues, {
    schema: LocationFormSchema,
    validateOnChange: true,
    validateOnBlur: true,
    debounceMs: 300,
  });
}

// Stock movement form validation hook
export function useStockMovementValidation(initialValues: any) {
  const MovementFormSchema = z.object({
    movementType: z.enum(['INBOUND', 'OUTBOUND', 'ADJUSTMENT', 'TRANSFER', 'RETURN', 'DAMAGED', 'EXPIRED']),
    quantity: z.number().int().positive('Quantity must be positive'),
    unitCost: z.number().min(0).optional(),
    reason: z.string().optional(),
    notes: z.string().optional(),
    batchNumber: z.string().optional(),
    expiryDate: z.string().datetime().optional(),
    fromLocationId: z.string().optional(),
    toLocationId: z.string().optional(),
  });

  return useFormValidation(initialValues, {
    schema: MovementFormSchema,
    validateOnChange: true,
    validateOnBlur: true,
    debounceMs: 300,
  });
}

// Inventory reservation form validation hook
export function useInventoryReservationValidation(initialValues: any) {
  const ReservationFormSchema = z.object({
    reservationType: z.enum(['ORDER', 'QUOTE', 'MANUAL', 'TRANSFER', 'QUALITY_HOLD']),
    quantity: z.number().int().positive('Quantity must be positive'),
    referenceId: z.string().optional(),
    reservedUntil: z.string().datetime().optional(),
    priority: z.enum(['LOW', 'NORMAL', 'HIGH', 'URGENT']).default('NORMAL'),
    reason: z.string().optional(),
    notes: z.string().optional(),
  });

  return useFormValidation(initialValues, {
    schema: ReservationFormSchema,
    validateOnChange: true,
    validateOnBlur: true,
    debounceMs: 300,
  });
}

// Validation helper functions
export function formatValidationError(error: z.ZodError): ValidationError[] {
  return error.errors.map(err => ({
    field: err.path.join('.'),
    message: err.message,
    code: err.code,
  }));
}

export function getFirstValidationError(errors: Record<string, string>): string | null {
  const firstKey = Object.keys(errors)[0];
  return firstKey ? errors[firstKey] : null;
}

export function hasValidationErrors(errors: Record<string, string>): boolean {
  return Object.keys(errors).length > 0;
}

// Custom validation rules
export const customValidationRules = {
  // Validate that a number is within a range
  numberRange: (min: number, max: number) => (value: number) => {
    return value >= min && value <= max;
  },
  
  // Validate that a string matches a pattern
  stringPattern: (pattern: RegExp, message: string) => (value: string) => {
    return pattern.test(value) || message;
  },
  
  // Validate that a date is in the future
  futureDate: (value: string | Date) => {
    const date = new Date(value);
    return date > new Date() || 'Date must be in the future';
  },
  
  // Validate that a date is in the past
  pastDate: (value: string | Date) => {
    const date = new Date(value);
    return date < new Date() || 'Date must be in the past';
  },
  
  // Validate file size
  fileSize: (maxSizeBytes: number) => (file: File) => {
    return file.size <= maxSizeBytes || `File size must be less than ${maxSizeBytes / 1024 / 1024}MB`;
  },
  
  // Validate file type
  fileType: (allowedTypes: string[]) => (file: File) => {
    return allowedTypes.includes(file.type) || `File type must be one of: ${allowedTypes.join(', ')}`;
  },
  
  // Validate URL accessibility
  urlAccessible: async (url: string) => {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok || 'URL is not accessible';
    } catch {
      return 'URL is not accessible';
    }
  },
  
  // Validate unique value (requires async check)
  unique: (checkFunction: (value: any) => Promise<boolean>) => async (value: any) => {
    const isUnique = await checkFunction(value);
    return isUnique || 'Value must be unique';
  },
};

// Form field validation status
export interface FieldValidationStatus {
  isValid: boolean;
  isInvalid: boolean;
  isPending: boolean;
  message?: string;
}

export function getFieldValidationStatus(
  field: string,
  errors: Record<string, string>,
  isValidating: boolean,
  touchedFields: Set<string>
): FieldValidationStatus {
  const hasError = Boolean(errors[field]);
  const isTouched = touchedFields.has(field);
  
  return {
    isValid: isTouched && !hasError && !isValidating,
    isInvalid: isTouched && hasError,
    isPending: isValidating,
    message: errors[field],
  };
}
