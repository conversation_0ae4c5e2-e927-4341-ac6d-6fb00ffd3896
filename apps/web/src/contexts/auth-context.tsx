'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import type { User } from '../types/api';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: () => void;
  logout: () => void;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  // Add compatibility with existing dashboard
  payloadUser: {
    firstName: string;
    lastName: string;
    role: string;
    avatar?: string;
  } | null;
  openPayloadAdmin: () => void;
  canAccessAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [payloadUser, setPayloadUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Helper function for fetch with retry logic
  const fetchWithRetry = async (url: string, options: RequestInit = {}, retries = 2): Promise<Response> => {
    for (let i = 0; i <= retries; i++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout

        const response = await fetch(url, {
          ...options,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);
        return response;
      } catch (error) {
        if (i === retries) {
          throw error;
        }
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, i)));
      }
    }
    throw new Error('Max retries exceeded');
  };

  // Check authentication status on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await fetchWithRetry('/api/auth/me', {
          credentials: 'include',
        });

        if (response.ok) {
          const userData = await response.json();
          setUser(userData);
          setIsAuthenticated(true);

          // Try to fetch additional factory/payload user data
          try {
            if (userData.payloadUser) {
              setPayloadUser(userData.payloadUser);
            } else {
              // Create a mock factory user for development
              setPayloadUser({
                firstName: userData.given_name || 'Factory',
                lastName: userData.family_name || 'Owner',
                email: userData.email,
                role: 'factory_admin',
                factoryId: 'factory_001',
                avatar: userData.picture
              });
            }
          } catch (error) {
            console.warn('Failed to set factory user data:', error);
          }
        } else {
          setUser(null);
          setPayloadUser(null);
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('Error checking auth status:', error);
        setUser(null);
        setPayloadUser(null);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = () => {
    window.location.href = '/api/auth/login';
  };

  const logout = async () => {
    try {
      // Clear local state immediately
      setUser(null);
      setPayloadUser(null);
      setIsAuthenticated(false);

      // Call logout endpoint
      await fetch('/api/auth/logout', { method: 'POST' });

      // Redirect to home page
      window.location.href = '/';
    } catch (error) {
      console.error('Logout error:', error);
      // Even if logout fails, clear state and redirect to home
      setUser(null);
      setPayloadUser(null);
      setIsAuthenticated(false);
      window.location.href = '/';
    }
  };

  const hasPermission = (permission: string): boolean => {
    return user?.permissions?.includes(permission) || false;
  };

  const hasRole = (role: string): boolean => {
    return user?.role === role;
  };

  const openPayloadAdmin = () => {
    // Temporarily disabled while Payload CMS integration is being fixed
    console.log('Payload CMS admin access temporarily disabled');
    // window.open('/admin', '_blank');
  };

  // Use payloadUser state or create compatibility layer
  const effectivePayloadUser = payloadUser || (user ? {
    firstName: user.name?.split(' ')[0] || 'Factory',
    lastName: user.name?.split(' ')[1] || 'User',
    role: user.role || 'factory_admin',
    avatar: user.picture,
    email: user.email,
    factoryId: 'factory_001'
  } : null);

  const canAccessAdmin = user?.role === 'factory_owner' || user?.role === 'admin';

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    hasPermission,
    hasRole,
    payloadUser: effectivePayloadUser,
    openPayloadAdmin,
    canAccessAdmin,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
