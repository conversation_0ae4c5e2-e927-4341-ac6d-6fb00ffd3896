'use client';

import { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { ScrollArea } from '../ui/scroll-area';
import { Badge } from '../ui/badge';
import { Avatar } from '../ui/avatar';
import { Separator } from '../ui/separator';
import { 
  Send, 
  Paperclip, 
  Search, 
  Plus, 
  MoreVertical,
  ArrowLeft,
  Phone,
  Video,
  Info
} from 'lucide-react';
import { trpc } from '../../lib/trpc';
import { LoadingSpinner } from '../ui/loading-spinner';
import { formatDateTime } from '../../lib/utils';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu';
import { NewConversationDialog } from './new-conversation-dialog';

interface MessageCenterDialogProps {
  isOpen: boolean;
  onClose: () => void;
  initialConversationId?: string;
}

export function MessageCenterDialog({ isOpen, onClose, initialConversationId }: MessageCenterDialogProps) {
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(initialConversationId || null);
  const [messageText, setMessageText] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [showNewConversation, setShowNewConversation] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Fetch conversations
  const { data: conversationsData, isLoading: conversationsLoading } = trpc.messages.getConversations.useQuery({
    page: 1,
    limit: 50,
  });

  // Fetch messages for selected conversation
  const { data: messagesData, isLoading: messagesLoading } = trpc.messages.getMessages.useQuery(
    {
      conversationId: selectedConversationId!,
      page: 1,
      limit: 100,
    },
    {
      enabled: !!selectedConversationId,
    }
  );

  // Send message mutation
  const sendMessageMutation = trpc.messages.sendMessage.useMutation({
    onSuccess: () => {
      setMessageText('');
      // Refetch messages and conversations
      trpc.useContext().messages.getMessages.invalidate();
      trpc.useContext().messages.getConversations.invalidate();
      trpc.useContext().messages.getUnreadCount.invalidate();
    },
  });

  // Mark messages as read when conversation is selected
  const markAsReadMutation = trpc.messages.markMessageRead.useMutation({
    onSuccess: () => {
      trpc.useContext().messages.getUnreadCount.invalidate();
    },
  });

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messagesData]);

  // Mark messages as read when conversation is opened
  useEffect(() => {
    if (selectedConversationId && messagesData?.data) {
      // Mark all messages in the conversation as read
      markAsReadMutation.mutate({
        conversationId: selectedConversationId,
        messageId: messagesData.data[messagesData.data.length - 1]?.id || '',
      });
    }
  }, [selectedConversationId, messagesData]);

  const handleSendMessage = () => {
    if (!messageText.trim() || !selectedConversationId) return;

    sendMessageMutation.mutate({
      conversationId: selectedConversationId,
      content: messageText.trim(),
      messageType: 'TEXT',
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const conversations = conversationsData?.data || [];
  const messages = messagesData?.data || [];
  const selectedConversation = conversations.find(c => c.id === selectedConversationId);

  const filteredConversations = conversations.filter(conv => {
    if (!searchQuery) return true;
    const participant = conv.participants.find(p => p.user.id !== conv.participants[0]?.user.id);
    const participantName = `${participant?.user.firstName} ${participant?.user.lastName}`.toLowerCase();
    const subject = conv.subject?.toLowerCase() || '';
    return participantName.includes(searchQuery.toLowerCase()) || subject.includes(searchQuery.toLowerCase());
  });

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl h-[80vh] p-0">
        <div className="flex h-full">
          {/* Conversations Sidebar */}
          <div className="w-1/3 border-r flex flex-col">
            <DialogHeader className="p-4 border-b">
              <div className="flex items-center justify-between">
                <DialogTitle>Messages</DialogTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowNewConversation(true)}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
              <div className="relative mt-2">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search conversations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </DialogHeader>

            <ScrollArea className="flex-1">
              {conversationsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : filteredConversations.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No conversations found</p>
                </div>
              ) : (
                <div className="p-2">
                  {filteredConversations.map((conversation) => {
                    const otherParticipant = conversation.participants.find(
                      p => p.user.id !== conversation.participants[0]?.user.id
                    );
                    const hasUnread = conversation.participants.some(p => p.unreadCount > 0);
                    const isSelected = conversation.id === selectedConversationId;

                    return (
                      <div
                        key={conversation.id}
                        className={`p-3 rounded-lg cursor-pointer transition-colors mb-2 ${
                          isSelected 
                            ? 'bg-blue-100 border-blue-200' 
                            : hasUnread 
                            ? 'bg-blue-50 hover:bg-blue-100' 
                            : 'hover:bg-gray-50'
                        }`}
                        onClick={() => setSelectedConversationId(conversation.id)}
                      >
                        <div className="flex items-start space-x-3">
                          <Avatar className="w-10 h-10">
                            <div className="w-full h-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                              {otherParticipant?.user.firstName?.[0]}{otherParticipant?.user.lastName?.[0]}
                            </div>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <h4 className="text-sm font-medium text-gray-900 truncate">
                                {otherParticipant?.user.firstName} {otherParticipant?.user.lastName}
                              </h4>
                              {hasUnread && (
                                <Badge variant="destructive" className="text-xs">
                                  {conversation.participants.find(p => p.unreadCount > 0)?.unreadCount}
                                </Badge>
                              )}
                            </div>
                            <p className="text-xs text-gray-500 truncate">
                              {conversation.subject || 'No subject'}
                            </p>
                            {conversation.lastMessage && (
                              <p className="text-xs text-gray-600 truncate mt-1">
                                {conversation.lastMessage.content}
                              </p>
                            )}
                            <p className="text-xs text-gray-400 mt-1">
                              {conversation.lastMessageAt && formatDateTime(conversation.lastMessageAt)}
                            </p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </ScrollArea>
          </div>

          {/* Message Thread */}
          <div className="flex-1 flex flex-col">
            {selectedConversation ? (
              <>
                {/* Conversation Header */}
                <div className="p-4 border-b flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="md:hidden"
                      onClick={() => setSelectedConversationId(null)}
                    >
                      <ArrowLeft className="w-4 h-4" />
                    </Button>
                    <Avatar className="w-8 h-8">
                      <div className="w-full h-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                        {selectedConversation.participants[0]?.user.firstName?.[0]}
                        {selectedConversation.participants[0]?.user.lastName?.[0]}
                      </div>
                    </Avatar>
                    <div>
                      <h3 className="text-sm font-medium">
                        {selectedConversation.participants.find(p => p.user.id !== selectedConversation.participants[0]?.user.id)?.user.firstName} {' '}
                        {selectedConversation.participants.find(p => p.user.id !== selectedConversation.participants[0]?.user.id)?.user.lastName}
                      </h3>
                      <p className="text-xs text-gray-500">
                        {selectedConversation.subject || 'No subject'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm">
                      <Phone className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Video className="w-4 h-4" />
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem>
                          <Info className="w-4 h-4 mr-2" />
                          Conversation Info
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                {/* Messages */}
                <ScrollArea className="flex-1 p-4">
                  {messagesLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <LoadingSpinner />
                    </div>
                  ) : messages.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <p>No messages yet. Start the conversation!</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {messages.map((message, index) => {
                        const isCurrentUser = message.sender.id === 'current-user-id'; // TODO: Get from auth context
                        const showAvatar = index === 0 || messages[index - 1]?.sender.id !== message.sender.id;

                        return (
                          <div
                            key={message.id}
                            className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
                          >
                            <div className={`flex max-w-xs lg:max-w-md ${isCurrentUser ? 'flex-row-reverse' : 'flex-row'}`}>
                              {showAvatar && !isCurrentUser && (
                                <Avatar className="w-8 h-8 mr-2">
                                  <div className="w-full h-full bg-gray-500 flex items-center justify-center text-white text-sm font-medium">
                                    {message.sender.firstName?.[0]}{message.sender.lastName?.[0]}
                                  </div>
                                </Avatar>
                              )}
                              <div
                                className={`px-4 py-2 rounded-lg ${
                                  isCurrentUser
                                    ? 'bg-blue-500 text-white'
                                    : 'bg-gray-100 text-gray-900'
                                }`}
                              >
                                <p className="text-sm">{message.content}</p>
                                <p className={`text-xs mt-1 ${isCurrentUser ? 'text-blue-100' : 'text-gray-500'}`}>
                                  {formatDateTime(message.createdAt)}
                                  {message.isEdited && ' (edited)'}
                                </p>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                      <div ref={messagesEndRef} />
                    </div>
                  )}
                </ScrollArea>

                {/* Message Input */}
                <div className="p-4 border-t">
                  <div className="flex items-end space-x-2">
                    <Button variant="ghost" size="sm">
                      <Paperclip className="w-4 h-4" />
                    </Button>
                    <div className="flex-1">
                      <Textarea
                        placeholder="Type a message..."
                        value={messageText}
                        onChange={(e) => setMessageText(e.target.value)}
                        onKeyPress={handleKeyPress}
                        className="min-h-[40px] max-h-32 resize-none"
                        rows={1}
                      />
                    </div>
                    <Button
                      onClick={handleSendMessage}
                      disabled={!messageText.trim() || sendMessageMutation.isLoading}
                      size="sm"
                    >
                      {sendMessageMutation.isLoading ? (
                        <LoadingSpinner className="w-4 h-4" />
                      ) : (
                        <Send className="w-4 h-4" />
                      )}
                    </Button>
                  </div>
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Send className="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Select a conversation</h3>
                  <p>Choose a conversation from the sidebar to start messaging</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>

      <NewConversationDialog
        isOpen={showNewConversation}
        onClose={() => setShowNewConversation(false)}
        onConversationCreated={(conversationId) => {
          setSelectedConversationId(conversationId);
          setShowNewConversation(false);
        }}
      />
    </Dialog>
  );
}
