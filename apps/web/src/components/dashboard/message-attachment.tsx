'use client';

import { useState, useRef, useCallback } from 'react';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { Card } from '../ui/card';
import {
  Paperclip,
  File,
  Image,
  Video,
  Music,
  Download,
  X,
  Upload,
  AlertCircle,
  CheckCircle2
} from 'lucide-react';
import { trpc } from '../../lib/trpc';
import { LoadingSpinner } from '../ui/loading-spinner';
import { toast } from 'sonner';

interface MessageAttachmentUploaderProps {
  conversationId: string;
  onAttachmentUploaded?: (attachment: any) => void;
  maxFileSize?: number; // in MB
  maxFiles?: number;
  allowedTypes?: string[];
  disabled?: boolean;
}

interface AttachmentUpload {
  id: string;
  file: File;
  preview?: string;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
  uploadUrl?: string;
  filePath?: string;
  attachmentId?: string;
}

export function MessageAttachmentUploader({
  conversationId,
  onAttachmentUploaded,
  maxFileSize = 50, // 50MB for messages
  maxFiles = 10,
  allowedTypes = [
    'image/jpeg', 'image/png', 'image/webp', 'image/gif',
    'application/pdf', 'text/plain', 'text/csv',
    'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ],
  disabled = false
}: MessageAttachmentUploaderProps) {
  const [uploads, setUploads] = useState<AttachmentUpload[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Generate upload URL mutation
  const generateUploadUrlMutation = trpc.messages.generateAttachmentUploadUrl.useMutation();

  // Confirm upload mutation
  const confirmUploadMutation = trpc.messages.confirmAttachmentUpload.useMutation({
    onSuccess: (data, variables) => {
      const uploadId = variables.filePath.split('/').pop()?.split('-')[0] || '';
      setUploads(prev => prev.map(upload =>
        upload.id === uploadId
          ? { ...upload, status: 'completed', progress: 100, attachmentId: data.attachment.id }
          : upload
      ));
      onAttachmentUploaded?.(data.attachment);
      toast.success('File uploaded successfully');
    },
    onError: (error, variables) => {
      const uploadId = variables.filePath.split('/').pop()?.split('-')[0] || '';
      setUploads(prev => prev.map(upload =>
        upload.id === uploadId
          ? { ...upload, status: 'error', error: error.message }
          : upload
      ));
      toast.error('Upload failed: ' + error.message);
    },
  });

  // Validate file before upload
  const validateFile = useCallback((file: File): string | null => {
    // Check file size
    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size must be less than ${maxFileSize}MB`;
    }

    // Check file type
    if (!allowedTypes.includes(file.type)) {
      return `File type ${file.type} is not allowed`;
    }

    // Check total files limit
    if (uploads.length >= maxFiles) {
      return `Maximum ${maxFiles} files allowed`;
    }

    return null;
  }, [maxFileSize, allowedTypes, maxFiles, uploads.length]);

  // Create preview for image files
  const createPreview = useCallback((file: File): Promise<string | undefined> => {
    return new Promise((resolve) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.onerror = () => resolve(undefined);
        reader.readAsDataURL(file);
      } else {
        resolve(undefined);
      }
    });
  }, []);

  // Upload file to Supabase Storage
  const uploadToStorage = useCallback(async (
    uploadUrl: string,
    file: File,
    uploadId: string
  ): Promise<void> => {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      // Track upload progress
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          setUploads(prev => prev.map(upload =>
            upload.id === uploadId
              ? { ...upload, progress, status: 'uploading' }
              : upload
          ));
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          setUploads(prev => prev.map(upload =>
            upload.id === uploadId
              ? { ...upload, status: 'processing', progress: 100 }
              : upload
          ));
          resolve();
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Upload failed'));
      });

      xhr.open('PUT', uploadUrl);
      xhr.setRequestHeader('Content-Type', file.type);
      xhr.send(file);
    });
  }, []);

  // Handle file selection and upload
  const handleFileSelect = useCallback(async (files: FileList | null) => {
    if (!files || disabled) return;

    const fileArray = Array.from(files);

    for (const file of fileArray) {
      const validation = validateFile(file);
      if (validation) {
        toast.error(validation);
        continue;
      }

      const uploadId = Math.random().toString(36).substr(2, 9);
      const preview = await createPreview(file);

      const newUpload: AttachmentUpload = {
        id: uploadId,
        file,
        preview,
        status: 'pending',
        progress: 0,
      };

      setUploads(prev => [...prev, newUpload]);

      // Start upload process
      try {
        // Step 1: Generate upload URL
        const { uploadUrl, filePath } = await generateUploadUrlMutation.mutateAsync({
          fileName: file.name,
          fileSize: file.size,
          mimeType: file.type,
          conversationId,
        });

        setUploads(prev => prev.map(upload =>
          upload.id === uploadId
            ? { ...upload, uploadUrl, filePath, status: 'uploading' }
            : upload
        ));

        // Step 2: Upload to Supabase Storage
        await uploadToStorage(uploadUrl, file, uploadId);

        // Step 3: Confirm upload and create attachment record
        await confirmUploadMutation.mutateAsync({
          filePath,
          conversationId,
          originalName: file.name,
        });

      } catch (error) {
        setUploads(prev => prev.map(upload =>
          upload.id === uploadId
            ? {
                ...upload,
                status: 'error',
                error: error instanceof Error ? error.message : 'Upload failed'
              }
            : upload
        ));
        toast.error('Upload failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
      }
    }
  }, [disabled, validateFile, createPreview, conversationId, generateUploadUrlMutation, confirmUploadMutation, uploadToStorage]);
      const uploadFile = async () => {
        try {
          // Simulate progress
          for (let progress = 0; progress <= 100; progress += 10) {
            await new Promise(resolve => setTimeout(resolve, 100));
            setAttachments(prev => prev.map(a => 
              a.id === attachmentId 
                ? { ...a, progress }
                : a
            ));
          }

          // Upload to server
          await uploadAttachmentMutation.mutateAsync({
            filename: `${attachmentId}-${file.name}`,
            originalName: file.name,
            mimeType: file.type,
            fileSize: file.size,
            fileUrl: 'https://placeholder-url.com', // This would be the actual uploaded URL
            storagePath: `messages/${attachmentId}-${file.name}`,
            bucketName: 'messages',
          });
        } catch (error) {
          console.error('Upload failed:', error);
        }
      };

      uploadFile();
    });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const removeAttachment = (id: string) => {
    setAttachments(prev => prev.filter(a => a.id !== id));
  };

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return <Image className="w-4 h-4" />;
    if (mimeType.startsWith('video/')) return <Video className="w-4 h-4" />;
    if (mimeType.startsWith('audio/')) return <Music className="w-4 h-4" />;
    return <File className="w-4 h-4" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          isDragOver 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
        <p className="text-sm text-gray-600 mb-2">
          Drag and drop files here, or{' '}
          <button
            onClick={() => fileInputRef.current?.click()}
            className="text-blue-500 hover:text-blue-600 underline"
          >
            browse
          </button>
        </p>
        <p className="text-xs text-gray-500">
          Maximum file size: {maxFileSize}MB
        </p>
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={allowedTypes.join(',')}
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
        />
      </div>

      {/* Attachment Previews */}
      {attachments.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Attachments</h4>
          {attachments.map(attachment => (
            <div
              key={attachment.id}
              className="flex items-center space-x-3 p-3 border rounded-lg bg-gray-50"
            >
              {/* File Icon/Preview */}
              <div className="flex-shrink-0">
                {attachment.preview ? (
                  <img
                    src={attachment.preview}
                    alt={attachment.file.name}
                    className="w-10 h-10 object-cover rounded"
                  />
                ) : (
                  <div className="w-10 h-10 bg-gray-200 rounded flex items-center justify-center">
                    {getFileIcon(attachment.file.type)}
                  </div>
                )}
              </div>

              {/* File Info */}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {attachment.file.name}
                </p>
                <p className="text-xs text-gray-500">
                  {formatFileSize(attachment.file.size)}
                </p>
                
                {/* Upload Progress */}
                {attachment.uploading && (
                  <div className="mt-2">
                    <Progress value={attachment.progress} className="h-1" />
                    <p className="text-xs text-gray-500 mt-1">
                      Uploading... {attachment.progress}%
                    </p>
                  </div>
                )}

                {/* Error */}
                {attachment.error && (
                  <p className="text-xs text-red-500 mt-1">
                    {attachment.error}
                  </p>
                )}
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-2">
                {attachment.uploading ? (
                  <LoadingSpinner className="w-4 h-4" />
                ) : (
                  <Badge variant={attachment.error ? 'destructive' : 'secondary'} className="text-xs">
                    {attachment.error ? 'Failed' : 'Ready'}
                  </Badge>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeAttachment(attachment.id)}
                  className="h-6 w-6 p-0"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Quick Upload Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => fileInputRef.current?.click()}
        className="w-full"
      >
        <Paperclip className="w-4 h-4 mr-2" />
        Attach Files
      </Button>
    </div>
  );
}
