'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { MessageSquare, ExternalLink, Plus } from 'lucide-react';
import { formatDateTime } from '../../lib/utils';
import { trpc } from '../../lib/trpc';
import { LoadingSpinner } from '../ui/loading-spinner';
import { MessageCenterDialog } from './message-center-dialog';

export function MessageCenter() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Fetch conversations with real-time updates
  const { data: conversationsData, isLoading, error } = trpc.messages.getConversations.useQuery({
    page: 1,
    limit: 5, // Show only recent 5 conversations in dashboard widget
  });

  // Get unread count
  const { data: unreadCountData } = trpc.messages.getUnreadCount.useQuery();
  const unreadCount = typeof unreadCountData === 'number' ? unreadCountData : unreadCountData?.count || 0;

  if (isLoading) {
    return (
      <Card className="h-fit">
        <CardHeader>
          <CardTitle>Messages</CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <LoadingSpinner />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="h-fit">
        <CardHeader>
          <CardTitle>Messages</CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8 text-gray-500">
          Failed to load messages
        </CardContent>
      </Card>
    );
  }

  const conversations = conversationsData?.data || [];

  return (
    <>
      <Card className="h-fit">
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle>Messages</CardTitle>
            {unreadCount > 0 && (
              <Badge variant="destructive" className="text-xs">
                {unreadCount}
              </Badge>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsDialogOpen(true)}
            >
              <Plus className="w-4 h-4 mr-2" />
              New
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsDialogOpen(true)}
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              View All
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {conversations.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <MessageSquare className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>No messages yet</p>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() => setIsDialogOpen(true)}
              >
                Start a conversation
              </Button>
            </div>
          ) : (
            conversations.map((conversation) => {
              const lastMessage = conversation.lastMessage;
              const otherParticipant = conversation.participants.find(
                p => p.user.id !== conversation.participants[0]?.user.id
              );
              const hasUnread = conversation.participants.some(p => p.unreadCount > 0);

              return (
                <div
                  key={conversation.id}
                  className={`p-3 rounded-lg border transition-colors hover:bg-gray-50 cursor-pointer ${
                    hasUnread ? 'bg-blue-50 border-blue-200' : 'bg-white'
                  }`}
                  onClick={() => setIsDialogOpen(true)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {otherParticipant?.user.firstName} {otherParticipant?.user.lastName}
                        </h4>
                        {hasUnread && (
                          <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                        )}
                      </div>
                      <p className="text-xs text-gray-500">
                        {conversation.subject || 'No subject'}
                      </p>
                    </div>
                    <MessageSquare className="w-4 h-4 text-gray-400 flex-shrink-0" />
                  </div>

                  {lastMessage && (
                    <>
                      <p className="text-xs text-gray-600 line-clamp-2 mb-2">
                        {lastMessage.content}
                      </p>
                      <p className="text-xs text-gray-400">
                        {formatDateTime(lastMessage.createdAt)}
                      </p>
                    </>
                  )}
                </div>
              );
            })
          )}
        </CardContent>
      </Card>

      <MessageCenterDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
      />
    </>
  );
}
