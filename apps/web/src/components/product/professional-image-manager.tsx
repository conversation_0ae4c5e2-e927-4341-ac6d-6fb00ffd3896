'use client';

import { useState, useCallback, useEffect } from 'react';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { useDropzone } from 'react-dropzone';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Progress } from '../ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { 
  Upload, 
  X, 
  Star, 
  GripVertical, 
  Edit3,
  Trash2,
  Image as ImageIcon,
  Plus,
  Loader2,
  AlertCircle,
  CheckCircle2,
  ZoomIn,
  Download,
  Eye,
  Save,
  RotateCw,
  Crop,
  Palette,
  FileImage,
  Info
} from 'lucide-react';
import { toast } from 'sonner';
import { trpc } from '../../lib/trpc';
import { EnhancedImageGallery } from './enhanced-image-gallery';

interface ProductImage {
  id: string;
  url: string;
  isMain: boolean;
  sortOrder: number;
  originalName?: string;
  altText?: string;
  caption?: string;
  fileSize?: number;
  mimeType?: string;
  status: 'ACTIVE' | 'INACTIVE' | 'DELETED';
}

interface UploadFile {
  id: string;
  file: File;
  preview: string;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
  uploadedUrl?: string;
  altText?: string;
  caption?: string;
}

interface ProfessionalImageManagerProps {
  productId: string;
  factoryId: string;
  maxImages?: number;
  maxFileSize?: number; // in MB
  showUpload?: boolean;
  showMetadata?: boolean;
  onImagesChange?: (images: ProductImage[]) => void;
  className?: string;
}

export function ProfessionalImageManager({
  productId,
  factoryId,
  maxImages = 10,
  maxFileSize = 5,
  showUpload = true,
  showMetadata = true,
  onImagesChange,
  className = ''
}: ProfessionalImageManagerProps) {
  const [images, setImages] = useState<ProductImage[]>([]);
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [editingImage, setEditingImage] = useState<string | null>(null);
  const [editMetadata, setEditMetadata] = useState<{ altText: string; caption: string }>({
    altText: '',
    caption: ''
  });

  // tRPC queries and mutations
  const { data: productImages, refetch: refetchImages, isLoading } = trpc.imageManagement.getProductImages.useQuery({
    productId,
    factoryId
  });

  const addImagesMutation = trpc.imageManagement.addImages.useMutation({
    onSuccess: () => {
      refetchImages();
      toast.success('Images uploaded successfully');
    },
    onError: (error) => {
      toast.error(`Upload failed: ${error.message}`);
    }
  });

  const updateImageMutation = trpc.imageManagement.updateImageMetadata.useMutation({
    onSuccess: () => {
      refetchImages();
      toast.success('Image metadata updated');
      setEditingImage(null);
    },
    onError: (error) => {
      toast.error(`Update failed: ${error.message}`);
    }
  });

  const reorderImagesMutation = trpc.imageManagement.reorderImagesByIds.useMutation({
    onSuccess: () => {
      refetchImages();
      toast.success('Images reordered successfully');
    },
    onError: (error) => {
      toast.error(`Reorder failed: ${error.message}`);
    }
  });

  // Update local images when data changes
  useEffect(() => {
    if (productImages) {
      setImages(productImages);
      onImagesChange?.(productImages);
    }
  }, [productImages, onImagesChange]);

  // Dropzone configuration
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp', '.bmp']
    },
    maxFiles: maxImages - images.length,
    maxSize: maxFileSize * 1024 * 1024,
    onDrop: handleFileDrop,
    disabled: isUploading || images.length >= maxImages
  });

  function handleFileDrop(acceptedFiles: File[]) {
    if (images.length + acceptedFiles.length > maxImages) {
      toast.error(`Maximum ${maxImages} images allowed`);
      return;
    }

    const newUploadFiles: UploadFile[] = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substring(7),
      file,
      preview: URL.createObjectURL(file),
      status: 'pending',
      progress: 0,
      altText: '',
      caption: ''
    }));

    setUploadFiles(prev => [...prev, ...newUploadFiles]);
  }

  const handleUploadFiles = useCallback(async () => {
    if (uploadFiles.length === 0) return;

    setIsUploading(true);
    
    try {
      // Mark all files as uploading
      setUploadFiles(prev => prev.map(f => ({ ...f, status: 'uploading' as const })));

      const uploadPromises = uploadFiles.map(async (uploadFile, index) => {
        try {
          // Simulate progress updates
          const progressInterval = setInterval(() => {
            setUploadFiles(prev => prev.map(f => 
              f.id === uploadFile.id 
                ? { ...f, progress: Math.min(f.progress + 10, 90) }
                : f
            ));
          }, 200);

          // Upload to Supabase via API
          const formData = new FormData();
          formData.append('file', uploadFile.file);
          formData.append('factoryId', factoryId);
          formData.append('productId', productId);

          const response = await fetch('/api/upload/image', {
            method: 'POST',
            body: formData,
          });

          clearInterval(progressInterval);

          if (!response.ok) {
            throw new Error(`Failed to upload ${uploadFile.file.name}`);
          }

          const result = await response.json();
          
          // Update progress to 100%
          setUploadFiles(prev => prev.map(f => 
            f.id === uploadFile.id 
              ? { ...f, status: 'success', progress: 100, uploadedUrl: result.url }
              : f
          ));
          
          return {
            url: result.url,
            originalName: uploadFile.file.name,
            fileSize: uploadFile.file.size,
            mimeType: uploadFile.file.type,
            altText: uploadFile.altText || undefined,
            caption: uploadFile.caption || undefined,
          };
        } catch (error) {
          setUploadFiles(prev => prev.map(f => 
            f.id === uploadFile.id 
              ? { ...f, status: 'error', error: error instanceof Error ? error.message : 'Upload failed' }
              : f
          ));
          throw error;
        }
      });
      
      const uploadedImages = await Promise.all(uploadPromises);
      
      // Add images to product via tRPC
      await addImagesMutation.mutateAsync({
        productId,
        factoryId,
        images: uploadedImages,
      });
      
      // Clear upload files after successful upload
      setUploadFiles([]);
      
    } catch (error) {
      console.error('Upload error:', error);
      toast.error('Some uploads failed. Please try again.');
    } finally {
      setIsUploading(false);
    }
  }, [uploadFiles, productId, factoryId, addImagesMutation]);

  const handleRemoveUploadFile = useCallback((fileId: string) => {
    setUploadFiles(prev => {
      const file = prev.find(f => f.id === fileId);
      if (file?.preview) {
        URL.revokeObjectURL(file.preview);
      }
      return prev.filter(f => f.id !== fileId);
    });
  }, []);

  const handleDragEnd = useCallback(async (result: any) => {
    if (!result.destination) return;

    const items = Array.from(images);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update local state immediately for better UX
    const reorderedImages = items.map((item, index) => ({
      ...item,
      sortOrder: index
    }));
    setImages(reorderedImages);

    // Update on server
    try {
      await reorderImagesMutation.mutateAsync({
        productId,
        factoryId,
        imageIds: reorderedImages.map(img => img.id)
      });
    } catch (error) {
      // Revert on error
      setImages(images);
    }
  }, [images, productId, factoryId, reorderImagesMutation]);

  const handleEditMetadata = useCallback((image: ProductImage) => {
    setEditingImage(image.id);
    setEditMetadata({
      altText: image.altText || '',
      caption: image.caption || ''
    });
  }, []);

  const handleSaveMetadata = useCallback(async () => {
    if (!editingImage) return;

    try {
      await updateImageMutation.mutateAsync({
        productId,
        factoryId,
        imageId: editingImage,
        altText: editMetadata.altText,
        caption: editMetadata.caption
      });
    } catch (error) {
      console.error('Error updating metadata:', error);
    }
  }, [editingImage, editMetadata, productId, factoryId, updateImageMutation]);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin mr-2" />
          <span>Loading images...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <Tabs defaultValue="gallery" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="gallery">Gallery View</TabsTrigger>
          {showUpload && <TabsTrigger value="upload">Upload Images</TabsTrigger>}
        </TabsList>

        <TabsContent value="gallery" className="space-y-4">
          <EnhancedImageGallery
            productId={productId}
            factoryId={factoryId}
            images={images}
            onImageUpdate={setImages}
            showControls={true}
          />

          {/* Drag & Drop Reorder Interface */}
          {images.length > 1 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <GripVertical className="w-5 h-5 mr-2" />
                  Reorder Images
                </CardTitle>
              </CardHeader>
              <CardContent>
                <DragDropContext onDragEnd={handleDragEnd}>
                  <Droppable droppableId="images" direction="horizontal">
                    {(provided) => (
                      <div
                        {...provided.droppableProps}
                        ref={provided.innerRef}
                        className="flex space-x-2 overflow-x-auto pb-2"
                      >
                        {images.map((image, index) => (
                          <Draggable key={image.id} draggableId={image.id} index={index}>
                            {(provided, snapshot) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                className={`relative flex-shrink-0 w-20 h-20 bg-gray-100 rounded-lg overflow-hidden border-2 ${
                                  snapshot.isDragging ? 'border-blue-500 shadow-lg' : 'border-gray-200'
                                } ${image.isMain ? 'ring-2 ring-blue-500' : ''}`}
                              >
                                <img
                                  src={image.url}
                                  alt={image.altText || `Image ${index + 1}`}
                                  className="w-full h-full object-cover"
                                />
                                {image.isMain && (
                                  <div className="absolute top-1 left-1">
                                    <Star className="w-3 h-3 text-blue-500 fill-current" />
                                  </div>
                                )}
                                <div className="absolute bottom-1 right-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded">
                                  {index + 1}
                                </div>
                                {showMetadata && (
                                  <Button
                                    size="sm"
                                    variant="secondary"
                                    className="absolute top-1 right-1 h-5 w-5 p-0"
                                    onClick={() => handleEditMetadata(image)}
                                  >
                                    <Edit3 className="w-3 h-3" />
                                  </Button>
                                )}
                              </div>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {showUpload && (
          <TabsContent value="upload" className="space-y-4">
            {/* Upload Area */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Upload className="w-5 h-5 mr-2" />
                    Upload Images ({images.length}/{maxImages})
                  </div>
                  <Badge variant="outline">
                    Max {maxFileSize}MB per file
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div
                  {...getRootProps()}
                  className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                    isDragActive 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-300 hover:border-gray-400'
                  } ${isUploading || images.length >= maxImages ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  <input {...getInputProps()} />
                  <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                  <p className="text-lg font-medium text-gray-900 mb-2">
                    {isDragActive ? 'Drop images here' : 'Drag & drop images here'}
                  </p>
                  <p className="text-gray-500 mb-4">
                    or click to select files ({maxImages - images.length} remaining)
                  </p>
                  <Button variant="outline" disabled={isUploading || images.length >= maxImages}>
                    <Plus className="w-4 h-4 mr-2" />
                    Select Images
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Upload Queue */}
            {uploadFiles.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center">
                      <FileImage className="w-5 h-5 mr-2" />
                      Upload Queue ({uploadFiles.length} files)
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        onClick={handleUploadFiles}
                        disabled={isUploading}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        {isUploading ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Uploading...
                          </>
                        ) : (
                          <>
                            <Upload className="w-4 h-4 mr-2" />
                            Upload All
                          </>
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setUploadFiles([])}
                        disabled={isUploading}
                      >
                        Clear All
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {uploadFiles.map((uploadFile) => (
                      <div key={uploadFile.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                        <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                          <img
                            src={uploadFile.preview}
                            alt="Preview"
                            className="w-full h-full object-cover"
                          />
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {uploadFile.file.name}
                          </p>
                          <p className="text-sm text-gray-500">
                            {(uploadFile.file.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                          
                          {uploadFile.status === 'uploading' && (
                            <div className="mt-2">
                              <Progress value={uploadFile.progress} className="h-2" />
                              <p className="text-xs text-gray-500 mt-1">
                                {uploadFile.progress}% uploaded
                              </p>
                            </div>
                          )}
                          
                          {uploadFile.status === 'error' && (
                            <div className="mt-2 flex items-center text-red-600">
                              <AlertCircle className="w-4 h-4 mr-1" />
                              <span className="text-sm">{uploadFile.error}</span>
                            </div>
                          )}
                          
                          {uploadFile.status === 'success' && (
                            <div className="mt-2 flex items-center text-green-600">
                              <CheckCircle2 className="w-4 h-4 mr-1" />
                              <span className="text-sm">Upload complete</span>
                            </div>
                          )}
                        </div>

                        {showMetadata && uploadFile.status === 'pending' && (
                          <div className="flex-1 space-y-2">
                            <Input
                              placeholder="Alt text (for accessibility)"
                              value={uploadFile.altText || ''}
                              onChange={(e) => {
                                setUploadFiles(prev => prev.map(f => 
                                  f.id === uploadFile.id 
                                    ? { ...f, altText: e.target.value }
                                    : f
                                ));
                              }}
                              className="text-sm"
                            />
                            <Input
                              placeholder="Caption (optional)"
                              value={uploadFile.caption || ''}
                              onChange={(e) => {
                                setUploadFiles(prev => prev.map(f => 
                                  f.id === uploadFile.id 
                                    ? { ...f, caption: e.target.value }
                                    : f
                                ));
                              }}
                              className="text-sm"
                            />
                          </div>
                        )}
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveUploadFile(uploadFile.id)}
                          disabled={isUploading}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        )}
      </Tabs>

      {/* Metadata Edit Modal */}
      {editingImage && (
        <Card className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Edit Image Metadata</h3>
            <div className="space-y-4">
              <div>
                <Label htmlFor="altText">Alt Text (for accessibility)</Label>
                <Input
                  id="altText"
                  value={editMetadata.altText}
                  onChange={(e) => setEditMetadata(prev => ({ ...prev, altText: e.target.value }))}
                  placeholder="Describe the image for screen readers"
                />
              </div>
              <div>
                <Label htmlFor="caption">Caption</Label>
                <Textarea
                  id="caption"
                  value={editMetadata.caption}
                  onChange={(e) => setEditMetadata(prev => ({ ...prev, caption: e.target.value }))}
                  placeholder="Optional caption for the image"
                  rows={3}
                />
              </div>
            </div>
            <div className="flex justify-end space-x-2 mt-6">
              <Button variant="outline" onClick={() => setEditingImage(null)}>
                Cancel
              </Button>
              <Button onClick={handleSaveMetadata}>
                <Save className="w-4 h-4 mr-2" />
                Save
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}
