'use client';

import { useState, useCallback, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { 
  Star, 
  Edit3,
  Trash2,
  Image as ImageIcon,
  ZoomIn,
  Download,
  Eye,
  RotateCw,
  Maximize2,
  X,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { toast } from 'sonner';
import { trpc } from '../../lib/trpc';

interface ProductImage {
  id: string;
  url: string;
  isMain: boolean;
  sortOrder: number;
  originalName?: string;
  altText?: string;
  caption?: string;
  fileSize?: number;
  mimeType?: string;
  status: 'ACTIVE' | 'INACTIVE' | 'DELETED';
}

interface EnhancedImageGalleryProps {
  productId: string;
  factoryId: string;
  images: ProductImage[];
  onImageUpdate?: (images: ProductImage[]) => void;
  showControls?: boolean;
  className?: string;
}

export function EnhancedImageGallery({
  productId,
  factoryId,
  images,
  onImageUpdate,
  showControls = false,
  className = ''
}: EnhancedImageGalleryProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [rotation, setRotation] = useState(0);
  const [zoom, setZoom] = useState(1);

  // Filter active images and sort by sortOrder
  const activeImages = images
    .filter(img => img.status === 'ACTIVE')
    .sort((a, b) => a.sortOrder - b.sortOrder);

  const mainImage = activeImages.find(img => img.isMain) || activeImages[0];

  // tRPC mutations for image management
  const setMainImageMutation = trpc.imageManagement.setMainImage.useMutation({
    onSuccess: () => {
      toast.success('Main image updated successfully');
      onImageUpdate?.(images);
    },
    onError: (error) => {
      toast.error(`Failed to set main image: ${error.message}`);
    }
  });

  const deleteImageMutation = trpc.imageManagement.removeImage.useMutation({
    onSuccess: () => {
      toast.success('Image deleted successfully');
      onImageUpdate?.(images.filter(img => img.id !== activeImages[selectedImageIndex]?.id));
    },
    onError: (error) => {
      toast.error(`Failed to delete image: ${error.message}`);
    }
  });

  const handleSetMainImage = useCallback(async (imageId: string) => {
    try {
      await setMainImageMutation.mutateAsync({
        productId,
        factoryId,
        imageId
      });
    } catch (error) {
      console.error('Error setting main image:', error);
    }
  }, [productId, factoryId, setMainImageMutation]);

  const handleDeleteImage = useCallback(async (imageId: string) => {
    if (activeImages.length <= 1) {
      toast.error('Cannot delete the last image');
      return;
    }

    try {
      await deleteImageMutation.mutateAsync({
        productId,
        factoryId,
        imageId
      });
    } catch (error) {
      console.error('Error deleting image:', error);
    }
  }, [productId, factoryId, deleteImageMutation, activeImages.length]);

  const handleDownloadImage = useCallback((imageUrl: string, fileName?: string) => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = fileName || 'product-image.jpg';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }, []);

  const openLightbox = useCallback((index: number) => {
    setSelectedImageIndex(index);
    setIsLightboxOpen(true);
    setRotation(0);
    setZoom(1);
  }, []);

  const navigateImage = useCallback((direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setSelectedImageIndex(prev => prev > 0 ? prev - 1 : activeImages.length - 1);
    } else {
      setSelectedImageIndex(prev => prev < activeImages.length - 1 ? prev + 1 : 0);
    }
    setRotation(0);
    setZoom(1);
  }, [activeImages.length]);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!isLightboxOpen) return;
    
    switch (e.key) {
      case 'ArrowLeft':
        navigateImage('prev');
        break;
      case 'ArrowRight':
        navigateImage('next');
        break;
      case 'Escape':
        setIsLightboxOpen(false);
        break;
      case 'r':
        setRotation(prev => (prev + 90) % 360);
        break;
    }
  }, [isLightboxOpen, navigateImage]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  if (activeImages.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <ImageIcon className="w-16 h-16 text-gray-300 mb-4" />
          <p className="text-gray-500 text-lg">No images available</p>
          <p className="text-gray-400 text-sm">Upload images to see them here</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <ImageIcon className="w-5 h-5 mr-2" />
              Product Gallery ({activeImages.length} images)
            </div>
            {mainImage && (
              <Badge variant="secondary">
                Main: {mainImage.originalName || 'Image'}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Main Image Display */}
          <div className="mb-6">
            <div className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden group cursor-pointer">
              {mainImage && (
                <>
                  <img
                    src={mainImage.url}
                    alt={mainImage.altText || 'Product image'}
                    className="w-full h-full object-cover transition-transform group-hover:scale-105"
                    onClick={() => openLightbox(activeImages.findIndex(img => img.id === mainImage.id))}
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button size="sm" variant="secondary" className="mr-2">
                        <ZoomIn className="w-4 h-4 mr-1" />
                        View
                      </Button>
                      {showControls && (
                        <Button size="sm" variant="secondary">
                          <Download className="w-4 h-4 mr-1" />
                          Download
                        </Button>
                      )}
                    </div>
                  </div>
                </>
              )}
            </div>
            {mainImage?.caption && (
              <p className="text-sm text-gray-600 mt-2 text-center">{mainImage.caption}</p>
            )}
          </div>

          {/* Thumbnail Grid */}
          {activeImages.length > 1 && (
            <div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2">
              {activeImages.map((image, index) => (
                <div
                  key={image.id}
                  className={`relative aspect-square bg-gray-100 rounded-md overflow-hidden cursor-pointer border-2 transition-all ${
                    image.isMain ? 'border-blue-500' : 'border-transparent hover:border-gray-300'
                  }`}
                  onClick={() => openLightbox(index)}
                >
                  <img
                    src={image.url}
                    alt={image.altText || `Product image ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                  {image.isMain && (
                    <div className="absolute top-1 left-1">
                      <Star className="w-3 h-3 text-blue-500 fill-current" />
                    </div>
                  )}
                  {showControls && (
                    <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center">
                      <div className="opacity-0 hover:opacity-100 transition-opacity flex space-x-1">
                        {!image.isMain && (
                          <Button
                            size="sm"
                            variant="secondary"
                            className="h-6 w-6 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleSetMainImage(image.id);
                            }}
                          >
                            <Star className="w-3 h-3" />
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="destructive"
                          className="h-6 w-6 p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteImage(image.id);
                          }}
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Lightbox Modal */}
      <Dialog open={isLightboxOpen} onOpenChange={setIsLightboxOpen}>
        <DialogContent className="max-w-6xl w-full h-[90vh] p-0">
          <div className="relative w-full h-full bg-black">
            {/* Close Button */}
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-4 right-4 z-10 text-white hover:bg-white/20"
              onClick={() => setIsLightboxOpen(false)}
            >
              <X className="w-4 h-4" />
            </Button>

            {/* Navigation Buttons */}
            {activeImages.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute left-4 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20"
                  onClick={() => navigateImage('prev')}
                >
                  <ChevronLeft className="w-6 h-6" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-4 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20"
                  onClick={() => navigateImage('next')}
                >
                  <ChevronRight className="w-6 h-6" />
                </Button>
              </>
            )}

            {/* Image Controls */}
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-10 flex space-x-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setRotation(prev => (prev + 90) % 360)}
              >
                <RotateCw className="w-4 h-4 mr-1" />
                Rotate
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setZoom(prev => prev === 1 ? 2 : 1)}
              >
                <Maximize2 className="w-4 h-4 mr-1" />
                {zoom === 1 ? 'Zoom In' : 'Zoom Out'}
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={() => handleDownloadImage(
                  activeImages[selectedImageIndex]?.url,
                  activeImages[selectedImageIndex]?.originalName
                )}
              >
                <Download className="w-4 h-4 mr-1" />
                Download
              </Button>
            </div>

            {/* Main Image */}
            <div className="w-full h-full flex items-center justify-center p-8">
              {activeImages[selectedImageIndex] && (
                <img
                  src={activeImages[selectedImageIndex].url}
                  alt={activeImages[selectedImageIndex].altText || 'Product image'}
                  className="max-w-full max-h-full object-contain transition-transform duration-200"
                  style={{
                    transform: `rotate(${rotation}deg) scale(${zoom})`,
                  }}
                />
              )}
            </div>

            {/* Image Info */}
            <div className="absolute bottom-4 left-4 z-10 text-white text-sm">
              <p>{selectedImageIndex + 1} of {activeImages.length}</p>
              {activeImages[selectedImageIndex]?.originalName && (
                <p className="opacity-75">{activeImages[selectedImageIndex].originalName}</p>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
