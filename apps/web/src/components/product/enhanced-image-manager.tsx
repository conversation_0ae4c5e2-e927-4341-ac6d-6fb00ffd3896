'use client';

import { useState, useCallback, useEffect } from 'react';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { 
  Star, 
  GripVertical, 
  Edit3,
  Trash2,
  Image as ImageIcon,
  Plus,
  Loader2,
  AlertCircle,
  CheckCircle2
} from 'lucide-react';
import { toast } from 'sonner';
import { trpc } from '../../lib/trpc';
import { AdvancedImageUpload } from './advanced-image-upload';

interface ProductImage {
  id: string;
  url: string;
  isMain: boolean;
  sortOrder: number;
  originalName?: string;
  altText?: string;
  caption?: string;
  fileSize?: number;
  mimeType?: string;
  status: string;
  uploadedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface EnhancedImageManagerProps {
  productId: string;
  factoryId: string;
  maxImages?: number;
  maxFileSize?: number; // in MB
  showUpload?: boolean;
  showMetadata?: boolean;
  onImagesChange?: (images: ProductImage[]) => void;
}

export function EnhancedImageManager({
  productId,
  factoryId,
  maxImages = 10,
  maxFileSize = 5,
  showUpload = true,
  showMetadata = true,
  onImagesChange
}: EnhancedImageManagerProps) {
  const [images, setImages] = useState<ProductImage[]>([]);
  const [editingAlt, setEditingAlt] = useState<string | null>(null);
  const [altTexts, setAltTexts] = useState<Record<string, string>>({});
  const [captions, setCaptions] = useState<Record<string, string>>({});

  // tRPC queries and mutations
  const { data: productImages, refetch: refetchImages, isLoading } = trpc.imageManagement.getProductImages.useQuery({
    productId,
    factoryId
  });

  const reorderImagesMutation = trpc.imageManagement.reorderImages.useMutation({
    onSuccess: () => {
      toast.success('Images reordered successfully');
      refetchImages();
    },
    onError: (error) => {
      toast.error(`Failed to reorder images: ${error.message}`);
    }
  });

  const setMainImageMutation = trpc.imageManagement.setMainImage.useMutation({
    onSuccess: () => {
      toast.success('Main image updated');
      refetchImages();
    },
    onError: (error) => {
      toast.error(`Failed to set main image: ${error.message}`);
    }
  });

  const removeImageMutation = trpc.imageManagement.removeImage.useMutation({
    onSuccess: () => {
      toast.success('Image removed successfully');
      refetchImages();
    },
    onError: (error) => {
      toast.error(`Failed to remove image: ${error.message}`);
    }
  });

  const updateAltTextMutation = trpc.imageManagement.updateAltText.useMutation({
    onSuccess: () => {
      toast.success('Alt text updated');
      refetchImages();
      setEditingAlt(null);
    },
    onError: (error) => {
      toast.error(`Failed to update alt text: ${error.message}`);
    }
  });

  // Update local state when data changes
  useEffect(() => {
    if (productImages?.images) {
      setImages(productImages.images);
      onImagesChange?.(productImages.images);
    }
  }, [productImages, onImagesChange]);

  // Handle drag and drop reordering
  const handleDragEnd = useCallback(async (result: any) => {
    if (!result.destination) return;

    const reorderedImages = Array.from(images);
    const [removed] = reorderedImages.splice(result.source.index, 1);
    reorderedImages.splice(result.destination.index, 0, removed);

    // Update local state immediately for better UX
    setImages(reorderedImages);

    // Update sort orders
    const imageOrder = reorderedImages.map((img, index) => ({
      imageId: img.id,
      sortOrder: index,
    }));

    try {
      await reorderImagesMutation.mutateAsync({
        productId,
        factoryId,
        imageOrder,
      });
    } catch (error) {
      // Revert local state on error
      setImages(images);
    }
  }, [images, productId, factoryId, reorderImagesMutation]);

  // Set main image
  const handleSetMainImage = useCallback(async (imageId: string) => {
    await setMainImageMutation.mutateAsync({
      productId,
      factoryId,
      imageId,
    });
  }, [productId, factoryId, setMainImageMutation]);

  // Remove image
  const handleRemoveImage = useCallback(async (imageId: string) => {
    if (!confirm('Are you sure you want to remove this image?')) return;

    await removeImageMutation.mutateAsync({
      productId,
      factoryId,
      imageId,
    });
  }, [productId, factoryId, removeImageMutation]);

  // Update alt text
  const handleUpdateAltText = useCallback(async (imageId: string) => {
    const altText = altTexts[imageId] || '';
    const caption = captions[imageId];

    await updateAltTextMutation.mutateAsync({
      productId,
      factoryId,
      imageId,
      altText,
      caption,
    });
  }, [productId, factoryId, altTexts, captions, updateAltTextMutation]);

  // Handle new images uploaded
  const handleImagesUploaded = useCallback(() => {
    refetchImages();
  }, [refetchImages]);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          <span>Loading images...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Upload Component */}
      {showUpload && (
        <AdvancedImageUpload
          productId={productId}
          factoryId={factoryId}
          onImagesUploaded={handleImagesUploaded}
          maxImages={maxImages}
          maxFileSize={maxFileSize}
          showMetadataFields={showMetadata}
        />
      )}

      {/* Current Images */}
      {images.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <ImageIcon className="w-5 h-5 mr-2" />
                Current Images ({images.length}/{maxImages})
              </div>
              <Badge variant="outline">
                Drag to reorder
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="images" direction="horizontal">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
                  >
                    {images.map((image, index) => (
                      <Draggable key={image.id} draggableId={image.id} index={index}>
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            className={`relative group ${
                              snapshot.isDragging ? 'z-50' : ''
                            }`}
                          >
                            <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                              <img
                                src={image.url}
                                alt={image.altText || `Product image ${index + 1}`}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                  const parent = target.parentElement;
                                  if (parent) {
                                    parent.innerHTML = `
                                      <div class="w-full h-full flex items-center justify-center">
                                        <div class="text-center text-gray-400">
                                          <svg class="w-12 h-12 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                                          </svg>
                                          <p class="text-sm">Image not available</p>
                                        </div>
                                      </div>
                                    `;
                                  }
                                }}
                              />
                            </div>
                            
                            {/* Image Controls */}
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 rounded-lg">
                              <div className="absolute top-2 left-2 flex space-x-1">
                                {image.isMain && (
                                  <Badge className="bg-blue-600 text-white text-xs">
                                    Main
                                  </Badge>
                                )}
                                {image.status !== 'ACTIVE' && (
                                  <Badge variant="secondary" className="text-xs">
                                    {image.status}
                                  </Badge>
                                )}
                              </div>
                              
                              <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                <div
                                  {...provided.dragHandleProps}
                                  className="p-1 bg-white rounded cursor-move"
                                >
                                  <GripVertical className="w-4 h-4" />
                                </div>
                              </div>

                              <div className="absolute bottom-2 left-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                <div className="flex justify-between">
                                  <div className="flex space-x-1">
                                    {!image.isMain && (
                                      <Button
                                        size="sm"
                                        variant="secondary"
                                        onClick={() => handleSetMainImage(image.id)}
                                        className="h-8 px-2"
                                        disabled={setMainImageMutation.isLoading}
                                      >
                                        <Star className="w-3 h-3" />
                                      </Button>
                                    )}
                                    {showMetadata && (
                                      <Button
                                        size="sm"
                                        variant="secondary"
                                        onClick={() => {
                                          setEditingAlt(image.id);
                                          setAltTexts(prev => ({
                                            ...prev,
                                            [image.id]: image.altText || ''
                                          }));
                                          setCaptions(prev => ({
                                            ...prev,
                                            [image.id]: image.caption || ''
                                          }));
                                        }}
                                        className="h-8 px-2"
                                      >
                                        <Edit3 className="w-3 h-3" />
                                      </Button>
                                    )}
                                  </div>
                                  <Button
                                    size="sm"
                                    variant="destructive"
                                    onClick={() => handleRemoveImage(image.id)}
                                    className="h-8 px-2"
                                    disabled={removeImageMutation.isLoading}
                                  >
                                    <Trash2 className="w-3 h-3" />
                                  </Button>
                                </div>
                              </div>
                            </div>

                            {/* Image Info */}
                            {showMetadata && (
                              <div className="mt-2 text-xs text-gray-600">
                                <p className="truncate">{image.originalName || 'Unknown'}</p>
                                {image.fileSize && (
                                  <p>{(image.fileSize / 1024 / 1024).toFixed(2)} MB</p>
                                )}
                              </div>
                            )}
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </CardContent>
        </Card>
      )}

      {/* Alt Text Editing Modal */}
      {editingAlt && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Edit Image Metadata</h3>

            <div className="space-y-4">
              <div>
                <Label htmlFor="altText">Alt Text (for accessibility)</Label>
                <Input
                  id="altText"
                  value={altTexts[editingAlt] || ''}
                  onChange={(e) => setAltTexts(prev => ({
                    ...prev,
                    [editingAlt]: e.target.value
                  }))}
                  placeholder="Describe this image for screen readers..."
                  className="mt-1"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Helps visually impaired users understand the image content
                </p>
              </div>

              <div>
                <Label htmlFor="caption">Caption (optional)</Label>
                <Input
                  id="caption"
                  value={captions[editingAlt] || ''}
                  onChange={(e) => setCaptions(prev => ({
                    ...prev,
                    [editingAlt]: e.target.value
                  }))}
                  placeholder="Image caption or description..."
                  className="mt-1"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Optional caption displayed with the image
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setEditingAlt(null)}
                disabled={updateAltTextMutation.isLoading}
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleUpdateAltText(editingAlt)}
                disabled={updateAltTextMutation.isLoading}
              >
                {updateAltTextMutation.isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  'Save Changes'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Empty State */}
      {images.length === 0 && !showUpload && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <ImageIcon className="w-12 h-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No images yet</h3>
            <p className="text-gray-600 text-center">
              This product doesn't have any images. Upload some images to get started.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
