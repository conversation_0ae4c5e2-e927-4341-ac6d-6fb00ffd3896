'use client';

import React, { forwardRef, useState } from 'react';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, CheckCircle, Loader2 } from 'lucide-react';

// Base validation status interface
export interface ValidationStatus {
  isValid?: boolean;
  isInvalid?: boolean;
  isPending?: boolean;
  message?: string;
}

// Common props for all validated components
interface BaseValidatedProps {
  label?: string;
  description?: string;
  required?: boolean;
  validation?: ValidationStatus;
  className?: string;
  disabled?: boolean;
}

// Validated Input Component
interface ValidatedInputProps extends BaseValidatedProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
  value: string | number;
  onChange: (value: string | number) => void;
  onBlur?: () => void;
  placeholder?: string;
  min?: number;
  max?: number;
  step?: number;
  maxLength?: number;
  pattern?: string;
  autoComplete?: string;
  id?: string;
}

export const ValidatedInput = forwardRef<HTMLInputElement, ValidatedInputProps>(
  ({ 
    label, 
    description, 
    required, 
    validation, 
    className, 
    disabled,
    type = 'text',
    value,
    onChange,
    onBlur,
    placeholder,
    min,
    max,
    step,
    maxLength,
    pattern,
    autoComplete,
    id,
    ...props 
  }, ref) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
    const hasError = validation?.isInvalid;
    const isValid = validation?.isValid;
    const isPending = validation?.isPending;

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = type === 'number' ? parseFloat(e.target.value) || 0 : e.target.value;
      onChange(newValue);
    };

    return (
      <div className={cn('space-y-2', className)}>
        {label && (
          <Label htmlFor={inputId} className={cn(hasError && 'text-destructive')}>
            {label}
            {required && <span className="text-destructive ml-1">*</span>}
          </Label>
        )}
        
        <div className="relative">
          <Input
            ref={ref}
            id={inputId}
            type={type}
            value={value}
            onChange={handleChange}
            onBlur={onBlur}
            placeholder={placeholder}
            disabled={disabled || isPending}
            min={min}
            max={max}
            step={step}
            maxLength={maxLength}
            pattern={pattern}
            autoComplete={autoComplete}
            className={cn(
              hasError && 'border-destructive focus-visible:ring-destructive',
              isValid && 'border-green-500 focus-visible:ring-green-500',
              isPending && 'opacity-50'
            )}
            {...props}
          />
          
          {/* Validation status icon */}
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {isPending && <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />}
            {isValid && <CheckCircle className="h-4 w-4 text-green-500" />}
            {hasError && <AlertCircle className="h-4 w-4 text-destructive" />}
          </div>
        </div>

        {/* Description */}
        {description && !validation?.message && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}

        {/* Validation message */}
        {validation?.message && (
          <p className={cn(
            'text-sm',
            hasError ? 'text-destructive' : 'text-muted-foreground'
          )}>
            {validation.message}
          </p>
        )}
      </div>
    );
  }
);

ValidatedInput.displayName = 'ValidatedInput';

// Validated Textarea Component
interface ValidatedTextareaProps extends BaseValidatedProps {
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  placeholder?: string;
  rows?: number;
  maxLength?: number;
  id?: string;
}

export const ValidatedTextarea = forwardRef<HTMLTextAreaElement, ValidatedTextareaProps>(
  ({ 
    label, 
    description, 
    required, 
    validation, 
    className, 
    disabled,
    value,
    onChange,
    onBlur,
    placeholder,
    rows = 3,
    maxLength,
    id,
    ...props 
  }, ref) => {
    const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;
    const hasError = validation?.isInvalid;
    const isValid = validation?.isValid;
    const isPending = validation?.isPending;

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      onChange(e.target.value);
    };

    return (
      <div className={cn('space-y-2', className)}>
        {label && (
          <Label htmlFor={textareaId} className={cn(hasError && 'text-destructive')}>
            {label}
            {required && <span className="text-destructive ml-1">*</span>}
          </Label>
        )}
        
        <div className="relative">
          <Textarea
            ref={ref}
            id={textareaId}
            value={value}
            onChange={handleChange}
            onBlur={onBlur}
            placeholder={placeholder}
            disabled={disabled || isPending}
            rows={rows}
            maxLength={maxLength}
            className={cn(
              hasError && 'border-destructive focus-visible:ring-destructive',
              isValid && 'border-green-500 focus-visible:ring-green-500',
              isPending && 'opacity-50'
            )}
            {...props}
          />
          
          {/* Character count */}
          {maxLength && (
            <div className="absolute bottom-2 right-2">
              <Badge variant="outline" className="text-xs">
                {value.length}/{maxLength}
              </Badge>
            </div>
          )}
        </div>

        {/* Description */}
        {description && !validation?.message && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}

        {/* Validation message */}
        {validation?.message && (
          <p className={cn(
            'text-sm',
            hasError ? 'text-destructive' : 'text-muted-foreground'
          )}>
            {validation.message}
          </p>
        )}
      </div>
    );
  }
);

ValidatedTextarea.displayName = 'ValidatedTextarea';

// Validated Select Component
interface ValidatedSelectProps extends BaseValidatedProps {
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  placeholder?: string;
  options: Array<{ value: string; label: string; disabled?: boolean }>;
  id?: string;
}

export const ValidatedSelect = forwardRef<HTMLButtonElement, ValidatedSelectProps>(
  ({ 
    label, 
    description, 
    required, 
    validation, 
    className, 
    disabled,
    value,
    onChange,
    onBlur,
    placeholder = 'Select an option...',
    options,
    id,
    ...props 
  }, ref) => {
    const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;
    const hasError = validation?.isInvalid;
    const isValid = validation?.isValid;
    const isPending = validation?.isPending;

    return (
      <div className={cn('space-y-2', className)}>
        {label && (
          <Label htmlFor={selectId} className={cn(hasError && 'text-destructive')}>
            {label}
            {required && <span className="text-destructive ml-1">*</span>}
          </Label>
        )}
        
        <div className="relative">
          <Select
            value={value}
            onValueChange={onChange}
            disabled={disabled || isPending}
            {...props}
          >
            <SelectTrigger
              ref={ref}
              id={selectId}
              className={cn(
                hasError && 'border-destructive focus:ring-destructive',
                isValid && 'border-green-500 focus:ring-green-500',
                isPending && 'opacity-50'
              )}
              onBlur={onBlur}
            >
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              {options.map((option) => (
                <SelectItem 
                  key={option.value} 
                  value={option.value}
                  disabled={option.disabled}
                >
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {/* Validation status icon */}
          <div className="absolute right-8 top-1/2 transform -translate-y-1/2 pointer-events-none">
            {isPending && <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />}
            {isValid && <CheckCircle className="h-4 w-4 text-green-500" />}
            {hasError && <AlertCircle className="h-4 w-4 text-destructive" />}
          </div>
        </div>

        {/* Description */}
        {description && !validation?.message && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}

        {/* Validation message */}
        {validation?.message && (
          <p className={cn(
            'text-sm',
            hasError ? 'text-destructive' : 'text-muted-foreground'
          )}>
            {validation.message}
          </p>
        )}
      </div>
    );
  }
);

ValidatedSelect.displayName = 'ValidatedSelect';

// Validated Checkbox Component
interface ValidatedCheckboxProps extends BaseValidatedProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  onBlur?: () => void;
  id?: string;
}

export const ValidatedCheckbox = forwardRef<HTMLButtonElement, ValidatedCheckboxProps>(
  ({ 
    label, 
    description, 
    required, 
    validation, 
    className, 
    disabled,
    checked,
    onChange,
    onBlur,
    id,
    ...props 
  }, ref) => {
    const checkboxId = id || `checkbox-${Math.random().toString(36).substr(2, 9)}`;
    const hasError = validation?.isInvalid;
    const isValid = validation?.isValid;
    const isPending = validation?.isPending;

    return (
      <div className={cn('space-y-2', className)}>
        <div className="flex items-center space-x-2">
          <Checkbox
            ref={ref}
            id={checkboxId}
            checked={checked}
            onCheckedChange={onChange}
            disabled={disabled || isPending}
            onBlur={onBlur}
            className={cn(
              hasError && 'border-destructive',
              isValid && 'border-green-500'
            )}
            {...props}
          />
          
          {label && (
            <Label 
              htmlFor={checkboxId} 
              className={cn(
                'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
                hasError && 'text-destructive'
              )}
            >
              {label}
              {required && <span className="text-destructive ml-1">*</span>}
            </Label>
          )}
          
          {/* Validation status icon */}
          {isPending && <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />}
          {isValid && <CheckCircle className="h-4 w-4 text-green-500" />}
          {hasError && <AlertCircle className="h-4 w-4 text-destructive" />}
        </div>

        {/* Description */}
        {description && !validation?.message && (
          <p className="text-sm text-muted-foreground ml-6">{description}</p>
        )}

        {/* Validation message */}
        {validation?.message && (
          <p className={cn(
            'text-sm ml-6',
            hasError ? 'text-destructive' : 'text-muted-foreground'
          )}>
            {validation.message}
          </p>
        )}
      </div>
    );
  }
);

ValidatedCheckbox.displayName = 'ValidatedCheckbox';
