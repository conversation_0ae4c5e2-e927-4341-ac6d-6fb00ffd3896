'use client';

import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  AlertTriangle, 
  CheckCircle, 
  Info, 
  X, 
  ChevronDown, 
  ChevronRight,
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react';

// Validation error types
export interface ValidationError {
  field: string;
  message: string;
  code?: string;
  severity?: 'error' | 'warning' | 'info';
}

export interface ValidationSummary {
  isValid: boolean;
  errorCount: number;
  warningCount: number;
  errors: ValidationError[];
  warnings: ValidationError[];
}

// Props for ValidationErrorHandler
interface ValidationErrorHandlerProps {
  errors: Record<string, string>;
  warnings?: Record<string, string>;
  className?: string;
  showSummary?: boolean;
  showDetails?: boolean;
  collapsible?: boolean;
  onRetry?: () => void;
  onClearErrors?: () => void;
  maxDisplayErrors?: number;
}

export function ValidationErrorHandler({
  errors,
  warnings = {},
  className,
  showSummary = true,
  showDetails = true,
  collapsible = false,
  onRetry,
  onClearErrors,
  maxDisplayErrors = 10
}: ValidationErrorHandlerProps) {
  const [isOpen, setIsOpen] = React.useState(!collapsible);
  const [showAllErrors, setShowAllErrors] = React.useState(false);

  const errorEntries = Object.entries(errors);
  const warningEntries = Object.entries(warnings);
  const totalErrors = errorEntries.length;
  const totalWarnings = warningEntries.length;
  const hasErrors = totalErrors > 0;
  const hasWarnings = totalWarnings > 0;
  const hasIssues = hasErrors || hasWarnings;

  // Determine which errors to display
  const displayErrors = showAllErrors 
    ? errorEntries 
    : errorEntries.slice(0, maxDisplayErrors);
  const hiddenErrorCount = errorEntries.length - displayErrors.length;

  if (!hasIssues) {
    return null;
  }

  const ValidationSummaryBadge = () => (
    <div className="flex items-center gap-2">
      {hasErrors && (
        <Badge variant="destructive" className="flex items-center gap-1">
          <AlertTriangle className="w-3 h-3" />
          {totalErrors} Error{totalErrors !== 1 ? 's' : ''}
        </Badge>
      )}
      {hasWarnings && (
        <Badge variant="outline" className="flex items-center gap-1 text-orange-600 border-orange-600">
          <Info className="w-3 h-3" />
          {totalWarnings} Warning{totalWarnings !== 1 ? 's' : ''}
        </Badge>
      )}
    </div>
  );

  const ErrorList = ({ entries, severity }: { entries: [string, string][]; severity: 'error' | 'warning' }) => (
    <ul className="space-y-2">
      {entries.map(([field, message], index) => (
        <li key={`${field}-${index}`} className="flex items-start gap-2">
          <div className={`mt-0.5 ${severity === 'error' ? 'text-destructive' : 'text-orange-600'}`}>
            {severity === 'error' ? (
              <AlertTriangle className="w-4 h-4" />
            ) : (
              <Info className="w-4 h-4" />
            )}
          </div>
          <div className="flex-1 min-w-0">
            <div className="font-medium text-sm capitalize">
              {field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </div>
            <div className={`text-sm ${severity === 'error' ? 'text-destructive' : 'text-orange-600'}`}>
              {message}
            </div>
          </div>
        </li>
      ))}
    </ul>
  );

  const content = (
    <div className="space-y-4">
      {/* Summary */}
      {showSummary && (
        <div className="flex items-center justify-between">
          <ValidationSummaryBadge />
          <div className="flex items-center gap-2">
            {onRetry && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRetry}
                className="h-8"
              >
                <RefreshCw className="w-3 h-3 mr-1" />
                Retry
              </Button>
            )}
            {onClearErrors && (
              <Button
                variant="outline"
                size="sm"
                onClick={onClearErrors}
                className="h-8"
              >
                <X className="w-3 h-3 mr-1" />
                Clear
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Error Details */}
      {showDetails && (
        <div className="space-y-4">
          {/* Errors */}
          {hasErrors && (
            <div>
              <h4 className="font-medium text-destructive mb-2 flex items-center gap-2">
                <AlertTriangle className="w-4 h-4" />
                Validation Errors
              </h4>
              <ErrorList entries={displayErrors} severity="error" />
              
              {/* Show more/less toggle */}
              {hiddenErrorCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAllErrors(!showAllErrors)}
                  className="mt-2 h-8 text-xs"
                >
                  {showAllErrors ? (
                    <>
                      <EyeOff className="w-3 h-3 mr-1" />
                      Show Less
                    </>
                  ) : (
                    <>
                      <Eye className="w-3 h-3 mr-1" />
                      Show {hiddenErrorCount} More Error{hiddenErrorCount !== 1 ? 's' : ''}
                    </>
                  )}
                </Button>
              )}
            </div>
          )}

          {/* Warnings */}
          {hasWarnings && (
            <div>
              <h4 className="font-medium text-orange-600 mb-2 flex items-center gap-2">
                <Info className="w-4 h-4" />
                Warnings
              </h4>
              <ErrorList entries={warningEntries} severity="warning" />
            </div>
          )}
        </div>
      )}
    </div>
  );

  if (collapsible) {
    return (
      <Collapsible open={isOpen} onOpenChange={setIsOpen} className={className}>
        <CollapsibleTrigger asChild>
          <Button variant="outline" className="w-full justify-between">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-4 h-4 text-destructive" />
              <span>Validation Issues</span>
              <ValidationSummaryBadge />
            </div>
            {isOpen ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <Card className="mt-2">
            <CardContent className="pt-4">
              {content}
            </CardContent>
          </Card>
        </CollapsibleContent>
      </Collapsible>
    );
  }

  return (
    <Alert className={className} variant={hasErrors ? "destructive" : "default"}>
      {content}
    </Alert>
  );
}

// Inline field validation error component
interface FieldValidationErrorProps {
  error?: string;
  warning?: string;
  className?: string;
  showIcon?: boolean;
}

export function FieldValidationError({
  error,
  warning,
  className,
  showIcon = true
}: FieldValidationErrorProps) {
  const message = error || warning;
  const isError = Boolean(error);
  
  if (!message) {
    return null;
  }

  return (
    <div className={`flex items-center gap-1 text-sm ${
      isError ? 'text-destructive' : 'text-orange-600'
    } ${className}`}>
      {showIcon && (
        isError ? (
          <AlertTriangle className="w-3 h-3 flex-shrink-0" />
        ) : (
          <Info className="w-3 h-3 flex-shrink-0" />
        )
      )}
      <span>{message}</span>
    </div>
  );
}

// Validation status indicator component
interface ValidationStatusIndicatorProps {
  isValid: boolean;
  errorCount: number;
  warningCount: number;
  isValidating?: boolean;
  className?: string;
}

export function ValidationStatusIndicator({
  isValid,
  errorCount,
  warningCount,
  isValidating = false,
  className
}: ValidationStatusIndicatorProps) {
  if (isValidating) {
    return (
      <Badge variant="outline" className={`${className} animate-pulse`}>
        <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
        Validating...
      </Badge>
    );
  }

  if (isValid && errorCount === 0 && warningCount === 0) {
    return (
      <Badge variant="outline" className={`${className} text-green-600 border-green-600`}>
        <CheckCircle className="w-3 h-3 mr-1" />
        Valid
      </Badge>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {errorCount > 0 && (
        <Badge variant="destructive">
          <AlertTriangle className="w-3 h-3 mr-1" />
          {errorCount} Error{errorCount !== 1 ? 's' : ''}
        </Badge>
      )}
      {warningCount > 0 && (
        <Badge variant="outline" className="text-orange-600 border-orange-600">
          <Info className="w-3 h-3 mr-1" />
          {warningCount} Warning{warningCount !== 1 ? 's' : ''}
        </Badge>
      )}
    </div>
  );
}

// Form validation summary component
interface FormValidationSummaryProps {
  errors: Record<string, string>;
  warnings?: Record<string, string>;
  isValidating?: boolean;
  className?: string;
}

export function FormValidationSummary({
  errors,
  warnings = {},
  isValidating = false,
  className
}: FormValidationSummaryProps) {
  const errorCount = Object.keys(errors).length;
  const warningCount = Object.keys(warnings).length;
  const isValid = errorCount === 0;

  return (
    <div className={`flex items-center justify-between p-3 bg-muted/50 rounded-lg ${className}`}>
      <ValidationStatusIndicator
        isValid={isValid}
        errorCount={errorCount}
        warningCount={warningCount}
        isValidating={isValidating}
      />
      
      {(errorCount > 0 || warningCount > 0) && (
        <div className="text-sm text-muted-foreground">
          {errorCount > 0 && `${errorCount} field${errorCount !== 1 ? 's' : ''} need${errorCount === 1 ? 's' : ''} attention`}
          {errorCount > 0 && warningCount > 0 && ', '}
          {warningCount > 0 && `${warningCount} warning${warningCount !== 1 ? 's' : ''}`}
        </div>
      )}
    </div>
  );
}

// Utility functions for validation error handling
export const validationUtils = {
  // Group errors by category/prefix
  groupErrorsByCategory: (errors: Record<string, string>) => {
    const grouped: Record<string, Record<string, string>> = {};
    
    Object.entries(errors).forEach(([field, message]) => {
      const category = field.split('.')[0] || 'general';
      if (!grouped[category]) {
        grouped[category] = {};
      }
      grouped[category][field] = message;
    });
    
    return grouped;
  },

  // Get first error message
  getFirstError: (errors: Record<string, string>): string | null => {
    const firstKey = Object.keys(errors)[0];
    return firstKey ? errors[firstKey] : null;
  },

  // Check if specific field has error
  hasFieldError: (errors: Record<string, string>, field: string): boolean => {
    return Boolean(errors[field]);
  },

  // Get error for specific field
  getFieldError: (errors: Record<string, string>, field: string): string | undefined => {
    return errors[field];
  },

  // Count errors by severity
  countErrorsBySeverity: (errors: ValidationError[]) => {
    return errors.reduce(
      (acc, error) => {
        const severity = error.severity || 'error';
        acc[severity] = (acc[severity] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );
  },
};
