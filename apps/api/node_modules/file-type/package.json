{"name": "file-type", "version": "21.0.0", "description": "Detect the file type of a file, stream, or data", "license": "MIT", "repository": "sindresorhus/file-type", "funding": "https://github.com/sindresorhus/file-type?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {".": {"node": {"types": "./index.d.ts", "import": "./index.js", "module-sync": "./index.js"}, "default": {"types": "./core.d.ts", "import": "./core.js", "module-sync": "./core.js"}}, "./core": {"types": "./core.d.ts", "default": "./core.js"}, "./node": {"types": "./index.d.ts", "default": "./index.js"}}, "sideEffects": false, "engines": {"node": ">=20"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "core.js", "core.d.ts", "supported.js", "util.js"], "keywords": ["mime", "file", "type", "magic", "archive", "image", "img", "pic", "picture", "flash", "photo", "video", "detect", "check", "is", "exif", "elf", "macho", "exe", "binary", "buffer", "uint8array", "jpg", "png", "apng", "gif", "webp", "flif", "xcf", "cr2", "cr3", "orf", "arw", "dng", "nef", "rw2", "raf", "tif", "bmp", "icns", "jxr", "psd", "indd", "zip", "tar", "rar", "gz", "bz2", "7z", "dmg", "mp4", "mid", "mkv", "webm", "mov", "avi", "mpg", "mp2", "mp3", "m4a", "ogg", "opus", "flac", "wav", "amr", "pdf", "epub", "mobi", "swf", "rtf", "woff", "woff2", "eot", "ttf", "otf", "ttc", "ico", "flv", "ps", "xz", "sqlite", "xpi", "cab", "deb", "ar", "rpm", "Z", "lz", "cfb", "mxf", "mts", "wasm", "webassembly", "blend", "bpg", "docx", "pptx", "xlsx", "3gp", "j2c", "jp2", "jpm", "jpx", "mj2", "aif", "odt", "ods", "odp", "xml", "heic", "ics", "glb", "pcap", "dsf", "lnk", "alias", "voc", "ac3", "3g2", "m4b", "m4p", "m4v", "f4a", "f4b", "f4p", "f4v", "mie", "qcp", "asf", "ogv", "ogm", "oga", "spx", "ogx", "ape", "wv", "cur", "nes", "crx", "ktx", "dcm", "mpc", "arrow", "shp", "aac", "mp1", "it", "s3m", "xm", "skp", "avif", "eps", "lzh", "pgp", "asar", "stl", "chm", "3mf", "zst", "jxl", "vcf", "jls", "pst", "dwg", "parquet", "class", "arj", "cpio", "ace", "avro", "icc", "fbx", "vsdx", "vtt", "apk", "drc", "lz4", "potx", "xltx", "dotx", "xltm", "ots", "odg", "otg", "otp", "ott", "xlsm", "docm", "dotm", "potm", "pptm", "jar", "rm", "ppsm", "ppsx"], "dependencies": {"@tokenizer/inflate": "^0.2.7", "strtok3": "^10.2.2", "token-types": "^6.0.0", "uint8array-extras": "^1.4.0"}, "devDependencies": {"@tokenizer/token": "^0.3.0", "@types/node": "^22.15.21", "ava": "^6.3.0", "commonmark": "^0.31.2", "get-stream": "^9.0.1", "noop-stream": "^1.0.0", "tsd": "^0.32.0", "xo": "^0.60.0"}, "xo": {"envs": ["node", "browser"], "ignores": ["fixture"], "rules": {"no-inner-declarations": "warn", "no-await-in-loop": "warn", "no-bitwise": "off", "@typescript-eslint/no-unsafe-assignment": "off", "unicorn/text-encoding-identifier-case": "off", "unicorn/switch-case-braces": "off", "unicorn/prefer-top-level-await": "off"}}, "ava": {"serial": true}}