
> api@1.0.0 type-check
> tsc --noEmit

[1G[0K[96msrc/lib/auth/auth0.ts[0m:[93m8[0m:[93m30[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.
  Overload 1 of 2, '(options: ManagementClientOptionsWithToken): ManagementClient', gave the following error.
    Object literal may only specify known properties, and 'clientId' does not exist in type 'ManagementClientOptionsWithToken'.
  Overload 2 of 2, '(options: ManagementClientOptionsWithClientCredentials): ManagementClient', gave the following error.
    Object literal may only specify known properties, and 'scope' does not exist in type 'ManagementClientOptionsWithClientCredentials'.

[7m8[0m const managementClient = new ManagementClient({
[7m [0m [91m                             ~~~~~~~~~~~~~~~~[0m


[96msrc/lib/auth/auth0.ts[0m:[93m52[0m:[93m7[0m - [91merror[0m[90m TS2322: [0mType '{ status: UserStatus; id: string; auth0Id: string; email: string; firstName: string; lastName: string; avatar: string | null; phone: string | null; language: string; timezone: string; ... 5 more ...; lastLoginAt: Date | null; }' is not assignable to type '{ factory: { status: FactoryStatus; id: string; name: string; slug: string; } | null; } & { status: UserStatus; id: string; auth0Id: string; email: string; firstName: string; ... 10 more ...; lastLoginAt: Date | null; }'.
  Property 'factory' is missing in type '{ status: UserStatus; id: string; auth0Id: string; email: string; firstName: string; lastName: string; avatar: string | null; phone: string | null; language: string; timezone: string; ... 5 more ...; lastLoginAt: Date | null; }' but required in type '{ factory: { status: FactoryStatus; id: string; name: string; slug: string; } | null; }'.

[7m52[0m       user = await prisma.user.create({
[7m  [0m [91m      ~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m53[0m:[93m9[0m - [91merror[0m[90m TS2375: [0mType '{ auth0Id: string; email: string; firstName: string; lastName: string; avatar: string | undefined; role: "SUPER_ADMIN" | "FACTORY_OWNER" | "FACTORY_ADMIN" | "FACTORY_MANAGER" | "FACTORY_STAFF" | "CUSTOMER" | "CUSTOMER_ADMIN"; status: "ACTIVE"; permissions: ("FACTORY_ADMIN" | ... 21 more ... | "MESSAGE_DELETE")[]; la...' is not assignable to type '(Without<UserCreateInput, UserUncheckedCreateInput> & UserUncheckedCreateInput) | (Without<...> & UserCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Type '{ auth0Id: string; email: string; firstName: string; lastName: string; avatar: string | undefined; role: "SUPER_ADMIN" | "FACTORY_OWNER" | "FACTORY_ADMIN" | "FACTORY_MANAGER" | "FACTORY_STAFF" | "CUSTOMER" | "CUSTOMER_ADMIN"; status: "ACTIVE"; permissions: ("FACTORY_ADMIN" | ... 21 more ... | "MESSAGE_DELETE")[]; la...' is not assignable to type 'Without<UserUncheckedCreateInput, UserCreateInput> & UserCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Type '{ auth0Id: string; email: string; firstName: string; lastName: string; avatar: string | undefined; role: "SUPER_ADMIN" | "FACTORY_OWNER" | "FACTORY_ADMIN" | "FACTORY_MANAGER" | "FACTORY_STAFF" | "CUSTOMER" | "CUSTOMER_ADMIN"; status: "ACTIVE"; permissions: ("FACTORY_ADMIN" | ... 21 more ... | "MESSAGE_DELETE")[]; la...' is not assignable to type 'UserCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
      Types of property 'avatar' are incompatible.
        Type 'string | undefined' is not assignable to type 'string | null'.
          Type 'undefined' is not assignable to type 'string | null'.

[7m53[0m         data: {
[7m  [0m [91m        ~~~~[0m

  [96m../../node_modules/.prisma/client/index.d.ts[0m:[93m5347[0m:[93m5[0m
    [7m5347[0m     data: XOR<UserCreateInput, UserUncheckedCreateInput>
    [7m    [0m [96m    ~~~~[0m
    The expected type comes from property 'data' which is declared here on type '{ select?: UserSelect<DefaultArgs> | null; include?: UserInclude<DefaultArgs> | null; data: (Without<UserCreateInput, UserUncheckedCreateInput> & UserUncheckedCreateInput) | (Without<...> & UserCreateInput); }'

[96msrc/lib/auth/auth0.ts[0m:[93m78[0m:[93m17[0m - [91merror[0m[90m TS18047: [0m'user' is possibly 'null'.

[7m78[0m         userId: user.id,
[7m  [0m [91m                ~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m79[0m:[93m15[0m - [91merror[0m[90m TS18047: [0m'user' is possibly 'null'.

[7m79[0m         role: user.role,
[7m  [0m [91m              ~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m80[0m:[93m20[0m - [91merror[0m[90m TS18047: [0m'user' is possibly 'null'.

[7m80[0m         factoryId: user.factoryId,
[7m  [0m [91m                   ~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m107[0m:[93m31[0m - [91merror[0m[90m TS2379: [0mArgument of type '{ userId: string; auth0Id: string; email: string; role: $Enums.UserRole; factoryId: string | undefined; permissions: any[]; }' is not assignable to parameter of type '{ userId: string; auth0Id: string; email: string; role: "SUPER_ADMIN" | "FACTORY_OWNER" | "FACTORY_ADMIN" | "FACTORY_MANAGER" | "FACTORY_STAFF" | "CUSTOMER" | "CUSTOMER_ADMIN"; factoryId?: string; permissions: ("FACTORY_ADMIN" | ... 21 more ... | "MESSAGE_DELETE")[]; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'factoryId' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.

[7m107[0m     const token = generateJWT({
[7m   [0m [91m                              ~[0m
[7m108[0m       userId: user.id,
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~[0m
[7m...[0m 
[7m113[0m       permissions: user.permissions as any[],
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m114[0m     });
[7m   [0m [91m~~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m108[0m:[93m15[0m - [91merror[0m[90m TS18047: [0m'user' is possibly 'null'.

[7m108[0m       userId: user.id,
[7m   [0m [91m              ~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m109[0m:[93m16[0m - [91merror[0m[90m TS18047: [0m'user' is possibly 'null'.

[7m109[0m       auth0Id: user.auth0Id,
[7m   [0m [91m               ~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m110[0m:[93m14[0m - [91merror[0m[90m TS18047: [0m'user' is possibly 'null'.

[7m110[0m       email: user.email,
[7m   [0m [91m             ~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m111[0m:[93m13[0m - [91merror[0m[90m TS18047: [0m'user' is possibly 'null'.

[7m111[0m       role: user.role,
[7m   [0m [91m            ~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m112[0m:[93m18[0m - [91merror[0m[90m TS18047: [0m'user' is possibly 'null'.

[7m112[0m       factoryId: user.factoryId || undefined,
[7m   [0m [91m                 ~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m113[0m:[93m20[0m - [91merror[0m[90m TS18047: [0m'user' is possibly 'null'.

[7m113[0m       permissions: user.permissions as any[],
[7m   [0m [91m                   ~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m134[0m:[93m28[0m - [91merror[0m[90m TS2339: [0mProperty 'updateUser' does not exist on type 'ManagementClient'.

[7m134[0m     await managementClient.updateUser(
[7m   [0m [91m                           ~~~~~~~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m152[0m:[93m41[0m - [91merror[0m[90m TS2339: [0mProperty 'getUser' does not exist on type 'ManagementClient'.

[7m152[0m     const user = await managementClient.getUser({ id: auth0Id });
[7m   [0m [91m                                        ~~~~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m170[0m:[93m46[0m - [91merror[0m[90m TS2339: [0mProperty 'createUser' does not exist on type 'ManagementClient'.

[7m170[0m     const auth0User = await managementClient.createUser({
[7m   [0m [91m                                             ~~~~~~~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m199[0m:[93m28[0m - [91merror[0m[90m TS2339: [0mProperty 'deleteUser' does not exist on type 'ManagementClient'.

[7m199[0m     await managementClient.deleteUser({ id: auth0Id });
[7m   [0m [91m                           ~~~~~~~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m209[0m:[93m32[0m - [91merror[0m[90m TS2339: [0mProperty 'requestChangePasswordEmail' does not exist on type 'AuthenticationClient'.

[7m209[0m     await authenticationClient.requestChangePasswordEmail({
[7m   [0m [91m                               ~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m222[0m:[93m28[0m - [91merror[0m[90m TS2339: [0mProperty 'sendEmailVerification' does not exist on type 'ManagementClient'.

[7m222[0m     await managementClient.sendEmailVerification({ user_id: auth0Id });
[7m   [0m [91m                           ~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m232[0m:[93m28[0m - [91merror[0m[90m TS2339: [0mProperty 'updateUser' does not exist on type 'ManagementClient'.

[7m232[0m     await managementClient.updateUser({ id: auth0Id }, { blocked });
[7m   [0m [91m                           ~~~~~~~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m242[0m:[93m42[0m - [91merror[0m[90m TS2339: [0mProperty 'getUserRoles' does not exist on type 'ManagementClient'.

[7m242[0m     const roles = await managementClient.getUserRoles({ id: auth0Id });
[7m   [0m [91m                                         ~~~~~~~~~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m243[0m:[93m22[0m - [91merror[0m[90m TS7006: [0mParameter 'role' implicitly has an 'any' type.

[7m243[0m     return roles.map(role => role.name || '');
[7m   [0m [91m                     ~~~~[0m

[96msrc/lib/auth/auth0.ts[0m:[93m253[0m:[93m28[0m - [91merror[0m[90m TS2339: [0mProperty 'assignRolestoUser' does not exist on type 'ManagementClient'.

[7m253[0m     await managementClient.assignRolestoUser({ id: auth0Id }, { roles: [roleId] });
[7m   [0m [91m                           ~~~~~~~~~~~~~~~~~[0m

[96msrc/lib/auth/jwt.ts[0m:[93m14[0m:[93m9[0m - [91merror[0m[90m TS2375: [0mType '{ sub: string; userId: string; email: string; role: "SUPER_ADMIN" | "FACTORY_OWNER" | "FACTORY_ADMIN" | "FACTORY_MANAGER" | "FACTORY_STAFF" | "CUSTOMER" | "CUSTOMER_ADMIN"; factoryId: string | undefined; permissions: ("FACTORY_ADMIN" | ... 21 more ... | "MESSAGE_DELETE")[]; }' is not assignable to type 'Omit<JWTPayload, "iat" | "exp" | "aud" | "iss">' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'factoryId' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.

[7m14[0m   const jwtPayload: Omit<JWTPayload, 'iat' | 'exp' | 'aud' | 'iss'> = {
[7m  [0m [91m        ~~~~~~~~~~[0m

[96msrc/lib/auth/jwt.ts[0m:[93m23[0m:[93m14[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.
  Overload 1 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: null, options?: (SignOptions & { algorithm: "none"; }) | undefined): string', gave the following error.
    Argument of type 'string' is not assignable to parameter of type 'null'.
  Overload 2 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Buffer<ArrayBufferLike> | Secret | PrivateKeyInput | JsonWebKeyInput, options?: SignOptions | undefined): string', gave the following error.
    Type 'string' is not assignable to type 'number | StringValue | undefined'.
  Overload 3 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Buffer<ArrayBufferLike> | Secret | PrivateKeyInput | JsonWebKeyInput, callback: SignCallback): void', gave the following error.
    Object literal may only specify known properties, and 'expiresIn' does not exist in type 'SignCallback'.

[7m23[0m   return jwt.sign(jwtPayload, config.JWT_SECRET, {
[7m  [0m [91m             ~~~~[0m


[96msrc/lib/database/connection.ts[0m:[93m22[0m:[93m66[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ datasources: { db: { url: string; }; }; log: readonly ["query", "info", "warn", "error"] | readonly ["warn", "error"]; }' is not assignable to parameter of type 'Subset<PrismaClientOptions, PrismaClientOptions>'.
  Types of property 'log' are incompatible.
    Type 'readonly ["query", "info", "warn", "error"] | readonly ["warn", "error"]' is not assignable to type '(LogLevel | LogDefinition)[]'.
      The type 'readonly ["query", "info", "warn", "error"]' is 'readonly' and cannot be assigned to the mutable type '(LogLevel | LogDefinition)[]'.

[7m22[0m export const prisma = globalForPrisma.prisma ?? new PrismaClient(prismaConfig);
[7m  [0m [91m                                                                 ~~~~~~~~~~~~[0m

[96msrc/lib/database/connection.ts[0m:[93m55[0m:[93m5[0m - [91merror[0m[90m TS2375: [0mType '{ healthy: true; latency: number; activeConnections: number | undefined; }' is not assignable to type '{ healthy: boolean; latency: number; activeConnections?: number; error?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'activeConnections' are incompatible.
    Type 'number | undefined' is not assignable to type 'number'.
      Type 'undefined' is not assignable to type 'number'.

[7m55[0m     return {
[7m  [0m [91m    ~~~~~~[0m

[96msrc/lib/database/connection.ts[0m:[93m113[0m:[93m3[0m - [91merror[0m[90m TS2322: [0mType 'Promise<any[]>' is not assignable to type 'Promise<T>'.
  Type 'any[]' is not assignable to type 'T'.
    'T' could be instantiated with an arbitrary type which could be unrelated to 'any[]'.

[7m113[0m   return prisma.$transaction(callback);
[7m   [0m [91m  ~~~~~~[0m

[96msrc/lib/database/connection.ts[0m:[93m113[0m:[93m30[0m - [91merror[0m[90m TS2769: [0mNo overload matches this call.
  Overload 1 of 2, '(arg: PrismaPromise<any>[], options?: { isolationLevel?: TransactionIsolationLevel; } | undefined): Promise<any[]>', gave the following error.
    Argument of type '(tx: PrismaClient<PrismaClientOptions, never, DefaultArgs>) => Promise<T>' is not assignable to parameter of type 'PrismaPromise<any>[]'.
  Overload 2 of 2, '(fn: (prisma: Omit<PrismaClient<PrismaClientOptions, never, DefaultArgs>, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">) => Promise<...>, options?: { ...; } | undefined): Promise<...>', gave the following error.
    Argument of type '(tx: PrismaClient<PrismaClientOptions, never, DefaultArgs>) => Promise<T>' is not assignable to parameter of type '(prisma: Omit<PrismaClient<PrismaClientOptions, never, DefaultArgs>, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">) => Promise<...>'.
      Types of parameters 'tx' and 'prisma' are incompatible.
        Type 'Omit<PrismaClient<PrismaClientOptions, never, DefaultArgs>, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">' is missing the following properties from type 'PrismaClient<PrismaClientOptions, never, DefaultArgs>': $on, $connect, $disconnect, $use, and 2 more.

[7m113[0m   return prisma.$transaction(callback);
[7m   [0m [91m                             ~~~~~~~~[0m


[96msrc/lib/realtime/messaging-service.ts[0m:[93m79[0m:[93m15[0m - [91merror[0m[90m TS2339: [0mProperty 'error' does not exist on type 'RealtimeChannelSendResponse'.

[7m79[0m       const { error } = await supabaseAdmin.realtime
[7m  [0m [91m              ~~~~~[0m

[96msrc/lib/realtime/messaging-service.ts[0m:[93m306[0m:[93m9[0m - [91merror[0m[90m TS2375: [0mType '{ productId?: string; inquiryId?: string; orderId?: string; quoteId?: string; type: any; subject: string | undefined; factoryId: string; }' is not assignable to type '(Without<ConversationCreateInput, ConversationUncheckedCreateInput> & ConversationUncheckedCreateInput) | (Without<...> & ConversationCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Type '{ productId?: string; inquiryId?: string; orderId?: string; quoteId?: string; type: any; subject: string | undefined; factoryId: string; }' is not assignable to type 'Without<ConversationCreateInput, ConversationUncheckedCreateInput> & ConversationUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Type '{ productId?: string; inquiryId?: string; orderId?: string; quoteId?: string; type: any; subject: string | undefined; factoryId: string; }' is not assignable to type 'ConversationUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
      Types of property 'subject' are incompatible.
        Type 'string | undefined' is not assignable to type 'string | null'.
          Type 'undefined' is not assignable to type 'string | null'.

[7m306[0m         data: {
[7m   [0m [91m        ~~~~[0m

  [96m../../node_modules/.prisma/client/index.d.ts[0m:[93m30210[0m:[93m5[0m
    [7m30210[0m     data: XOR<ConversationCreateInput, ConversationUncheckedCreateInput>
    [7m     [0m [96m    ~~~~[0m
    The expected type comes from property 'data' which is declared here on type '{ select?: ConversationSelect<DefaultArgs> | null; include?: ConversationInclude<DefaultArgs> | null; data: (Without<...> & ConversationUncheckedCreateInput) | (Without<...> & ConversationCreateInput); }'

[96msrc/lib/realtime/messaging-service.ts[0m:[93m327[0m:[93m9[0m - [91merror[0m[90m TS2322: [0mType '{ userId: string; conversationId: string; role: string; canWrite: boolean; canRead: boolean; }[]' is not assignable to type 'ConversationParticipantCreateManyInput | ConversationParticipantCreateManyInput[]'.
  Type '{ userId: string; conversationId: string; role: string; canWrite: boolean; canRead: boolean; }[]' is not assignable to type 'ConversationParticipantCreateManyInput[]'.
    Type '{ userId: string; conversationId: string; role: string; canWrite: boolean; canRead: boolean; }' is not assignable to type 'ConversationParticipantCreateManyInput'.
      Types of property 'role' are incompatible.
        Type 'string' is not assignable to type 'ParticipantRole'.

[7m327[0m         data: participantData,
[7m   [0m [91m        ~~~~[0m

[96msrc/lib/realtime/supabase-client.ts[0m:[93m122[0m:[93m37[0m - [91merror[0m[90m TS2339: [0mProperty 'id' does not exist on type '{} | { [key: string]: any; }'.
  Property 'id' does not exist on type '{}'.

[7m122[0m             messageId: payload.new?.id,
[7m   [0m [91m                                    ~~[0m

[96msrc/lib/realtime/supabase-client.ts[0m:[93m124[0m:[93m20[0m - [91merror[0m[90m TS2352: [0mConversion of type 'RealtimePostgresChangesPayload<{ [key: string]: any; }>' to type 'RealtimePayload<RealtimeMessage>' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'RealtimePostgresDeletePayload<{ [key: string]: any; }>' is not comparable to type 'RealtimePayload<RealtimeMessage>'.
    Types of property 'new' are incompatible.
      Type '{}' is missing the following properties from type 'RealtimeMessage': id, content, messageType, conversationId, and 5 more.

[7m124[0m           callback(payload as RealtimePayload<RealtimeMessage>);
[7m   [0m [91m                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/lib/realtime/supabase-client.ts[0m:[93m160[0m:[93m42[0m - [91merror[0m[90m TS2339: [0mProperty 'id' does not exist on type '{} | { [key: string]: any; }'.
  Property 'id' does not exist on type '{}'.

[7m160[0m             conversationId: payload.new?.id,
[7m   [0m [91m                                         ~~[0m

[96msrc/lib/realtime/supabase-client.ts[0m:[93m162[0m:[93m20[0m - [91merror[0m[90m TS2352: [0mConversion of type 'RealtimePostgresChangesPayload<{ [key: string]: any; }>' to type 'RealtimePayload<RealtimeConversation>' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'RealtimePostgresDeletePayload<{ [key: string]: any; }>' is not comparable to type 'RealtimePayload<RealtimeConversation>'.
    Types of property 'new' are incompatible.
      Type '{}' is missing the following properties from type 'RealtimeConversation': id, type, factoryId, isActive, and 3 more.

[7m162[0m           callback(payload as RealtimePayload<RealtimeConversation>);
[7m   [0m [91m                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/lib/realtime/supabase-client.ts[0m:[93m199[0m:[93m41[0m - [91merror[0m[90m TS2339: [0mProperty 'id' does not exist on type '{} | { [key: string]: any; }'.
  Property 'id' does not exist on type '{}'.

[7m199[0m             participantId: payload.new?.id,
[7m   [0m [91m                                        ~~[0m

[96msrc/lib/realtime/supabase-client.ts[0m:[93m201[0m:[93m20[0m - [91merror[0m[90m TS2352: [0mConversion of type 'RealtimePostgresChangesPayload<{ [key: string]: any; }>' to type 'RealtimePayload<RealtimeConversationParticipant>' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
  Type 'RealtimePostgresDeletePayload<{ [key: string]: any; }>' is not comparable to type 'RealtimePayload<RealtimeConversationParticipant>'.
    Types of property 'new' are incompatible.
      Type '{}' is missing the following properties from type 'RealtimeConversationParticipant': id, userId, conversationId, role, and 4 more.

[7m201[0m           callback(payload as RealtimePayload<RealtimeConversationParticipant>);
[7m   [0m [91m                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m9[0m:[93m46[0m - [91merror[0m[90m TS2304: [0mCannot find name 'Socket'.

[7m9[0m export interface AuthenticatedSocket extends Socket {
[7m [0m [91m                                             ~~~~~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m52[0m:[93m32[0m - [91merror[0m[90m TS2339: [0mProperty 'userId' does not exist on type 'Promise<JWTPayload>'.

[7m52[0m           where: { id: payload.userId },
[7m  [0m [91m                               ~~~~~~[0m

  [96msrc/lib/realtime/websocket-handler.ts[0m:[93m52[0m:[93m32[0m
    [7m52[0m           where: { id: payload.userId },
    [7m  [0m [96m                               ~~~~~~[0m
    Did you forget to use 'await'?

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m89[0m:[93m18[0m - [91merror[0m[90m TS18046: [0m'error' is of type 'unknown'.

[7m89[0m           error: error.message,
[7m  [0m [91m                 ~~~~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m97[0m:[93m30[0m - [91merror[0m[90m TS2345: [0mArgument of type '(socket: AuthenticatedSocket) => void' is not assignable to parameter of type '(socket: Socket<DefaultEventsMap, DefaultEventsMap, DefaultEventsMap, any>) => void'.
  Types of parameters 'socket' and 'socket' are incompatible.
    Type 'Socket<DefaultEventsMap, DefaultEventsMap, DefaultEventsMap, any>' is missing the following properties from type 'AuthenticatedSocket': userId, factoryId, auth0Id, email, role

[7m97[0m     this.io.on('connection', (socket: AuthenticatedSocket) => {
[7m  [0m [91m                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m99[0m:[93m26[0m - [91merror[0m[90m TS2339: [0mProperty 'id' does not exist on type 'AuthenticatedSocket'.

[7m99[0m         socketId: socket.id,
[7m  [0m [91m                         ~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m105[0m:[93m53[0m - [91merror[0m[90m TS2339: [0mProperty 'id' does not exist on type 'AuthenticatedSocket'.

[7m105[0m       this.connectedUsers.set(socket.userId, socket.id);
[7m   [0m [91m                                                    ~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m108[0m:[93m14[0m - [91merror[0m[90m TS2339: [0mProperty 'join' does not exist on type 'AuthenticatedSocket'.

[7m108[0m       socket.join(`factory:${socket.factoryId}`);
[7m   [0m [91m             ~~~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m109[0m:[93m14[0m - [91merror[0m[90m TS2339: [0mProperty 'join' does not exist on type 'AuthenticatedSocket'.

[7m109[0m       socket.join(`user:${socket.userId}`);
[7m   [0m [91m             ~~~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m112[0m:[93m14[0m - [91merror[0m[90m TS2339: [0mProperty 'on' does not exist on type 'AuthenticatedSocket'.

[7m112[0m       socket.on('join_conversation', async (data: { conversationId: string }) => {
[7m   [0m [91m             ~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m125[0m:[93m20[0m - [91merror[0m[90m TS2339: [0mProperty 'emit' does not exist on type 'AuthenticatedSocket'.

[7m125[0m             socket.emit('error', { message: 'Access denied to conversation' });
[7m   [0m [91m                   ~~~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m129[0m:[93m18[0m - [91merror[0m[90m TS2339: [0mProperty 'join' does not exist on type 'AuthenticatedSocket'.

[7m129[0m           socket.join(`conversation:${data.conversationId}`);
[7m   [0m [91m                 ~~~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m130[0m:[93m18[0m - [91merror[0m[90m TS2339: [0mProperty 'emit' does not exist on type 'AuthenticatedSocket'.

[7m130[0m           socket.emit('conversation_joined', { conversationId: data.conversationId });
[7m   [0m [91m                 ~~~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m142[0m:[93m18[0m - [91merror[0m[90m TS2339: [0mProperty 'emit' does not exist on type 'AuthenticatedSocket'.

[7m142[0m           socket.emit('error', { message: 'Failed to join conversation' });
[7m   [0m [91m                 ~~~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m147[0m:[93m14[0m - [91merror[0m[90m TS2339: [0mProperty 'on' does not exist on type 'AuthenticatedSocket'.

[7m147[0m       socket.on('leave_conversation', (data: { conversationId: string }) => {
[7m   [0m [91m             ~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m148[0m:[93m16[0m - [91merror[0m[90m TS2339: [0mProperty 'leave' does not exist on type 'AuthenticatedSocket'.

[7m148[0m         socket.leave(`conversation:${data.conversationId}`);
[7m   [0m [91m               ~~~~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m149[0m:[93m16[0m - [91merror[0m[90m TS2339: [0mProperty 'emit' does not exist on type 'AuthenticatedSocket'.

[7m149[0m         socket.emit('conversation_left', { conversationId: data.conversationId });
[7m   [0m [91m               ~~~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m158[0m:[93m14[0m - [91merror[0m[90m TS2339: [0mProperty 'on' does not exist on type 'AuthenticatedSocket'.

[7m158[0m       socket.on('typing_start', async (data: { conversationId: string }) => {
[7m   [0m [91m             ~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m166[0m:[93m18[0m - [91merror[0m[90m TS2339: [0mProperty 'to' does not exist on type 'AuthenticatedSocket'.

[7m166[0m           socket.to(`conversation:${data.conversationId}`).emit('user_typing', {
[7m   [0m [91m                 ~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m180[0m:[93m14[0m - [91merror[0m[90m TS2339: [0mProperty 'on' does not exist on type 'AuthenticatedSocket'.

[7m180[0m       socket.on('typing_stop', async (data: { conversationId: string }) => {
[7m   [0m [91m             ~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m188[0m:[93m18[0m - [91merror[0m[90m TS2339: [0mProperty 'to' does not exist on type 'AuthenticatedSocket'.

[7m188[0m           socket.to(`conversation:${data.conversationId}`).emit('user_typing', {
[7m   [0m [91m                 ~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m203[0m:[93m14[0m - [91merror[0m[90m TS2339: [0mProperty 'on' does not exist on type 'AuthenticatedSocket'.

[7m203[0m       socket.on('mark_message_read', async (data: {
[7m   [0m [91m             ~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m214[0m:[93m18[0m - [91merror[0m[90m TS2339: [0mProperty 'to' does not exist on type 'AuthenticatedSocket'.

[7m214[0m           socket.to(`conversation:${data.conversationId}`).emit('message_read', {
[7m   [0m [91m                 ~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m230[0m:[93m14[0m - [91merror[0m[90m TS2339: [0mProperty 'on' does not exist on type 'AuthenticatedSocket'.

[7m230[0m       socket.on('update_presence', (data: { status: 'online' | 'away' | 'busy' }) => {
[7m   [0m [91m             ~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m231[0m:[93m16[0m - [91merror[0m[90m TS2339: [0mProperty 'to' does not exist on type 'AuthenticatedSocket'.

[7m231[0m         socket.to(`factory:${socket.factoryId}`).emit('user_presence_updated', {
[7m   [0m [91m               ~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m239[0m:[93m14[0m - [91merror[0m[90m TS2339: [0mProperty 'on' does not exist on type 'AuthenticatedSocket'.

[7m239[0m       socket.on('disconnect', (reason) => {
[7m   [0m [91m             ~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m239[0m:[93m32[0m - [91merror[0m[90m TS7006: [0mParameter 'reason' implicitly has an 'any' type.

[7m239[0m       socket.on('disconnect', (reason) => {
[7m   [0m [91m                               ~~~~~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m241[0m:[93m28[0m - [91merror[0m[90m TS2339: [0mProperty 'id' does not exist on type 'AuthenticatedSocket'.

[7m241[0m           socketId: socket.id,
[7m   [0m [91m                           ~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m251[0m:[93m16[0m - [91merror[0m[90m TS2339: [0mProperty 'to' does not exist on type 'AuthenticatedSocket'.

[7m251[0m         socket.to(`factory:${socket.factoryId}`).emit('user_presence_updated', {
[7m   [0m [91m               ~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m259[0m:[93m14[0m - [91merror[0m[90m TS2339: [0mProperty 'on' does not exist on type 'AuthenticatedSocket'.

[7m259[0m       socket.on('error', (error) => {
[7m   [0m [91m             ~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m259[0m:[93m27[0m - [91merror[0m[90m TS7006: [0mParameter 'error' implicitly has an 'any' type.

[7m259[0m       socket.on('error', (error) => {
[7m   [0m [91m                          ~~~~~[0m

[96msrc/lib/realtime/websocket-handler.ts[0m:[93m261[0m:[93m28[0m - [91merror[0m[90m TS2339: [0mProperty 'id' does not exist on type 'AuthenticatedSocket'.

[7m261[0m           socketId: socket.id,
[7m   [0m [91m                           ~~[0m

[96msrc/lib/trpc.ts[0m:[93m133[0m:[93m11[0m - [91merror[0m[90m TS2375: [0mType '{ id: string; auth0Id: string; email: string; firstName: string; lastName: string; role: $Enums.UserRole; status: "ACTIVE"; factoryId: string | undefined; permissions: Permission[]; avatar: string | undefined; }' is not assignable to type '{ id: string; auth0Id: string; email: string; firstName: string; lastName: string; role: "SUPER_ADMIN" | "FACTORY_OWNER" | "FACTORY_ADMIN" | "FACTORY_MANAGER" | "FACTORY_STAFF" | "CUSTOMER" | "CUSTOMER_ADMIN"; status: "ACTIVE" | ... 2 more ... | "PENDING_VERIFICATION"; factoryId?: string; permissions: ("FACTORY_ADMI...' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'factoryId' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.

[7m133[0m           user: {
[7m   [0m [91m          ~~~~[0m

  [96m../../packages/shared-types/src/auth.ts[0m:[93m97[0m:[93m3[0m
    [7m97[0m   user: {
    [7m  [0m [96m  ~~~~[0m
    The expected type comes from property 'user' which is declared here on type 'AuthContext'

[96msrc/lib/trpc.ts[0m:[93m292[0m:[93m11[0m - [91merror[0m[90m TS2375: [0mType '{ type: "BUSINESS"; event: string; entityType: string | undefined; userId: string; factoryId: string | undefined; ipAddress: string; userAgent: string; metadata: { path: string; type: "query" | "mutation" | "subscription"; input: any; timestamp: string; }; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Type '{ type: "BUSINESS"; event: string; entityType: string | undefined; userId: string; factoryId: string | undefined; ipAddress: string; userAgent: string; metadata: { path: string; type: "query" | "mutation" | "subscription"; input: any; timestamp: string; }; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Type '{ type: "BUSINESS"; event: string; entityType: string | undefined; userId: string; factoryId: string | undefined; ipAddress: string; userAgent: string; metadata: { path: string; type: "query" | "mutation" | "subscription"; input: any; timestamp: string; }; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
      Types of property 'entityType' are incompatible.
        Type 'string | undefined' is not assignable to type 'string | null'.
          Type 'undefined' is not assignable to type 'string | null'.

[7m292[0m           data: {
[7m   [0m [91m          ~~~~[0m

  [96m../../node_modules/.prisma/client/index.d.ts[0m:[93m36857[0m:[93m5[0m
    [7m36857[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>
    [7m     [0m [96m    ~~~~[0m
    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'

[96msrc/routers/auth.ts[0m:[93m34[0m:[93m64[0m - [91merror[0m[90m TS2379: [0mArgument of type '{ sub: string; email: string; email_verified: boolean; name?: string | undefined; given_name?: string | undefined; family_name?: string | undefined; picture?: string | undefined; locale?: string | undefined; updated_at?: string | undefined; }' is not assignable to parameter of type 'Auth0Profile' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'name' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.

[7m34[0m         const { user, token, isNewUser } = await syncAuth0User(input.auth0Profile);
[7m  [0m [91m                                                               ~~~~~~~~~~~~~~~~~~[0m

[96msrc/routers/auth.ts[0m:[93m120[0m:[93m38[0m - [91merror[0m[90m TS2379: [0mArgument of type '{ userId: string; auth0Id: string; email: string; role: $Enums.UserRole; factoryId: string | undefined; permissions: any[]; }' is not assignable to parameter of type '{ userId: string; auth0Id: string; email: string; role: "SUPER_ADMIN" | "FACTORY_OWNER" | "FACTORY_ADMIN" | "FACTORY_MANAGER" | "FACTORY_STAFF" | "CUSTOMER" | "CUSTOMER_ADMIN"; factoryId?: string; permissions: ("FACTORY_ADMIN" | ... 21 more ... | "MESSAGE_DELETE")[]; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'factoryId' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.

[7m120[0m         const newToken = generateJWT({
[7m   [0m [91m                                     ~[0m
[7m121[0m           userId: user.id,
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m...[0m 
[7m126[0m           permissions: user.permissions as any[],
[7m   [0m [91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
[7m127[0m         });
[7m   [0m [91m~~~~~~~~~[0m

[96msrc/routers/auth.ts[0m:[93m215[0m:[93m9[0m - [91merror[0m[90m TS2375: [0mType '{ updatedAt: Date; firstName?: string | undefined; lastName?: string | undefined; avatar?: string | undefined; phone?: string | undefined; language?: string | undefined; timezone?: string | undefined; }' is not assignable to type '(Without<UserUpdateInput, UserUncheckedUpdateInput> & UserUncheckedUpdateInput) | (Without<...> & UserUpdateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Type '{ updatedAt: Date; firstName?: string | undefined; lastName?: string | undefined; avatar?: string | undefined; phone?: string | undefined; language?: string | undefined; timezone?: string | undefined; }' is not assignable to type 'Without<UserUncheckedUpdateInput, UserUpdateInput> & UserUpdateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Type '{ updatedAt: Date; firstName?: string | undefined; lastName?: string | undefined; avatar?: string | undefined; phone?: string | undefined; language?: string | undefined; timezone?: string | undefined; }' is not assignable to type 'UserUpdateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
      Types of property 'firstName' are incompatible.
        Type 'string | undefined' is not assignable to type 'string | StringFieldUpdateOperationsInput'.
          Type 'undefined' is not assignable to type 'string | StringFieldUpdateOperationsInput'.

[7m215[0m         data: {
[7m   [0m [91m        ~~~~[0m

  [96m../../node_modules/.prisma/client/index.d.ts[0m:[93m5395[0m:[93m5[0m
    [7m5395[0m     data: XOR<UserUpdateInput, UserUncheckedUpdateInput>
    [7m    [0m [96m    ~~~~[0m
    The expected type comes from property 'data' which is declared here on type '{ select?: UserSelect<DefaultArgs> | null; include?: UserInclude<DefaultArgs> | null; data: (Without<UserUpdateInput, UserUncheckedUpdateInput> & UserUncheckedUpdateInput) | (Without<...> & UserUpdateInput); where: UserWhereUniqueInput; }'

[96msrc/routers/auth.ts[0m:[93m233[0m:[93m9[0m - [91merror[0m[90m TS2375: [0mType '{ type: "BUSINESS"; event: string; userId: string; factoryId: string | undefined; ipAddress: string; userAgent: string; metadata: { changes: string[]; timestamp: string; }; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Type '{ type: "BUSINESS"; event: string; userId: string; factoryId: string | undefined; ipAddress: string; userAgent: string; metadata: { changes: string[]; timestamp: string; }; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Type '{ type: "BUSINESS"; event: string; userId: string; factoryId: string | undefined; ipAddress: string; userAgent: string; metadata: { changes: string[]; timestamp: string; }; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
      Types of property 'factoryId' are incompatible.
        Type 'string | undefined' is not assignable to type 'string | null'.
          Type 'undefined' is not assignable to type 'string | null'.

[7m233[0m         data: {
[7m   [0m [91m        ~~~~[0m

  [96m../../node_modules/.prisma/client/index.d.ts[0m:[93m36857[0m:[93m5[0m
    [7m36857[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>
    [7m     [0m [96m    ~~~~[0m
    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'

[96msrc/routers/auth.ts[0m:[93m260[0m:[93m30[0m - [91merror[0m[90m TS2551: [0mProperty 'factory' does not exist on type '{ status: UserStatus; id: string; auth0Id: string; email: string; firstName: string; lastName: string; avatar: string | null; phone: string | null; language: string; timezone: string; ... 5 more ...; lastLoginAt: Date | null; }'. Did you mean 'factoryId'?

[7m260[0m         factory: updatedUser.factory,
[7m   [0m [91m                             ~~~~~~~[0m

[96msrc/routers/auth.ts[0m:[93m299[0m:[93m11[0m - [91merror[0m[90m TS2375: [0mType '{ type: "SECURITY"; event: string; userId: string; factoryId: string | undefined; ipAddress: string; userAgent: string; metadata: { email: string; timestamp: string; }; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Type '{ type: "SECURITY"; event: string; userId: string; factoryId: string | undefined; ipAddress: string; userAgent: string; metadata: { email: string; timestamp: string; }; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Type '{ type: "SECURITY"; event: string; userId: string; factoryId: string | undefined; ipAddress: string; userAgent: string; metadata: { email: string; timestamp: string; }; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
      Types of property 'factoryId' are incompatible.
        Type 'string | undefined' is not assignable to type 'string | null'.
          Type 'undefined' is not assignable to type 'string | null'.

[7m299[0m           data: {
[7m   [0m [91m          ~~~~[0m

  [96m../../node_modules/.prisma/client/index.d.ts[0m:[93m36857[0m:[93m5[0m
    [7m36857[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>
    [7m     [0m [96m    ~~~~[0m
    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'

[96msrc/routers/auth.ts[0m:[93m330[0m:[93m9[0m - [91merror[0m[90m TS2375: [0mType '{ type: "SECURITY"; event: string; userId: string; factoryId: string | undefined; ipAddress: string; userAgent: string; metadata: { timestamp: string; }; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Type '{ type: "SECURITY"; event: string; userId: string; factoryId: string | undefined; ipAddress: string; userAgent: string; metadata: { timestamp: string; }; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Type '{ type: "SECURITY"; event: string; userId: string; factoryId: string | undefined; ipAddress: string; userAgent: string; metadata: { timestamp: string; }; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
      Types of property 'factoryId' are incompatible.
        Type 'string | undefined' is not assignable to type 'string | null'.
          Type 'undefined' is not assignable to type 'string | null'.

[7m330[0m         data: {
[7m   [0m [91m        ~~~~[0m

  [96m../../node_modules/.prisma/client/index.d.ts[0m:[93m36857[0m:[93m5[0m
    [7m36857[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>
    [7m     [0m [96m    ~~~~[0m
    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'

[96msrc/routers/factories.ts[0m:[93m233[0m:[93m9[0m - [91merror[0m[90m TS2375: [0mType '{ updatedAt: Date; slug?: string; description?: string | undefined; phone?: string | undefined; language?: string | undefined; timezone?: string | undefined; name?: string | undefined; ... 13 more ...; currency?: "USD" | ... 7 more ... | undefined; }' is not assignable to type '(Without<FactoryUpdateInput, FactoryUncheckedUpdateInput> & FactoryUncheckedUpdateInput) | (Without<...> & FactoryUpdateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Type '{ updatedAt: Date; slug?: string; description?: string | undefined; phone?: string | undefined; language?: string | undefined; timezone?: string | undefined; name?: string | undefined; ... 13 more ...; currency?: "USD" | ... 7 more ... | undefined; }' is not assignable to type 'Without<FactoryUncheckedUpdateInput, FactoryUpdateInput> & FactoryUpdateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Type '{ updatedAt: Date; slug?: string; description?: string | undefined; phone?: string | undefined; language?: string | undefined; timezone?: string | undefined; name?: string | undefined; ... 13 more ...; currency?: "USD" | ... 7 more ... | undefined; }' is not assignable to type 'FactoryUpdateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
      Types of property 'name' are incompatible.
        Type 'string | undefined' is not assignable to type 'string | StringFieldUpdateOperationsInput'.
          Type 'undefined' is not assignable to type 'string | StringFieldUpdateOperationsInput'.

[7m233[0m         data: {
[7m   [0m [91m        ~~~~[0m

  [96m../../node_modules/.prisma/client/index.d.ts[0m:[93m7152[0m:[93m5[0m
    [7m7152[0m     data: XOR<FactoryUpdateInput, FactoryUncheckedUpdateInput>
    [7m    [0m [96m    ~~~~[0m
    The expected type comes from property 'data' which is declared here on type '{ select?: FactorySelect<DefaultArgs> | null; include?: FactoryInclude<DefaultArgs> | null; data: (Without<FactoryUpdateInput, FactoryUncheckedUpdateInput> & FactoryUncheckedUpdateInput) | (Without<...> & FactoryUpdateInput); where: FactoryWhereUniqueInput; }'

[96msrc/routers/factories.ts[0m:[93m308[0m:[93m32[0m - [91merror[0m[90m TS2322: [0mType '{ factoryId?: never; } | { factoryId: string | undefined; }' is not assignable to type 'ProductWhereInput | undefined'.
  Type '{ factoryId: string | undefined; }' is not assignable to type 'ProductWhereInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Types of property 'factoryId' are incompatible.
      Type 'string | undefined' is not assignable to type 'string | StringFilter<"Product">'.
        Type 'undefined' is not assignable to type 'string | StringFilter<"Product">'.

[7m308[0m         ctx.db.product.count({ where }),
[7m   [0m [91m                               ~~~~~[0m

[96msrc/routers/factories.ts[0m:[93m309[0m:[93m32[0m - [91merror[0m[90m TS2322: [0mType '{ isActive: true; factoryId?: never; } | { isActive: true; factoryId: string | undefined; }' is not assignable to type 'ProductWhereInput | undefined'.
  Type '{ isActive: true; factoryId: string | undefined; }' is not assignable to type 'ProductWhereInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Types of property 'factoryId' are incompatible.
      Type 'string | undefined' is not assignable to type 'string | StringFilter<"Product">'.
        Type 'undefined' is not assignable to type 'string | StringFilter<"Product">'.

[7m309[0m         ctx.db.product.count({ where: { ...where, isActive: true } }),
[7m   [0m [91m                               ~~~~~[0m

[96msrc/routers/factories.ts[0m:[93m310[0m:[93m30[0m - [91merror[0m[90m TS2322: [0mType '{ factoryId?: never; } | { factoryId: string | undefined; }' is not assignable to type 'OrderWhereInput | undefined'.
  Type '{ factoryId: string | undefined; }' is not assignable to type 'OrderWhereInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Types of property 'factoryId' are incompatible.
      Type 'string | undefined' is not assignable to type 'string | StringFilter<"Order">'.
        Type 'undefined' is not assignable to type 'string | StringFilter<"Order">'.

[7m310[0m         ctx.db.order.count({ where }),
[7m   [0m [91m                             ~~~~~[0m

[96msrc/routers/factories.ts[0m:[93m312[0m:[93m11[0m - [91merror[0m[90m TS2322: [0mType '{ createdAt: { gte: Date; }; factoryId?: never; } | { createdAt: { gte: Date; }; factoryId: string | undefined; }' is not assignable to type 'OrderWhereInput | undefined'.
  Type '{ createdAt: { gte: Date; }; factoryId: string | undefined; }' is not assignable to type 'OrderWhereInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Types of property 'factoryId' are incompatible.
      Type 'string | undefined' is not assignable to type 'string | StringFilter<"Order">'.
        Type 'undefined' is not assignable to type 'string | StringFilter<"Order">'.

[7m312[0m           where: {
[7m   [0m [91m          ~~~~~[0m

[96msrc/routers/factories.ts[0m:[93m319[0m:[93m29[0m - [91merror[0m[90m TS2322: [0mType '{} | { factoryId: string | undefined; }' is not assignable to type 'UserWhereInput | undefined'.
  Type '{ factoryId: string | undefined; }' is not assignable to type 'UserWhereInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Types of property 'factoryId' are incompatible.
      Type 'string | undefined' is not assignable to type 'string | StringNullableFilter<"User"> | null'.
        Type 'undefined' is not assignable to type 'string | StringNullableFilter<"User"> | null'.


[7m319[0m         ctx.db.user.count({ where: isSystemAdmin ? {} : { factoryId } }),
[7m   [0m [91m                            ~~~~~[0m

[96msrc/routers/image-management.ts[0m:[93m96[0m:[93m15[0m - [91merror[0m[90m TS2375: [0mType '{ productId: string; url: string; originalName: string | undefined; fileSize: number | undefined; mimeType: string | undefined; altText: string | undefined; caption: string | undefined; isMain: false; sortOrder: number; status: "ACTIVE"; uploadedAt: Date; }' is not assignable to type '(Without<ProductImageCreateInput, ProductImageUncheckedCreateInput> & ProductImageUncheckedCreateInput) | (Without<...> & ProductImageCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Type '{ productId: string; url: string; originalName: string | undefined; fileSize: number | undefined; mimeType: string | undefined; altText: string | undefined; caption: string | undefined; isMain: false; sortOrder: number; status: "ACTIVE"; uploadedAt: Date; }' is not assignable to type 'Without<ProductImageCreateInput, ProductImageUncheckedCreateInput> & ProductImageUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Type '{ productId: string; url: string; originalName: string | undefined; fileSize: number | undefined; mimeType: string | undefined; altText: string | undefined; caption: string | undefined; isMain: false; sortOrder: number; status: "ACTIVE"; uploadedAt: Date; }' is not assignable to type 'ProductImageUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
      Types of property 'caption' are incompatible.
        Type 'string | undefined' is not assignable to type 'string | null'.
          Type 'undefined' is not assignable to type 'string | null'.

[7m96[0m               data: {
[7m  [0m [91m              ~~~~[0m

  [96m../../node_modules/.prisma/client/index.d.ts[0m:[93m11202[0m:[93m5[0m
    [7m11202[0m     data: XOR<ProductImageCreateInput, ProductImageUncheckedCreateInput>
    [7m     [0m [96m    ~~~~[0m
    The expected type comes from property 'data' which is declared here on type '{ select?: ProductImageSelect<DefaultArgs> | null; include?: ProductImageInclude<DefaultArgs> | null; data: (Without<...> & ProductImageUncheckedCreateInput) | (Without<...> & ProductImageCreateInput); }'

[96msrc/routers/orders.ts[0m:[93m400[0m:[93m13[0m - [91merror[0m[90m TS2322: [0mType '{ productId: string; variantId: string | undefined; quantity: number; unitPrice: number; totalPrice: number; specifications: Record<string, any> | undefined; }[]' is not assignable to type '(Without<OrderItemCreateWithoutOrderInput, OrderItemUncheckedCreateWithoutOrderInput> & OrderItemUncheckedCreateWithoutOrderInput) | (Without<...> & OrderItemCreateWithoutOrderInput) | OrderItemCreateWithoutOrderInput[] | OrderItemUncheckedCreateWithoutOrderInput[] | undefined'.
  Type '{ productId: string; variantId: string | undefined; quantity: number; unitPrice: number; totalPrice: number; specifications: Record<string, any> | undefined; }[]' is not assignable to type 'OrderItemCreateWithoutOrderInput[]'.
    Property 'product' is missing in type '{ productId: string; variantId: string | undefined; quantity: number; unitPrice: number; totalPrice: number; specifications: Record<string, any> | undefined; }' but required in type 'OrderItemCreateWithoutOrderInput'.

[7m400[0m             create: items.map(item => ({
[7m   [0m [91m            ~~~~~~[0m

  [96m../../node_modules/.prisma/client/index.d.ts[0m:[93m62764[0m:[93m5[0m
    [7m62764[0m     product: ProductCreateNestedOneWithoutOrderItemsInput
    [7m     [0m [96m    ~~~~~~~[0m
    'product' is declared here.

[96msrc/routers/orders.ts[0m:[93m453[0m:[93m30[0m - [91merror[0m[90m TS2339: [0mProperty 'items' does not exist on type '{ status: OrderStatus; id: string; factoryId: string; createdAt: Date; updatedAt: Date; currency: Currency; quoteId: string | null; customerEmail: string; ... 22 more ...; assignedToId: string | null; }'.

[7m453[0m             itemCount: order.items.length,
[7m   [0m [91m                             ~~~~~[0m

[96msrc/routers/orders.ts[0m:[93m465[0m:[93m22[0m - [91merror[0m[90m TS2339: [0mProperty 'items' does not exist on type '{ status: OrderStatus; id: string; factoryId: string; createdAt: Date; updatedAt: Date; currency: Currency; quoteId: string | null; customerEmail: string; ... 22 more ...; assignedToId: string | null; }'.

[7m465[0m         items: order.items,
[7m   [0m [91m                     ~~~~~[0m

[96msrc/routers/orders.ts[0m:[93m466[0m:[93m24[0m - [91merror[0m[90m TS2551: [0mProperty 'factory' does not exist on type '{ status: OrderStatus; id: string; factoryId: string; createdAt: Date; updatedAt: Date; currency: Currency; quoteId: string | null; customerEmail: string; ... 22 more ...; assignedToId: string | null; }'. Did you mean 'factoryId'?

[7m466[0m         factory: order.factory,
[7m   [0m [91m                       ~~~~~~~[0m

[96msrc/routers/users.ts[0m:[93m191[0m:[93m9[0m - [91merror[0m[90m TS2375: [0mType '{ updatedAt: Date; status?: "ACTIVE" | "INACTIVE" | "SUSPENDED" | "PENDING_VERIFICATION" | undefined; firstName?: string | undefined; lastName?: string | undefined; phone?: string | undefined; language?: string | undefined; timezone?: string | undefined; role?: "SUPER_ADMIN" | ... 6 more ... | undefined; permissions...' is not assignable to type '(Without<UserUpdateInput, UserUncheckedUpdateInput> & UserUncheckedUpdateInput) | (Without<...> & UserUpdateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Type '{ updatedAt: Date; status?: "ACTIVE" | "INACTIVE" | "SUSPENDED" | "PENDING_VERIFICATION" | undefined; firstName?: string | undefined; lastName?: string | undefined; phone?: string | undefined; language?: string | undefined; timezone?: string | undefined; role?: "SUPER_ADMIN" | ... 6 more ... | undefined; permissions...' is not assignable to type 'Without<UserUncheckedUpdateInput, UserUpdateInput> & UserUpdateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Type '{ updatedAt: Date; status?: "ACTIVE" | "INACTIVE" | "SUSPENDED" | "PENDING_VERIFICATION" | undefined; firstName?: string | undefined; lastName?: string | undefined; phone?: string | undefined; language?: string | undefined; timezone?: string | undefined; role?: "SUPER_ADMIN" | ... 6 more ... | undefined; permissions...' is not assignable to type 'UserUpdateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
      Types of property 'firstName' are incompatible.
        Type 'string | undefined' is not assignable to type 'string | StringFieldUpdateOperationsInput'.
          Type 'undefined' is not assignable to type 'string | StringFieldUpdateOperationsInput'.

[7m191[0m         data: {
[7m   [0m [91m        ~~~~[0m

  [96m../../node_modules/.prisma/client/index.d.ts[0m:[93m5395[0m:[93m5[0m
    [7m5395[0m     data: XOR<UserUpdateInput, UserUncheckedUpdateInput>
    [7m    [0m [96m    ~~~~[0m
    The expected type comes from property 'data' which is declared here on type '{ select?: UserSelect<DefaultArgs> | null; include?: UserInclude<DefaultArgs> | null; data: (Without<UserUpdateInput, UserUncheckedUpdateInput> & UserUncheckedUpdateInput) | (Without<...> & UserUpdateInput); where: UserWhereUniqueInput; }'

[96msrc/routers/users.ts[0m:[93m208[0m:[93m9[0m - [91merror[0m[90m TS2375: [0mType '{ type: "BUSINESS"; event: string; entityType: string; entityId: string; userId: string; factoryId: string | undefined; ipAddress: string; userAgent: string; metadata: { changes: string[]; targetUserId: string; targetUserEmail: string; }; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Type '{ type: "BUSINESS"; event: string; entityType: string; entityId: string; userId: string; factoryId: string | undefined; ipAddress: string; userAgent: string; metadata: { changes: string[]; targetUserId: string; targetUserEmail: string; }; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Type '{ type: "BUSINESS"; event: string; entityType: string; entityId: string; userId: string; factoryId: string | undefined; ipAddress: string; userAgent: string; metadata: { changes: string[]; targetUserId: string; targetUserEmail: string; }; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
      Types of property 'factoryId' are incompatible.
        Type 'string | undefined' is not assignable to type 'string | null'.
          Type 'undefined' is not assignable to type 'string | null'.

[7m208[0m         data: {
[7m   [0m [91m        ~~~~[0m

  [96m../../node_modules/.prisma/client/index.d.ts[0m:[93m36857[0m:[93m5[0m
    [7m36857[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>
    [7m     [0m [96m    ~~~~[0m
    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'

[96msrc/routers/users.ts[0m:[93m238[0m:[93m30[0m - [91merror[0m[90m TS2551: [0mProperty 'factory' does not exist on type '{ status: UserStatus; id: string; auth0Id: string; email: string; firstName: string; lastName: string; avatar: string | null; phone: string | null; language: string; timezone: string; ... 5 more ...; lastLoginAt: Date | null; }'. Did you mean 'factoryId'?

[7m238[0m         factory: updatedUser.factory,
[7m   [0m [91m                             ~~~~~~~[0m

[96msrc/routers/users.ts[0m:[93m285[0m:[93m9[0m - [91merror[0m[90m TS2375: [0mType '{ type: "SECURITY"; event: string; entityType: string; entityId: string; userId: string; factoryId: string | undefined; ipAddress: string; userAgent: string; metadata: { targetUserId: string; targetUserEmail: string; }; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Type '{ type: "SECURITY"; event: string; entityType: string; entityId: string; userId: string; factoryId: string | undefined; ipAddress: string; userAgent: string; metadata: { targetUserId: string; targetUserEmail: string; }; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
    Type '{ type: "SECURITY"; event: string; entityType: string; entityId: string; userId: string; factoryId: string | undefined; ipAddress: string; userAgent: string; metadata: { targetUserId: string; targetUserEmail: string; }; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
      Types of property 'factoryId' are incompatible.
        Type 'string | undefined' is not assignable to type 'string | null'.
          Type 'undefined' is not assignable to type 'string | null'.

[7m285[0m         data: {
[7m   [0m [91m        ~~~~[0m

  [96m../../node_modules/.prisma/client/index.d.ts[0m:[93m36857[0m:[93m5[0m
    [7m36857[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>
    [7m     [0m [96m    ~~~~[0m
    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'

[96msrc/server.ts[0m:[93m67[0m:[93m3[0m - [91merror[0m[90m TS2322: [0mType '(chunk?: any, encoding?: any) => void' is not assignable to type '{ (cb?: (() => void) | undefined): Response<any, Record<string, any>, number>; (chunk: any, cb?: (() => void) | undefined): Response<any, Record<string, any>, number>; (chunk: any, encoding: BufferEncoding, cb?: (() => void) | undefined): Response<...>; }'.
  Type 'void' is not assignable to type 'Response<any, Record<string, any>, number>'.

[7m67[0m   res.end = function(chunk?: any, encoding?: any) {
[7m  [0m [91m  ~~~~~~~[0m

[96msrc/server.ts[0m:[93m122[0m:[93m29[0m - [91merror[0m[90m TS7030: [0mNot all code paths return a value.

[7m122[0m app.get('/health/detailed', async (req, res) => {
[7m   [0m [91m                            ~~~~~~~~~~~~~~~~~~~~~[0m


Found 95 errors in 13 files.

Errors  Files
    23  src/lib/auth/auth0.ts[90m:8[0m
     2  src/lib/auth/jwt.ts[90m:14[0m
     4  src/lib/database/connection.ts[90m:22[0m
     3  src/lib/realtime/messaging-service.ts[90m:79[0m
     6  src/lib/realtime/supabase-client.ts[90m:122[0m
    31  src/lib/realtime/websocket-handler.ts[90m:9[0m
     2  src/lib/trpc.ts[90m:133[0m
     7  src/routers/auth.ts[90m:34[0m
     6  src/routers/factories.ts[90m:233[0m
     1  src/routers/image-management.ts[90m:96[0m
     4  src/routers/orders.ts[90m:400[0m
     4  src/routers/users.ts[90m:191[0m
     2  src/server.ts[90m:67[0m
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m Lifecycle script `type-check` failed with error:
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94mcode[39m [33m2[39m
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94mpath[39m /Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/api
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94mworkspace[39m api@1.0.0
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94mlocation[39m /Users/<USER>/PycharmProjects/FC-CHINA-F2C/apps/api
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m command failed
[1G[0K⠙[1G[0K[1mnpm[22m [31merror[39m [94mcommand[39m sh -c tsc --noEmit
[1G[0K⠙[1G[0K
