import { PrismaClient } from '@prisma/client';
import { TRPCError } from '@trpc/server';

// Types for inventory operations
export interface InventoryLocationData {
  locationName: string;
  locationCode?: string;
  locationAddress?: string;
  locationManager?: string;
  stockQuantity: number;
  reorderPoint?: number;
  maxStockLevel?: number;
  minStockLevel?: number;
  safetyStock?: number;
  averageCost?: number;
  lastCost?: number;
  currency?: string;
  isActive?: boolean;
  allowBackorders?: boolean;
  trackingEnabled?: boolean;
  leadTime?: number;
  supplierLeadTime?: number;
}

export interface StockMovementData {
  movementType: 'INBOUND' | 'OUTBOUND' | 'ADJUSTMENT' | 'TRANSFER' | 'RETURN' | 'DAMAGED' | 'EXPIRED';
  quantity: number;
  reason?: string;
  notes?: string;
  batchNumber?: string;
  expiryDate?: Date;
  unitCost?: number;
  totalCost?: number;
  currency?: string;
  referenceId?: string;
  referenceType?: string;
  fromLocationId?: string;
  toLocationId?: string;
}

export interface ReservationData {
  reservationType: 'ORDER' | 'QUOTE' | 'MANUAL' | 'TRANSFER' | 'QUALITY_HOLD';
  quantity: number;
  reservedUntil: Date;
  referenceId?: string;
  referenceType?: string;
  notes?: string;
}

export interface InventoryAlert {
  alertType: 'LOW_STOCK' | 'OUT_OF_STOCK' | 'OVERSTOCK' | 'REORDER_POINT' | 'EXPIRY_WARNING' | 'SLOW_MOVING' | 'FAST_MOVING' | 'COST_VARIANCE';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  title: string;
  message: string;
  triggerValue?: number;
  currentValue?: number;
  threshold?: number;
}

// Inventory calculation utilities
export function calculateAvailableQuantity(stockQuantity: number, reservedQuantity: number): number {
  return Math.max(0, stockQuantity - reservedQuantity);
}

export function calculateReorderQuantity(
  currentStock: number,
  reorderPoint: number,
  maxStockLevel: number,
  leadTime: number = 7,
  averageDailyUsage: number = 1
): number {
  const leadTimeStock = leadTime * averageDailyUsage;
  const targetStock = Math.min(maxStockLevel, reorderPoint + leadTimeStock);
  return Math.max(0, targetStock - currentStock);
}

export function calculateInventoryTurnover(
  costOfGoodsSold: number,
  averageInventoryValue: number
): number {
  if (averageInventoryValue === 0) return 0;
  return costOfGoodsSold / averageInventoryValue;
}

export function calculateDaysOfInventory(
  averageInventoryValue: number,
  costOfGoodsSold: number
): number {
  if (costOfGoodsSold === 0) return 0;
  return (averageInventoryValue / costOfGoodsSold) * 365;
}

export function calculateStockoutRisk(
  currentStock: number,
  averageDailyUsage: number,
  leadTime: number,
  safetyStock: number = 0
): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
  const daysOfStock = currentStock / Math.max(1, averageDailyUsage);
  const totalLeadTime = leadTime + (safetyStock / Math.max(1, averageDailyUsage));
  
  if (daysOfStock <= 0) return 'CRITICAL';
  if (daysOfStock <= totalLeadTime * 0.5) return 'HIGH';
  if (daysOfStock <= totalLeadTime) return 'MEDIUM';
  return 'LOW';
}

// Inventory validation utilities
export function validateInventoryLocation(data: InventoryLocationData): string[] {
  const errors: string[] = [];
  
  if (!data.locationName || data.locationName.trim().length === 0) {
    errors.push('Location name is required');
  }
  
  if (data.stockQuantity < 0) {
    errors.push('Stock quantity cannot be negative');
  }
  
  if (data.reorderPoint && data.reorderPoint < 0) {
    errors.push('Reorder point cannot be negative');
  }
  
  if (data.maxStockLevel && data.minStockLevel && data.maxStockLevel < data.minStockLevel) {
    errors.push('Maximum stock level cannot be less than minimum stock level');
  }
  
  if (data.reorderPoint && data.minStockLevel && data.reorderPoint < data.minStockLevel) {
    errors.push('Reorder point should not be less than minimum stock level');
  }
  
  if (data.leadTime && data.leadTime < 0) {
    errors.push('Lead time cannot be negative');
  }
  
  if (data.averageCost && data.averageCost < 0) {
    errors.push('Average cost cannot be negative');
  }
  
  return errors;
}

export function validateStockMovement(data: StockMovementData, currentStock: number): string[] {
  const errors: string[] = [];
  
  if (!data.movementType) {
    errors.push('Movement type is required');
  }
  
  if (data.quantity === 0) {
    errors.push('Quantity cannot be zero');
  }
  
  // For outbound movements, check if sufficient stock is available
  if (['OUTBOUND', 'TRANSFER', 'DAMAGED', 'EXPIRED'].includes(data.movementType) && data.quantity > 0) {
    if (Math.abs(data.quantity) > currentStock) {
      errors.push('Insufficient stock for outbound movement');
    }
  }
  
  if (data.unitCost && data.unitCost < 0) {
    errors.push('Unit cost cannot be negative');
  }
  
  if (data.totalCost && data.totalCost < 0) {
    errors.push('Total cost cannot be negative');
  }
  
  if (data.expiryDate && data.expiryDate < new Date()) {
    errors.push('Expiry date cannot be in the past');
  }
  
  return errors;
}

export function validateReservation(data: ReservationData, availableQuantity: number): string[] {
  const errors: string[] = [];
  
  if (!data.reservationType) {
    errors.push('Reservation type is required');
  }
  
  if (data.quantity <= 0) {
    errors.push('Reservation quantity must be positive');
  }
  
  if (data.quantity > availableQuantity) {
    errors.push('Cannot reserve more than available quantity');
  }
  
  if (data.reservedUntil <= new Date()) {
    errors.push('Reservation end date must be in the future');
  }
  
  return errors;
}

// Alert generation utilities
export function generateInventoryAlerts(
  locationData: {
    stockQuantity: number;
    reservedQuantity: number;
    reorderPoint?: number;
    minStockLevel?: number;
    maxStockLevel?: number;
    locationName: string;
    productName: string;
  }
): InventoryAlert[] {
  const alerts: InventoryAlert[] = [];
  const availableQuantity = calculateAvailableQuantity(locationData.stockQuantity, locationData.reservedQuantity);
  
  // Out of stock alert
  if (locationData.stockQuantity === 0) {
    alerts.push({
      alertType: 'OUT_OF_STOCK',
      severity: 'CRITICAL',
      title: 'Out of Stock',
      message: `${locationData.productName} is out of stock at ${locationData.locationName}`,
      currentValue: locationData.stockQuantity,
      threshold: 0,
    });
  }
  
  // Low stock alert
  if (locationData.minStockLevel && locationData.stockQuantity <= locationData.minStockLevel && locationData.stockQuantity > 0) {
    alerts.push({
      alertType: 'LOW_STOCK',
      severity: 'HIGH',
      title: 'Low Stock Warning',
      message: `${locationData.productName} stock is below minimum level at ${locationData.locationName}`,
      currentValue: locationData.stockQuantity,
      threshold: locationData.minStockLevel,
    });
  }
  
  // Reorder point alert
  if (locationData.reorderPoint && locationData.stockQuantity <= locationData.reorderPoint) {
    alerts.push({
      alertType: 'REORDER_POINT',
      severity: 'MEDIUM',
      title: 'Reorder Point Reached',
      message: `${locationData.productName} has reached reorder point at ${locationData.locationName}`,
      currentValue: locationData.stockQuantity,
      threshold: locationData.reorderPoint,
    });
  }
  
  // Overstock alert
  if (locationData.maxStockLevel && locationData.stockQuantity > locationData.maxStockLevel) {
    alerts.push({
      alertType: 'OVERSTOCK',
      severity: 'MEDIUM',
      title: 'Overstock Warning',
      message: `${locationData.productName} stock exceeds maximum level at ${locationData.locationName}`,
      currentValue: locationData.stockQuantity,
      threshold: locationData.maxStockLevel,
    });
  }
  
  return alerts;
}

// Inventory analytics utilities
export function calculateInventoryMetrics(locations: Array<{
  stockQuantity: number;
  reservedQuantity: number;
  averageCost?: number;
  lastCost?: number;
}>) {
  const totalStock = locations.reduce((sum, loc) => sum + loc.stockQuantity, 0);
  const totalReserved = locations.reduce((sum, loc) => sum + loc.reservedQuantity, 0);
  const totalAvailable = totalStock - totalReserved;
  
  const totalValue = locations.reduce((sum, loc) => {
    const cost = loc.averageCost || loc.lastCost || 0;
    return sum + (loc.stockQuantity * cost);
  }, 0);
  
  const averageCostPerUnit = totalStock > 0 ? totalValue / totalStock : 0;
  
  return {
    totalStock,
    totalReserved,
    totalAvailable,
    totalValue,
    averageCostPerUnit,
    locationCount: locations.length,
    utilizationRate: totalStock > 0 ? (totalReserved / totalStock) * 100 : 0,
  };
}

export function calculateLocationPerformance(movements: Array<{
  movementType: string;
  quantity: number;
  createdAt: Date;
}>, days: number = 30) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);
  
  const recentMovements = movements.filter(m => m.createdAt >= cutoffDate);
  
  const inbound = recentMovements
    .filter(m => m.movementType === 'INBOUND')
    .reduce((sum, m) => sum + m.quantity, 0);
    
  const outbound = recentMovements
    .filter(m => m.movementType === 'OUTBOUND')
    .reduce((sum, m) => sum + Math.abs(m.quantity), 0);
    
  const adjustments = recentMovements
    .filter(m => m.movementType === 'ADJUSTMENT')
    .reduce((sum, m) => sum + Math.abs(m.quantity), 0);
  
  return {
    inboundVolume: inbound,
    outboundVolume: outbound,
    adjustmentVolume: adjustments,
    netMovement: inbound - outbound,
    movementFrequency: recentMovements.length,
    averageDailyUsage: outbound / days,
  };
}
