// Advanced pricing utilities and calculations
import { Decimal } from '@prisma/client/runtime/library';

// Supported regions for regional pricing
export const PRICING_REGIONS = {
  'US': 'United States',
  'EU': 'European Union',
  'APAC': 'Asia-Pacific',
  'LATAM': 'Latin America',
  'MENA': 'Middle East & North Africa',
  'GLOBAL': 'Global Default',
} as const;

// Pricing adjustment types
export const ADJUSTMENT_TYPES = {
  'PERCENTAGE': 'Percentage-based adjustment',
  'FIXED': 'Fixed amount adjustment',
} as const;

// Cost categories for breakdown analysis
export const COST_CATEGORIES = {
  'MATERIAL': 'Material costs',
  'LABOR': 'Labor costs',
  'OVERHEAD': 'Overhead costs',
  'PACKAGING': 'Packaging costs',
  'SHIPPING': 'Shipping costs',
  'MARKETING': 'Marketing costs',
} as const;

// Currency exchange rates (in a real system, this would come from an API)
export const EXCHANGE_RATES = {
  'USD': 1.0,
  'EUR': 0.85,
  'GBP': 0.73,
  'CNY': 7.2,
  'JPY': 110.0,
  'KRW': 1200.0,
  'CAD': 1.25,
  'AUD': 1.35,
} as const;

/**
 * Calculate price with volume discount based on price breaks
 */
export function calculateVolumePrice(
  basePrice: number,
  quantity: number,
  priceBreaks: Array<{
    minQuantity: number;
    unitPrice: number;
  }>
): {
  unitPrice: number;
  totalPrice: number;
  appliedBreak: {
    minQuantity: number;
    unitPrice: number;
  } | undefined;
} {
  // Sort price breaks by minimum quantity (descending)
  const sortedBreaks = priceBreaks
    .filter(pb => quantity >= pb.minQuantity)
    .sort((a, b) => b.minQuantity - a.minQuantity);

  const appliedBreak = sortedBreaks[0];
  const unitPrice = appliedBreak ? appliedBreak.unitPrice : basePrice;
  
  return {
    unitPrice,
    totalPrice: unitPrice * quantity,
    appliedBreak: appliedBreak || undefined,
  };
}

/**
 * Apply regional pricing adjustments
 */
export function applyRegionalPricing(
  basePrice: number,
  regionalPricing?: {
    basePrice?: number;
    markup?: number;
    fixedAdjustment?: number;
  }
): number {
  if (!regionalPricing) {
    return basePrice;
  }

  let adjustedPrice = regionalPricing.basePrice || basePrice;

  // Apply percentage markup/discount
  if (regionalPricing.markup !== undefined) {
    adjustedPrice = adjustedPrice * (1 + regionalPricing.markup / 100);
  }

  // Apply fixed adjustment
  if (regionalPricing.fixedAdjustment !== undefined) {
    adjustedPrice += regionalPricing.fixedAdjustment;
  }

  return Math.max(0, adjustedPrice); // Ensure price is not negative
}

/**
 * Apply seasonal pricing adjustments
 */
export function applySeasonalPricing(
  basePrice: number,
  quantity: number,
  seasonalPricing: Array<{
    adjustmentType: string;
    adjustmentValue: number;
    minQuantity?: number;
    maxQuantity?: number;
    priority: number;
    startDate: Date;
    endDate: Date;
    isActive: boolean;
  }>,
  currentDate: Date = new Date()
): {
  adjustedPrice: number;
  appliedAdjustments: Array<{
    name?: string;
    adjustmentType: string;
    adjustmentValue: number;
    priority: number;
  }>;
} {
  // Filter active seasonal pricing that applies to current date and quantity
  const applicableAdjustments = seasonalPricing
    .filter(sp => 
      sp.isActive &&
      currentDate >= sp.startDate &&
      currentDate <= sp.endDate &&
      (sp.minQuantity === undefined || quantity >= sp.minQuantity) &&
      (sp.maxQuantity === undefined || quantity <= sp.maxQuantity)
    )
    .sort((a, b) => b.priority - a.priority); // Sort by priority (highest first)

  let adjustedPrice = basePrice;
  const appliedAdjustments: Array<{
    name?: string;
    adjustmentType: string;
    adjustmentValue: number;
    priority: number;
  }> = [];

  // Apply adjustments in priority order
  for (const adjustment of applicableAdjustments) {
    if (adjustment.adjustmentType === 'PERCENTAGE') {
      adjustedPrice = adjustedPrice * (1 + adjustment.adjustmentValue / 100);
    } else if (adjustment.adjustmentType === 'FIXED') {
      adjustedPrice += adjustment.adjustmentValue;
    }

    appliedAdjustments.push({
      adjustmentType: adjustment.adjustmentType,
      adjustmentValue: adjustment.adjustmentValue,
      priority: adjustment.priority,
    });
  }

  return {
    adjustedPrice: Math.max(0, adjustedPrice),
    appliedAdjustments,
  };
}

/**
 * Calculate comprehensive pricing with all adjustments
 */
export function calculateComprehensivePrice(
  basePrice: number,
  quantity: number,
  options: {
    priceBreaks?: Array<{
      minQuantity: number;
      unitPrice: number;
    }>;
    regionalPricing?: {
      basePrice?: number;
      markup?: number;
      fixedAdjustment?: number;
    };
    seasonalPricing?: Array<{
      adjustmentType: string;
      adjustmentValue: number;
      minQuantity?: number;
      maxQuantity?: number;
      priority: number;
      startDate: Date;
      endDate: Date;
      isActive: boolean;
    }>;
    currentDate?: Date;
  } = {}
): {
  basePrice: number;
  unitPrice: number;
  totalPrice: number;
  breakdown: {
    volumeDiscount?: {
      unitPrice: number;
      appliedBreak?: {
        minQuantity: number;
        unitPrice: number;
      };
    };
    regionalAdjustment?: {
      adjustedPrice: number;
    };
    seasonalAdjustment?: {
      adjustedPrice: number;
      appliedAdjustments: Array<{
        adjustmentType: string;
        adjustmentValue: number;
        priority: number;
      }>;
    };
  };
} {
  let currentPrice = basePrice;
  const breakdown: any = {};

  // 1. Apply volume pricing (price breaks)
  if (options.priceBreaks && options.priceBreaks.length > 0) {
    const volumeResult = calculateVolumePrice(currentPrice, quantity, options.priceBreaks);
    currentPrice = volumeResult.unitPrice;
    breakdown.volumeDiscount = volumeResult;
  }

  // 2. Apply regional pricing
  if (options.regionalPricing) {
    const regionalPrice = applyRegionalPricing(currentPrice, options.regionalPricing);
    breakdown.regionalAdjustment = { adjustedPrice: regionalPrice };
    currentPrice = regionalPrice;
  }

  // 3. Apply seasonal pricing
  if (options.seasonalPricing && options.seasonalPricing.length > 0) {
    const seasonalResult = applySeasonalPricing(
      currentPrice,
      quantity,
      options.seasonalPricing,
      options.currentDate
    );
    breakdown.seasonalAdjustment = seasonalResult;
    currentPrice = seasonalResult.adjustedPrice;
  }

  return {
    basePrice,
    unitPrice: currentPrice,
    totalPrice: currentPrice * quantity,
    breakdown,
  };
}

/**
 * Calculate cost breakdown and suggested pricing
 */
export function calculateCostBreakdown(costs: {
  materialCost: number;
  laborCost: number;
  overheadCost: number;
  packagingCost?: number;
  shippingCost?: number;
  marketingCost?: number;
  targetMargin: number; // Percentage
  quantityBasis?: number;
}): {
  totalCost: number;
  suggestedPrice: number;
  actualMargin: number;
  costPerUnit: number;
  breakdown: {
    [key: string]: {
      amount: number;
      percentage: number;
    };
  };
} {
  const {
    materialCost,
    laborCost,
    overheadCost,
    packagingCost = 0,
    shippingCost = 0,
    marketingCost = 0,
    targetMargin,
    quantityBasis = 1,
  } = costs;

  const totalCost = materialCost + laborCost + overheadCost + packagingCost + shippingCost + marketingCost;
  const costPerUnit = totalCost / quantityBasis;
  const suggestedPrice = costPerUnit * (1 + targetMargin / 100);
  const actualMargin = ((suggestedPrice - costPerUnit) / suggestedPrice) * 100;

  const breakdown = {
    material: {
      amount: materialCost,
      percentage: (materialCost / totalCost) * 100,
    },
    labor: {
      amount: laborCost,
      percentage: (laborCost / totalCost) * 100,
    },
    overhead: {
      amount: overheadCost,
      percentage: (overheadCost / totalCost) * 100,
    },
    packaging: {
      amount: packagingCost,
      percentage: (packagingCost / totalCost) * 100,
    },
    shipping: {
      amount: shippingCost,
      percentage: (shippingCost / totalCost) * 100,
    },
    marketing: {
      amount: marketingCost,
      percentage: (marketingCost / totalCost) * 100,
    },
  };

  return {
    totalCost,
    suggestedPrice,
    actualMargin,
    costPerUnit,
    breakdown,
  };
}

/**
 * Convert price between currencies
 */
export function convertCurrency(
  amount: number,
  fromCurrency: keyof typeof EXCHANGE_RATES,
  toCurrency: keyof typeof EXCHANGE_RATES
): number {
  if (fromCurrency === toCurrency) {
    return amount;
  }

  // Convert to USD first, then to target currency
  const usdAmount = amount / EXCHANGE_RATES[fromCurrency];
  return usdAmount * EXCHANGE_RATES[toCurrency];
}

/**
 * Validate pricing data
 */
export function validatePricingData(data: {
  basePrice?: number;
  priceBreaks?: Array<{ minQuantity: number; unitPrice: number }>;
  regionalPricing?: Array<{ region: string; basePrice: number }>;
}): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Validate base price
  if (data.basePrice !== undefined && data.basePrice <= 0) {
    errors.push('Base price must be greater than 0');
  }

  // Validate price breaks
  if (data.priceBreaks) {
    const sortedBreaks = [...data.priceBreaks].sort((a, b) => a.minQuantity - b.minQuantity);
    
    for (let i = 0; i < sortedBreaks.length; i++) {
      const current = sortedBreaks[i];

      if (!current) continue;

      if (current.minQuantity <= 0) {
        errors.push(`Price break ${i + 1}: Minimum quantity must be greater than 0`);
      }

      if (current.unitPrice <= 0) {
        errors.push(`Price break ${i + 1}: Unit price must be greater than 0`);
      }

      // Check for overlapping quantities
      const previous = sortedBreaks[i - 1];
      if (i > 0 && previous && current.minQuantity <= previous.minQuantity) {
        errors.push(`Price break ${i + 1}: Minimum quantity must be greater than previous break`);
      }
    }
  }

  // Validate regional pricing
  if (data.regionalPricing) {
    const regions = new Set<string>();
    
    for (const pricing of data.regionalPricing) {
      if (regions.has(pricing.region)) {
        errors.push(`Duplicate regional pricing for region: ${pricing.region}`);
      }
      regions.add(pricing.region);
      
      if (pricing.basePrice <= 0) {
        errors.push(`Regional pricing for ${pricing.region}: Base price must be greater than 0`);
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}
