import { supabaseAdmin, realtimeManager } from './supabase-client';
import { logger } from '../logging/logger';
import { prisma } from '../database/connection';

export interface MessageNotification {
  type: 'NEW_MESSAGE' | 'MESSAGE_READ' | 'TYPING_START' | 'TYPING_STOP';
  conversationId: string;
  senderId: string;
  messageId?: string;
  content?: string;
  timestamp: string;
}

export interface ConversationNotification {
  type: 'CONVERSATION_CREATED' | 'CONVERSATION_UPDATED' | 'PARTICIPANT_JOINED' | 'PARTICIPANT_LEFT';
  conversationId: string;
  userId: string;
  data?: any;
  timestamp: string;
}

export class MessagingService {
  // Send a real-time notification to conversation participants
  async notifyConversationParticipants(
    conversationId: string,
    notification: MessageNotification,
    excludeUserId?: string
  ): Promise<void> {
    try {
      // Get conversation participants
      const participants = await prisma.conversationParticipant.findMany({
        where: {
          conversationId,
          leftAt: null, // Only active participants
          ...(excludeUserId && {
            userId: {
              not: excludeUserId,
            },
          }),
        },
        include: {
          user: {
            select: {
              id: true,
              auth0Id: true,
              email: true,
            },
          },
        },
      });

      // Send notification to each participant via Supabase Realtime
      for (const participant of participants) {
        await this.sendRealtimeNotification(
          `user:${participant.userId}`,
          notification
        );
      }

      logger.info('Conversation notification sent', {
        conversationId,
        notificationType: notification.type,
        participantCount: participants.length,
      });
    } catch (error) {
      logger.error('Failed to notify conversation participants', {
        conversationId,
        error,
      });
    }
  }

  // Send a real-time notification to a specific channel
  private async sendRealtimeNotification(
    channel: string,
    notification: MessageNotification | ConversationNotification
  ): Promise<void> {
    try {
      const { error } = await supabaseAdmin.realtime
        .channel(channel)
        .send({
          type: 'broadcast',
          event: 'notification',
          payload: notification,
        });

      if (error) {
        logger.error('Failed to send realtime notification', {
          channel,
          error,
        });
      }
    } catch (error) {
      logger.error('Realtime notification error', {
        channel,
        error,
      });
    }
  }

  // Handle new message creation with real-time updates
  async handleNewMessage(messageData: {
    id: string;
    content: string;
    conversationId: string;
    senderId: string;
    messageType: string;
  }): Promise<void> {
    try {
      // Update conversation last message timestamp and count
      await prisma.conversation.update({
        where: { id: messageData.conversationId },
        data: {
          lastMessageAt: new Date(),
          messageCount: {
            increment: 1,
          },
        },
      });

      // Update unread counts for other participants
      await prisma.conversationParticipant.updateMany({
        where: {
          conversationId: messageData.conversationId,
          userId: {
            not: messageData.senderId,
          },
          leftAt: null,
        },
        data: {
          unreadCount: {
            increment: 1,
          },
        },
      });

      // Send real-time notification
      const notification: MessageNotification = {
        type: 'NEW_MESSAGE',
        conversationId: messageData.conversationId,
        senderId: messageData.senderId,
        messageId: messageData.id,
        content: messageData.content,
        timestamp: new Date().toISOString(),
      };

      await this.notifyConversationParticipants(
        messageData.conversationId,
        notification,
        messageData.senderId
      );

      logger.info('New message handled', {
        messageId: messageData.id,
        conversationId: messageData.conversationId,
      });
    } catch (error) {
      logger.error('Failed to handle new message', {
        messageId: messageData.id,
        error,
      });
    }
  }

  // Handle message read status updates
  async handleMessageRead(
    messageId: string,
    userId: string,
    conversationId: string
  ): Promise<void> {
    try {
      // Create read receipt
      await prisma.messageReadReceipt.upsert({
        where: {
          messageId_userId: {
            messageId,
            userId,
          },
        },
        create: {
          messageId,
          userId,
          factoryId: await this.getUserFactoryId(userId),
          readAt: new Date(),
        },
        update: {
          readAt: new Date(),
        },
      });

      // Update participant unread count
      const participant = await prisma.conversationParticipant.findUnique({
        where: {
          conversationId_userId: {
            conversationId,
            userId,
          },
        },
      });

      if (participant && participant.unreadCount > 0) {
        await prisma.conversationParticipant.update({
          where: {
            conversationId_userId: {
              conversationId,
              userId,
            },
          },
          data: {
            unreadCount: Math.max(0, participant.unreadCount - 1),
            lastReadAt: new Date(),
          },
        });
      }

      // Send real-time notification
      const notification: MessageNotification = {
        type: 'MESSAGE_READ',
        conversationId,
        senderId: userId,
        messageId,
        timestamp: new Date().toISOString(),
      };

      await this.notifyConversationParticipants(
        conversationId,
        notification,
        userId
      );

      logger.info('Message read handled', {
        messageId,
        userId,
        conversationId,
      });
    } catch (error) {
      logger.error('Failed to handle message read', {
        messageId,
        userId,
        error,
      });
    }
  }

  // Handle typing indicators
  async handleTypingIndicator(
    conversationId: string,
    userId: string,
    isTyping: boolean
  ): Promise<void> {
    try {
      const notification: MessageNotification = {
        type: isTyping ? 'TYPING_START' : 'TYPING_STOP',
        conversationId,
        senderId: userId,
        timestamp: new Date().toISOString(),
      };

      await this.notifyConversationParticipants(
        conversationId,
        notification,
        userId
      );

      logger.debug('Typing indicator handled', {
        conversationId,
        userId,
        isTyping,
      });
    } catch (error) {
      logger.error('Failed to handle typing indicator', {
        conversationId,
        userId,
        isTyping,
        error,
      });
    }
  }

  // Helper method to get user's factory ID
  private async getUserFactoryId(userId: string): Promise<string> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { factoryId: true },
    });

    if (!user?.factoryId) {
      throw new Error(`User ${userId} has no factory association`);
    }

    return user.factoryId;
  }

  // Create a new conversation with participants
  async createConversation(data: {
    type: string;
    subject?: string;
    factoryId: string;
    participantIds: string[];
    createdBy: string;
    contextId?: string;
    contextType?: 'quote' | 'order' | 'inquiry' | 'product';
  }): Promise<string> {
    try {
      const conversation = await prisma.conversation.create({
        data: {
          type: data.type as any,
          subject: data.subject,
          factoryId: data.factoryId,
          ...(data.contextType === 'quote' && { quoteId: data.contextId }),
          ...(data.contextType === 'order' && { orderId: data.contextId }),
          ...(data.contextType === 'inquiry' && { inquiryId: data.contextId }),
          ...(data.contextType === 'product' && { productId: data.contextId }),
        },
      });

      // Add participants
      const participantData = data.participantIds.map((userId, index) => ({
        userId,
        conversationId: conversation.id,
        role: userId === data.createdBy ? 'OWNER' : 'MEMBER',
        canWrite: true,
        canRead: true,
      }));

      await prisma.conversationParticipant.createMany({
        data: participantData,
      });

      // Send real-time notification
      const notification: ConversationNotification = {
        type: 'CONVERSATION_CREATED',
        conversationId: conversation.id,
        userId: data.createdBy,
        timestamp: new Date().toISOString(),
      };

      for (const participantId of data.participantIds) {
        await this.sendRealtimeNotification(
          `user:${participantId}`,
          notification
        );
      }

      logger.info('Conversation created', {
        conversationId: conversation.id,
        participantCount: data.participantIds.length,
      });

      return conversation.id;
    } catch (error) {
      logger.error('Failed to create conversation', {
        error,
      });
      throw error;
    }
  }
}

// Global messaging service instance
export const messagingService = new MessagingService();
