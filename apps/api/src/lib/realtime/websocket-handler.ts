import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import { verifyJWT } from '../auth/jwt';
import { messagingService } from './messaging-service';
import { realtimeManager } from './supabase-client';
import { logger } from '../logging/logger';
import { prisma } from '../database/connection';

export interface AuthenticatedSocket extends Socket {
  userId: string;
  factoryId: string;
  auth0Id: string;
  email: string;
  role: string;
}

export class WebSocketHandler {
  private io: SocketIOServer;
  private connectedUsers: Map<string, string> = new Map(); // userId -> socketId

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.NODE_ENV === 'production' 
          ? ['https://fc-china.com', 'https://www.fc-china.com']
          : ['http://localhost:3000', 'http://localhost:3001'],
        methods: ['GET', 'POST'],
        credentials: true,
      },
      transports: ['websocket', 'polling'],
    });

    this.setupMiddleware();
    this.setupEventHandlers();
  }

  private setupMiddleware() {
    // Authentication middleware
    this.io.use(async (socket: any, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        // Verify JWT token
        const payload = verifyJWT(token);
        
        // Get user details from database
        const user = await prisma.user.findUnique({
          where: { id: payload.userId },
          include: {
            factory: {
              select: {
                id: true,
                name: true,
                status: true,
              },
            },
          },
        });

        if (!user) {
          return next(new Error('User not found'));
        }

        if (!user.factoryId) {
          return next(new Error('User has no factory association'));
        }

        // Attach user data to socket
        socket.userId = user.id;
        socket.factoryId = user.factoryId;
        socket.auth0Id = user.auth0Id;
        socket.email = user.email;
        socket.role = user.role;

        logger.info('Socket authenticated', {
          socketId: socket.id,
          userId: user.id,
          factoryId: user.factoryId,
        });

        next();
      } catch (error) {
        logger.error('Socket authentication failed', {
          socketId: socket.id,
          error: error.message,
        });
        next(new Error('Authentication failed'));
      }
    });
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      logger.info('User connected', {
        socketId: socket.id,
        userId: socket.userId,
        factoryId: socket.factoryId,
      });

      // Track connected user
      this.connectedUsers.set(socket.userId, socket.id);

      // Join user to their factory room
      socket.join(`factory:${socket.factoryId}`);
      socket.join(`user:${socket.userId}`);

      // Handle joining conversation rooms
      socket.on('join_conversation', async (data: { conversationId: string }) => {
        try {
          // Verify user has access to this conversation
          const participant = await prisma.conversationParticipant.findUnique({
            where: {
              conversationId_userId: {
                conversationId: data.conversationId,
                userId: socket.userId,
              },
            },
          });

          if (!participant) {
            socket.emit('error', { message: 'Access denied to conversation' });
            return;
          }

          socket.join(`conversation:${data.conversationId}`);
          socket.emit('conversation_joined', { conversationId: data.conversationId });

          logger.info('User joined conversation', {
            userId: socket.userId,
            conversationId: data.conversationId,
          });
        } catch (error) {
          logger.error('Failed to join conversation', {
            userId: socket.userId,
            conversationId: data.conversationId,
            error,
          });
          socket.emit('error', { message: 'Failed to join conversation' });
        }
      });

      // Handle leaving conversation rooms
      socket.on('leave_conversation', (data: { conversationId: string }) => {
        socket.leave(`conversation:${data.conversationId}`);
        socket.emit('conversation_left', { conversationId: data.conversationId });

        logger.info('User left conversation', {
          userId: socket.userId,
          conversationId: data.conversationId,
        });
      });

      // Handle typing indicators
      socket.on('typing_start', async (data: { conversationId: string }) => {
        try {
          await messagingService.handleTypingIndicator(
            data.conversationId,
            socket.userId,
            true
          );

          socket.to(`conversation:${data.conversationId}`).emit('user_typing', {
            userId: socket.userId,
            conversationId: data.conversationId,
            isTyping: true,
          });
        } catch (error) {
          logger.error('Failed to handle typing start', {
            userId: socket.userId,
            conversationId: data.conversationId,
            error,
          });
        }
      });

      socket.on('typing_stop', async (data: { conversationId: string }) => {
        try {
          await messagingService.handleTypingIndicator(
            data.conversationId,
            socket.userId,
            false
          );

          socket.to(`conversation:${data.conversationId}`).emit('user_typing', {
            userId: socket.userId,
            conversationId: data.conversationId,
            isTyping: false,
          });
        } catch (error) {
          logger.error('Failed to handle typing stop', {
            userId: socket.userId,
            conversationId: data.conversationId,
            error,
          });
        }
      });

      // Handle message read receipts
      socket.on('mark_message_read', async (data: { 
        messageId: string; 
        conversationId: string; 
      }) => {
        try {
          await messagingService.handleMessageRead(
            data.messageId,
            socket.userId,
            data.conversationId
          );

          socket.to(`conversation:${data.conversationId}`).emit('message_read', {
            messageId: data.messageId,
            userId: socket.userId,
            conversationId: data.conversationId,
            readAt: new Date().toISOString(),
          });
        } catch (error) {
          logger.error('Failed to mark message as read', {
            userId: socket.userId,
            messageId: data.messageId,
            error,
          });
        }
      });

      // Handle user presence updates
      socket.on('update_presence', (data: { status: 'online' | 'away' | 'busy' }) => {
        socket.to(`factory:${socket.factoryId}`).emit('user_presence_updated', {
          userId: socket.userId,
          status: data.status,
          timestamp: new Date().toISOString(),
        });
      });

      // Handle disconnection
      socket.on('disconnect', (reason) => {
        logger.info('User disconnected', {
          socketId: socket.id,
          userId: socket.userId,
          factoryId: socket.factoryId,
          reason,
        });

        // Remove from connected users
        this.connectedUsers.delete(socket.userId);

        // Notify factory members that user went offline
        socket.to(`factory:${socket.factoryId}`).emit('user_presence_updated', {
          userId: socket.userId,
          status: 'offline',
          timestamp: new Date().toISOString(),
        });
      });

      // Handle errors
      socket.on('error', (error) => {
        logger.error('Socket error', {
          socketId: socket.id,
          userId: socket.userId,
          error,
        });
      });
    });
  }

  // Send message to specific user
  public sendToUser(userId: string, event: string, data: any) {
    const socketId = this.connectedUsers.get(userId);
    if (socketId) {
      this.io.to(socketId).emit(event, data);
      return true;
    }
    return false;
  }

  // Send message to conversation participants
  public sendToConversation(conversationId: string, event: string, data: any) {
    this.io.to(`conversation:${conversationId}`).emit(event, data);
  }

  // Send message to factory members
  public sendToFactory(factoryId: string, event: string, data: any) {
    this.io.to(`factory:${factoryId}`).emit(event, data);
  }

  // Get connected users count
  public getConnectedUsersCount(): number {
    return this.connectedUsers.size;
  }

  // Check if user is online
  public isUserOnline(userId: string): boolean {
    return this.connectedUsers.has(userId);
  }

  // Get Socket.IO server instance
  public getIO(): SocketIOServer {
    return this.io;
  }
}

// Global WebSocket handler instance
let webSocketHandler: WebSocketHandler | null = null;

export function initializeWebSocketHandler(server: HTTPServer): WebSocketHandler {
  if (!webSocketHandler) {
    webSocketHandler = new WebSocketHandler(server);
    logger.info('WebSocket handler initialized');
  }
  return webSocketHandler;
}

export function getWebSocketHandler(): WebSocketHandler | null {
  return webSocketHandler;
}
