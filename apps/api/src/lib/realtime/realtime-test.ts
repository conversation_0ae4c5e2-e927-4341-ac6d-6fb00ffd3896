import { validateRealtimeConnection, supabaseAdmin } from './supabase-client';
import { logger } from '../logging/logger';

export async function testRealtimeInfrastructure(): Promise<{
  success: boolean;
  results: {
    connection: boolean;
    tablesEnabled: boolean;
    rlsPolicies: boolean;
    subscriptionTest: boolean;
  };
  errors: string[];
}> {
  const results = {
    connection: false,
    tablesEnabled: false,
    rlsPolicies: false,
    subscriptionTest: false,
  };
  const errors: string[] = [];

  try {
    // Test 1: Basic connection
    logger.info('Testing Realtime connection...');
    results.connection = await validateRealtimeConnection();
    if (!results.connection) {
      errors.push('Failed to establish Realtime connection');
    }

    // Test 2: Check if messaging tables are enabled for Realtime
    logger.info('Testing Realtime table enablement...');
    try {
      const { data: publicationTables, error } = await supabaseAdmin
        .rpc('get_publication_tables', { publication_name: 'supabase_realtime' });

      if (error) {
        errors.push(`Failed to check publication tables: ${error.message}`);
      } else {
        const messagingTables = ['conversations', 'messages', 'conversation_participants', 'message_attachments', 'message_read_receipts'];
        const enabledTables = publicationTables?.map((t: any) => t.tablename) || [];
        const missingTables = messagingTables.filter(table => !enabledTables.includes(table));
        
        if (missingTables.length === 0) {
          results.tablesEnabled = true;
        } else {
          errors.push(`Missing Realtime tables: ${missingTables.join(', ')}`);
        }
      }
    } catch (error) {
      // Fallback: Direct query to check publication tables
      try {
        const { data, error: queryError } = await supabaseAdmin
          .from('pg_publication_tables')
          .select('tablename')
          .eq('pubname', 'supabase_realtime')
          .eq('schemaname', 'public');

        if (queryError) {
          errors.push(`Failed to query publication tables: ${queryError.message}`);
        } else {
          const messagingTables = ['conversations', 'messages', 'conversation_participants', 'message_attachments', 'message_read_receipts'];
          const enabledTables = data?.map(t => t.tablename) || [];
          const missingTables = messagingTables.filter(table => !enabledTables.includes(table));
          
          if (missingTables.length === 0) {
            results.tablesEnabled = true;
          } else {
            errors.push(`Missing Realtime tables: ${missingTables.join(', ')}`);
          }
        }
      } catch (fallbackError) {
        errors.push(`Failed to check Realtime table enablement: ${fallbackError}`);
      }
    }

    // Test 3: Check RLS policies
    logger.info('Testing RLS policies...');
    try {
      const { data: policies, error } = await supabaseAdmin
        .from('pg_policies')
        .select('tablename, policyname')
        .in('tablename', ['conversations', 'messages', 'conversation_participants', 'message_attachments', 'message_read_receipts']);

      if (error) {
        errors.push(`Failed to check RLS policies: ${error.message}`);
      } else {
        const requiredTables = ['conversations', 'messages', 'conversation_participants', 'message_attachments', 'message_read_receipts'];
        const tablesWithPolicies = [...new Set(policies?.map(p => p.tablename) || [])];
        const missingPolicies = requiredTables.filter(table => !tablesWithPolicies.includes(table));
        
        if (missingPolicies.length === 0) {
          results.rlsPolicies = true;
        } else {
          errors.push(`Missing RLS policies for tables: ${missingPolicies.join(', ')}`);
        }
      }
    } catch (error) {
      errors.push(`Failed to check RLS policies: ${error}`);
    }

    // Test 4: Test subscription functionality
    logger.info('Testing Realtime subscription...');
    try {
      let subscriptionWorking = false;
      let subscriptionTimeout: NodeJS.Timeout;

      const testPromise = new Promise<boolean>((resolve) => {
        const channel = supabaseAdmin
          .channel('test-channel')
          .on('postgres_changes', {
            event: '*',
            schema: 'public',
            table: 'conversations',
          }, (payload) => {
            logger.info('Test subscription received payload', { payload });
            subscriptionWorking = true;
            resolve(true);
          })
          .subscribe((status) => {
            logger.info('Test subscription status', { status });
            if (status === 'SUBSCRIBED') {
              // Subscription is active, set a timeout to resolve if no events
              subscriptionTimeout = setTimeout(() => {
                resolve(subscriptionWorking);
              }, 2000);
            }
          });

        // Cleanup timeout after 5 seconds
        setTimeout(() => {
          if (subscriptionTimeout) clearTimeout(subscriptionTimeout);
          channel.unsubscribe();
          resolve(subscriptionWorking);
        }, 5000);
      });

      results.subscriptionTest = await testPromise;
      if (!results.subscriptionTest) {
        // This is not necessarily an error - just means no events were received during test
        logger.info('Subscription test completed without receiving events (this is normal)');
        results.subscriptionTest = true; // Consider it successful if subscription was established
      }
    } catch (error) {
      errors.push(`Failed to test subscription: ${error}`);
    }

    logger.info('Realtime infrastructure test completed', {
      results,
      errorCount: errors.length,
    });

    return {
      success: errors.length === 0,
      results,
      errors,
    };
  } catch (error) {
    logger.error('Realtime infrastructure test failed', { error });
    errors.push(`Test execution failed: ${error}`);
    
    return {
      success: false,
      results,
      errors,
    };
  }
}

// Helper function to get RLS policy information
export async function getRLSPolicyInfo(): Promise<any[]> {
  try {
    const { data, error } = await supabaseAdmin
      .from('pg_policies')
      .select('*')
      .in('tablename', ['conversations', 'messages', 'conversation_participants', 'message_attachments', 'message_read_receipts']);

    if (error) {
      logger.error('Failed to get RLS policy info', { error });
      return [];
    }

    return data || [];
  } catch (error) {
    logger.error('Error getting RLS policy info', { error });
    return [];
  }
}

// Helper function to get Realtime publication info
export async function getRealtimePublicationInfo(): Promise<any[]> {
  try {
    const { data, error } = await supabaseAdmin
      .from('pg_publication_tables')
      .select('*')
      .eq('pubname', 'supabase_realtime')
      .eq('schemaname', 'public');

    if (error) {
      logger.error('Failed to get publication info', { error });
      return [];
    }

    return data || [];
  } catch (error) {
    logger.error('Error getting publication info', { error });
    return [];
  }
}
