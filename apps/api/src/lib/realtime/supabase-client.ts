import { createClient } from '@supabase/supabase-js';
import { config } from '../config';
import { logger } from '../logging/logger';

// Supabase client for Realtime functionality
const supabaseUrl = config.SUPABASE_URL;
const supabaseServiceKey = config.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase configuration for Realtime');
}

// Create Supabase client with service role for server-side operations
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});

// Create Supabase client for user authentication
export const createUserSupabaseClient = (accessToken: string) => {
  return createClient(supabaseUrl, config.SUPABASE_ANON_KEY, {
    global: {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    },
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
  });
};

// Realtime subscription types
export interface RealtimeMessage {
  id: string;
  content: string;
  messageType: string;
  conversationId: string;
  senderId: string;
  factoryId: string;
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface RealtimeConversation {
  id: string;
  type: string;
  subject?: string;
  factoryId: string;
  isActive: boolean;
  lastMessageAt?: string;
  messageCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface RealtimeConversationParticipant {
  id: string;
  userId: string;
  conversationId: string;
  role: string;
  canWrite: boolean;
  canRead: boolean;
  lastReadAt?: string;
  unreadCount: number;
  joinedAt: string;
}

// Realtime event types
export type RealtimeEventType = 'INSERT' | 'UPDATE' | 'DELETE';

export interface RealtimePayload<T = any> {
  eventType: RealtimeEventType;
  new: T;
  old: T;
  schema: string;
  table: string;
  commit_timestamp: string;
}

// Realtime subscription manager
export class RealtimeSubscriptionManager {
  private subscriptions: Map<string, any> = new Map();

  // Subscribe to conversation messages
  subscribeToConversationMessages(
    conversationId: string,
    accessToken: string,
    callback: (payload: RealtimePayload<RealtimeMessage>) => void
  ) {
    const client = createUserSupabaseClient(accessToken);
    const subscriptionKey = `messages_${conversationId}`;

    const subscription = client
      .channel(`messages:conversation_id=eq.${conversationId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${conversationId}`,
        },
        (payload) => {
          logger.info('Realtime message received', {
            conversationId,
            eventType: payload.eventType,
            messageId: payload.new?.id,
          });
          callback(payload as RealtimePayload<RealtimeMessage>);
        }
      )
      .subscribe((status) => {
        logger.info('Message subscription status', {
          conversationId,
          status,
        });
      });

    this.subscriptions.set(subscriptionKey, subscription);
    return subscriptionKey;
  }

  // Subscribe to user conversations
  subscribeToUserConversations(
    userId: string,
    accessToken: string,
    callback: (payload: RealtimePayload<RealtimeConversation>) => void
  ) {
    const client = createUserSupabaseClient(accessToken);
    const subscriptionKey = `conversations_${userId}`;

    const subscription = client
      .channel(`conversations:user_id=eq.${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'conversations',
        },
        (payload) => {
          logger.info('Realtime conversation received', {
            userId,
            eventType: payload.eventType,
            conversationId: payload.new?.id,
          });
          callback(payload as RealtimePayload<RealtimeConversation>);
        }
      )
      .subscribe((status) => {
        logger.info('Conversation subscription status', {
          userId,
          status,
        });
      });

    this.subscriptions.set(subscriptionKey, subscription);
    return subscriptionKey;
  }

  // Subscribe to conversation participants
  subscribeToConversationParticipants(
    conversationId: string,
    accessToken: string,
    callback: (payload: RealtimePayload<RealtimeConversationParticipant>) => void
  ) {
    const client = createUserSupabaseClient(accessToken);
    const subscriptionKey = `participants_${conversationId}`;

    const subscription = client
      .channel(`participants:conversation_id=eq.${conversationId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'conversation_participants',
          filter: `conversation_id=eq.${conversationId}`,
        },
        (payload) => {
          logger.info('Realtime participant received', {
            conversationId,
            eventType: payload.eventType,
            participantId: payload.new?.id,
          });
          callback(payload as RealtimePayload<RealtimeConversationParticipant>);
        }
      )
      .subscribe((status) => {
        logger.info('Participant subscription status', {
          conversationId,
          status,
        });
      });

    this.subscriptions.set(subscriptionKey, subscription);
    return subscriptionKey;
  }

  // Unsubscribe from a specific subscription
  unsubscribe(subscriptionKey: string) {
    const subscription = this.subscriptions.get(subscriptionKey);
    if (subscription) {
      subscription.unsubscribe();
      this.subscriptions.delete(subscriptionKey);
      logger.info('Unsubscribed from realtime', { subscriptionKey });
    }
  }

  // Unsubscribe from all subscriptions
  unsubscribeAll() {
    for (const [key, subscription] of this.subscriptions) {
      subscription.unsubscribe();
      logger.info('Unsubscribed from realtime', { subscriptionKey: key });
    }
    this.subscriptions.clear();
  }

  // Get active subscription count
  getActiveSubscriptionCount(): number {
    return this.subscriptions.size;
  }
}

// Global subscription manager instance
export const realtimeManager = new RealtimeSubscriptionManager();

// Helper function to validate Realtime connection
export async function validateRealtimeConnection(): Promise<boolean> {
  try {
    const { data, error } = await supabaseAdmin
      .from('conversations')
      .select('id')
      .limit(1);

    if (error) {
      logger.error('Realtime connection validation failed', { error });
      return false;
    }

    logger.info('Realtime connection validated successfully');
    return true;
  } catch (error) {
    logger.error('Realtime connection validation error', { error });
    return false;
  }
}
