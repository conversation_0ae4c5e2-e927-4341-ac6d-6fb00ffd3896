// Compliance utilities and validation functions
import { TRPCError } from '@trpc/server';

// ISO 3166-1 alpha-2 country codes (subset for common manufacturing countries)
export const COUNTRY_CODES = {
  'CN': 'China',
  'US': 'United States',
  'DE': 'Germany',
  'JP': 'Japan',
  'KR': 'South Korea',
  'TW': 'Taiwan',
  'IN': 'India',
  'VN': 'Vietnam',
  'TH': 'Thailand',
  'MY': 'Malaysia',
  'SG': 'Singapore',
  'ID': 'Indonesia',
  'PH': 'Philippines',
  'MX': 'Mexico',
  'BR': 'Brazil',
  'IT': 'Italy',
  'FR': 'France',
  'GB': 'United Kingdom',
  'CA': 'Canada',
  'AU': 'Australia',
} as const;

// Common certification types
export const CERTIFICATION_TYPES = {
  // Safety & Quality
  'CE': 'Conformité Européenne (European Conformity)',
  'FCC': 'Federal Communications Commission',
  'UL': 'Underwriters Laboratories',
  'CSA': 'Canadian Standards Association',
  'PSE': 'Product Safety Electrical Appliance and Material Safety Law',
  'CCC': 'China Compulsory Certification',
  'KC': 'Korea Certification',
  'BSMI': 'Bureau of Standards, Metrology and Inspection',
  
  // Quality Management
  'ISO_9001': 'ISO 9001 Quality Management Systems',
  'ISO_14001': 'ISO 14001 Environmental Management Systems',
  'ISO_45001': 'ISO 45001 Occupational Health and Safety',
  'ISO_27001': 'ISO 27001 Information Security Management',
  
  // Industry Specific
  'FDA': 'Food and Drug Administration',
  'CPSIA': 'Consumer Product Safety Improvement Act',
  'RoHS': 'Restriction of Hazardous Substances',
  'REACH': 'Registration, Evaluation, Authorisation and Restriction of Chemicals',
  'WEEE': 'Waste Electrical and Electronic Equipment',
  'Energy_Star': 'Energy Star Certification',
  'EPEAT': 'Electronic Product Environmental Assessment Tool',
} as const;

// Common compliance standards
export const COMPLIANCE_STANDARDS = {
  // Environmental
  'RoHS': 'Restriction of Hazardous Substances Directive',
  'REACH': 'Registration, Evaluation, Authorisation and Restriction of Chemicals',
  'WEEE': 'Waste Electrical and Electronic Equipment Directive',
  'CPSIA': 'Consumer Product Safety Improvement Act',
  
  // Electromagnetic Compatibility
  'EMC': 'Electromagnetic Compatibility',
  'EMI': 'Electromagnetic Interference',
  'EMS': 'Electromagnetic Susceptibility',
  
  // Safety Standards
  'IEC_62368': 'IEC 62368-1 Audio/video, information and communication technology equipment',
  'IEC_60950': 'IEC 60950-1 Information technology equipment - Safety',
  'UL_2089': 'UL 2089 Standard for Health/Wellness Devices',
  
  // Wireless Standards
  'FCC_Part_15': 'FCC Part 15 Radio Frequency Devices',
  'IC_RSS': 'Industry Canada Radio Standards Specification',
  'ETSI_EN': 'European Telecommunications Standards Institute',
} as const;

// Export restriction types
export const EXPORT_RESTRICTION_TYPES = {
  'PROHIBITED': 'Completely prohibited for export',
  'RESTRICTED': 'Restricted export with conditions',
  'LICENSE_REQUIRED': 'Export license required',
  'QUOTA': 'Subject to export quota limitations',
} as const;

// Regulatory agencies
export const REGULATORY_AGENCIES = {
  'EPA': 'Environmental Protection Agency',
  'OSHA': 'Occupational Safety and Health Administration',
  'CPSC': 'Consumer Product Safety Commission',
  'DOT': 'Department of Transportation',
  'FDA': 'Food and Drug Administration',
  'FTC': 'Federal Trade Commission',
  'NHTSA': 'National Highway Traffic Safety Administration',
} as const;

/**
 * Validate HS Code format and structure
 */
export function validateHSCode(hsCode: string): {
  valid: boolean;
  message: string;
  details?: {
    chapter?: string;
    heading?: string;
    subheading?: string;
    tariffItem?: string;
  };
} {
  // Remove any spaces or dashes
  const cleanCode = hsCode.replace(/[\s-]/g, '');
  
  // Check basic format (6-10 digits)
  if (!/^\d{6,10}$/.test(cleanCode)) {
    return {
      valid: false,
      message: 'HS Code must be 6-10 digits',
    };
  }

  const codeLength = cleanCode.length;
  
  // Extract components based on length
  const details: any = {};
  
  if (codeLength >= 2) {
    details.chapter = cleanCode.substring(0, 2);
  }
  
  if (codeLength >= 4) {
    details.heading = cleanCode.substring(0, 4);
  }
  
  if (codeLength >= 6) {
    details.subheading = cleanCode.substring(0, 6);
  }
  
  if (codeLength >= 8) {
    details.tariffItem = cleanCode.substring(0, 8);
  }

  return {
    valid: true,
    message: `Valid HS Code with ${codeLength} digits`,
    details,
  };
}

/**
 * Validate country code (ISO 3166-1 alpha-2)
 */
export function validateCountryCode(countryCode: string): {
  valid: boolean;
  message: string;
  countryName?: string;
} {
  const upperCode = countryCode.toUpperCase();
  
  if (upperCode.length !== 2) {
    return {
      valid: false,
      message: 'Country code must be exactly 2 characters',
    };
  }

  if (upperCode in COUNTRY_CODES) {
    return {
      valid: true,
      message: 'Valid country code',
      countryName: COUNTRY_CODES[upperCode as keyof typeof COUNTRY_CODES],
    };
  }

  return {
    valid: false,
    message: 'Invalid or unsupported country code',
  };
}

/**
 * Check if certification is expiring soon
 */
export function isCertificationExpiringSoon(
  expiryDate: string | Date,
  daysThreshold: number = 30
): boolean {
  const expiry = new Date(expiryDate);
  const threshold = new Date();
  threshold.setDate(threshold.getDate() + daysThreshold);
  
  return expiry <= threshold;
}

/**
 * Get certification status based on expiry date
 */
export function getCertificationStatus(
  expiryDate?: string | Date
): 'ACTIVE' | 'EXPIRED' | 'EXPIRING_SOON' {
  if (!expiryDate) {
    return 'ACTIVE';
  }

  const expiry = new Date(expiryDate);
  const now = new Date();
  
  if (expiry < now) {
    return 'EXPIRED';
  }
  
  if (isCertificationExpiringSoon(expiry)) {
    return 'EXPIRING_SOON';
  }
  
  return 'ACTIVE';
}

/**
 * Validate export restrictions for a country
 */
export function validateExportRestriction(
  targetCountry: string,
  restrictions: Array<{
    country: string;
    restrictionType: string;
    licenseRequired: boolean;
  }>
): {
  allowed: boolean;
  restrictions: Array<{
    restrictionType: string;
    licenseRequired: boolean;
    notes?: string;
  }>;
} {
  const countryRestrictions = restrictions.filter(
    r => r.country.toUpperCase() === targetCountry.toUpperCase()
  );

  if (countryRestrictions.length === 0) {
    return {
      allowed: true,
      restrictions: [],
    };
  }

  const hasProhibition = countryRestrictions.some(
    r => r.restrictionType === 'PROHIBITED'
  );

  return {
    allowed: !hasProhibition,
    restrictions: countryRestrictions.map(r => ({
      restrictionType: r.restrictionType,
      licenseRequired: r.licenseRequired,
    })),
  };
}

/**
 * Generate compliance checklist for a product
 */
export function generateComplianceChecklist(
  targetMarkets: string[],
  productCategory?: string
): Array<{
  category: string;
  items: Array<{
    requirement: string;
    mandatory: boolean;
    description: string;
  }>;
}> {
  const checklist: Array<{
    category: string;
    items: Array<{
      requirement: string;
      mandatory: boolean;
      description: string;
    }>;
  }> = [];

  // Basic requirements for all products
  checklist.push({
    category: 'Basic Compliance',
    items: [
      {
        requirement: 'HS Code Classification',
        mandatory: true,
        description: 'Proper Harmonized System code for customs classification',
      },
      {
        requirement: 'Country of Origin',
        mandatory: true,
        description: 'Accurate country of origin declaration',
      },
      {
        requirement: 'Product Documentation',
        mandatory: true,
        description: 'Complete product specifications and documentation',
      },
    ],
  });

  // Market-specific requirements
  if (targetMarkets.includes('US')) {
    checklist.push({
      category: 'US Market Requirements',
      items: [
        {
          requirement: 'FCC Compliance',
          mandatory: true,
          description: 'FCC certification for electronic products',
        },
        {
          requirement: 'CPSIA Compliance',
          mandatory: true,
          description: 'Consumer Product Safety Improvement Act compliance',
        },
      ],
    });
  }

  if (targetMarkets.some(market => ['DE', 'FR', 'IT', 'GB'].includes(market))) {
    checklist.push({
      category: 'European Market Requirements',
      items: [
        {
          requirement: 'CE Marking',
          mandatory: true,
          description: 'CE conformity marking for European market',
        },
        {
          requirement: 'RoHS Compliance',
          mandatory: true,
          description: 'Restriction of Hazardous Substances compliance',
        },
        {
          requirement: 'REACH Registration',
          mandatory: false,
          description: 'Chemical substance registration if applicable',
        },
      ],
    });
  }

  return checklist;
}
