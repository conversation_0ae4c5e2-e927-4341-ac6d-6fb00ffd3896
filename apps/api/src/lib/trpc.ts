import { initTR<PERSON>, TRPCError } from '@trpc/server';
import { CreateExpressContextOptions } from '@trpc/server/adapters/express';
import { ZodError } from 'zod';
import { prisma } from './database/connection';
import { verifyJWT } from './auth/jwt';
import { AuthContext, Permission } from '@fc-china/shared-types';

// Create context for tRPC
export async function createContext({ req, res }: CreateExpressContextOptions) {
  // Extract request metadata
  const ip = req.ip || req.connection.remoteAddress || 'unknown';
  const userAgent = req.headers['user-agent'] || 'unknown';
  const requestId = req.headers['x-request-id'] || generateRequestId();
  
  // Base context available to all procedures
  const baseContext = {
    req,
    res,
    db: prisma,
    ip,
    userAgent,
    requestId,
    startTime: Date.now(),
  };
  
  // Try to authenticate user from cookies (Auth0 session)
  const cookieHeader = req.headers.cookie;

  if (cookieHeader) {
    try {
      // Parse cookies to find auth0_session
      const cookies = cookieHeader.split(';').reduce((acc: Record<string, string>, cookie) => {
        const [key, value] = cookie.trim().split('=');
        if (key && value) {
          acc[key] = decodeURIComponent(value);
        }
        return acc;
      }, {});

      const auth0Session = cookies['auth0_session'];
      if (auth0Session) {
        let session;
        try {
          session = JSON.parse(auth0Session);
        } catch (parseError) {
          throw new Error('Invalid session cookie');
        }

        if (session.access_token) {
          // Decode the ID token to get user info (simple approach for development)
          if (session.id_token) {
            const payload = session.id_token.split('.')[1];
            const decoded = JSON.parse(atob(payload));

            // Create a mock user for now - in production you'd validate with Auth0 and lookup in DB
            const mockUser = {
              id: 'user_' + decoded.sub.replace('auth0|', ''),
              auth0Id: decoded.sub,
              email: decoded.email,
              firstName: decoded.name?.split(' ')[0] || 'Factory',
              lastName: decoded.name?.split(' ')[1] || 'User',
              role: 'FACTORY_ADMIN' as const,
              status: 'ACTIVE' as const,
              factoryId: 'cmdgtue8w0000neikaoycszlo', // Use the known factory ID for development
              permissions: [
                'FACTORY_READ',
                'FACTORY_WRITE',
                'FACTORY_ADMIN',
                'PRODUCT_READ',
                'PRODUCT_WRITE',
                'PRODUCT_DELETE',
                'PRODUCT_PUBLISH',
                'ORDER_READ',
                'ORDER_WRITE',
                'ORDER_PROCESS',
                'USER_READ',
                'USER_WRITE',
                'USER_INVITE',
                'ANALYTICS_READ',
                'MESSAGE_READ',
                'MESSAGE_WRITE',
              ] as Permission[],
              avatar: decoded.picture || undefined,
            };

            const authContext: AuthContext = {
              user: mockUser,
              token: session.access_token,
              isAuthenticated: true,
            };

            return {
              ...baseContext,
              user: authContext.user,
              auth: authContext,
              isAuthenticated: true,
            };
          }
        }
      }
    } catch (error) {
      // Log authentication failure but don't throw - allow unauthenticated access to public procedures
      console.warn('Cookie-based authentication failed:', error);
    }
  }

  // Try to authenticate user from Authorization header (fallback)
  const authHeader = req.headers.authorization;

  // Fallback to Bearer token authentication
  if (authHeader?.startsWith('Bearer ')) {
    try {
      const token = authHeader.substring(7);
      const payload = await verifyJWT(token);

      // Fetch user details from database
      const user = await prisma.user.findUnique({
        where: { id: payload.userId },
        include: {
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
              status: true,
            },
          },
        },
      });

      if (user && user.status === 'ACTIVE') {
        const authContext: AuthContext = {
          user: {
            id: user.id,
            auth0Id: user.auth0Id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            status: user.status,
            factoryId: user.factoryId || undefined,
            permissions: user.permissions as Permission[],
            avatar: user.avatar || undefined,
          },
          token,
          isAuthenticated: true,
        };

        return {
          ...baseContext,
          user: authContext.user,
          auth: authContext,
          isAuthenticated: true,
        };
      }
    } catch (error) {
      // Log authentication failure but don't throw - allow unauthenticated access to public procedures
      console.warn('Bearer token authentication failed:', error);
    }
  }
  
  return {
    ...baseContext,
    user: null,
    auth: null,
    isAuthenticated: false,
  };
}

export type Context = Awaited<ReturnType<typeof createContext>>;

// Initialize tRPC
const t = initTRPC.context<Context>().create({
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof ZodError
            ? error.cause.flatten()
            : null,
      },
    };
  },
  defaultMeta: {
    authRequired: false,
  },
});

// Base router and procedure
export const router = t.router;
export const publicProcedure = t.procedure;

// Authentication middleware
const isAuthenticated = t.middleware(({ ctx, next }) => {
  if (!ctx.isAuthenticated || !ctx.user) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Authentication required',
    });
  }
  
  return next({
    ctx: {
      ...ctx,
      user: ctx.user, // Now guaranteed to be non-null
    },
  });
});

// Protected procedure (requires authentication)
export const protectedProcedure = publicProcedure.use(isAuthenticated);

// Permission-based middleware
export const requirePermission = (permission: Permission) =>
  t.middleware(({ ctx, next }) => {
    if (!ctx.isAuthenticated || !ctx.user) {
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: 'Authentication required',
      });
    }
    
    const hasPermission = ctx.user.permissions.includes(permission) || 
                         ctx.user.permissions.includes('SYSTEM_ADMIN');
    
    if (!hasPermission) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: `Permission required: ${permission}`,
      });
    }
    
    return next({ ctx });
  });

// Factory access middleware
export const requireFactoryAccess = t.middleware(({ ctx, next }) => {
  if (!ctx.isAuthenticated || !ctx.user) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Authentication required',
    });
  }
  
  // Super admin can access any factory
  if (ctx.user.permissions.includes('SYSTEM_ADMIN')) {
    return next({ ctx });
  }
  
  // Factory users must have a factory ID
  if (!ctx.user.factoryId) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Factory access required',
    });
  }
  
  return next({ ctx });
});

// Role-based middleware
export const requireRole = (allowedRoles: string[]) =>
  t.middleware(({ ctx, next }) => {
    if (!ctx.isAuthenticated || !ctx.user) {
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: 'Authentication required',
      });
    }
    
    if (!allowedRoles.includes(ctx.user.role)) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: `Role required: ${allowedRoles.join(' or ')}`,
      });
    }
    
    return next({ ctx });
  });

// Audit logging middleware
export const withAudit = (eventType: string) =>
  t.middleware(async ({ ctx, next, path, type, input }) => {
    const result = await next();
    
    // Log the operation for audit purposes
    if (ctx.user) {
      try {
        await ctx.db.auditLog.create({
          data: {
            type: 'BUSINESS',
            event: eventType,
            entityType: path.split('.')[0],
            userId: ctx.user.id,
            factoryId: ctx.user.factoryId,
            ipAddress: ctx.ip,
            userAgent: ctx.userAgent,
            metadata: {
              path,
              type,
              input: sanitizeInput(input),
              timestamp: new Date().toISOString(),
            },
          },
        });
      } catch (error) {
        console.error('Failed to create audit log:', error);
      }
    }
    
    return result;
  });

// Rate limiting middleware (basic implementation)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export const rateLimit = (maxRequests: number, windowMs: number) =>
  t.middleware(({ ctx, next }) => {
    const key = ctx.user?.id || ctx.ip;
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Clean up old entries
    for (const [k, v] of rateLimitMap.entries()) {
      if (v.resetTime < windowStart) {
        rateLimitMap.delete(k);
      }
    }
    
    const current = rateLimitMap.get(key);
    if (!current) {
      rateLimitMap.set(key, { count: 1, resetTime: now });
    } else if (current.resetTime < windowStart) {
      rateLimitMap.set(key, { count: 1, resetTime: now });
    } else if (current.count >= maxRequests) {
      throw new TRPCError({
        code: 'TOO_MANY_REQUESTS',
        message: 'Rate limit exceeded',
      });
    } else {
      current.count++;
    }
    
    return next();
  });

// Utility functions
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(7)}`;
}

function sanitizeInput(input: any): any {
  if (!input) return input;
  
  const sanitized = { ...input };
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
  
  for (const field of sensitiveFields) {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]';
    }
  }
  
  return sanitized;
}

// Export middleware combinations for common use cases
export const adminProcedure = protectedProcedure.use(requirePermission('SYSTEM_ADMIN'));
export const factoryProcedure = protectedProcedure.use(requireFactoryAccess);
export const factoryAdminProcedure = factoryProcedure.use(requirePermission('FACTORY_ADMIN'));

// Export the tRPC instance
export { t };
