import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { createExpressMiddleware } from '@trpc/server/adapters/express';
import { config, corsConfig, isDevelopment } from './lib/config';
import { createContext } from './lib/trpc';
import { appRouter } from './routers';
import { initializeDatabase, disconnectDatabase } from './lib/database/connection';
import { logger } from './lib/logging/logger';

// Create Express app
const app = express();

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: !isDevelopment,
  crossOriginEmbedderPolicy: !isDevelopment,
}));

// Performance middleware
app.use(compression({
  level: 6,
  threshold: 1024,
  filter: (req, res) => {
    if (req.headers['x-no-compression']) return false;
    return compression.filter(req, res);
  },
}));

// CORS configuration
app.use(cors(corsConfig));

// Body parsing
app.use(express.json({ 
  limit: '10mb',
  verify: (req, res, buf) => {
    // Store raw body for webhook verification
    (req as any).rawBody = buf;
  }
}));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging middleware
app.use((req, res, next) => {
  const startTime = Date.now();
  const requestId = req.headers['x-request-id'] || `req_${Date.now()}_${Math.random().toString(36).substring(7)}`;
  
  // Add request ID to request object
  (req as any).requestId = requestId;
  
  // Log incoming request
  logger.info('Incoming request', {
    requestId,
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.headers['user-agent'],
    contentLength: req.headers['content-length'],
  });
  
  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any) {
    const duration = Date.now() - startTime;
    
    // Log response
    logger.info('Request completed', {
      requestId,
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      contentLength: res.get('content-length'),
    });
    
    // Log slow requests
    if (duration > 1000) {
      logger.warn('Slow request detected', {
        requestId,
        method: req.method,
        url: req.url,
        duration,
        statusCode: res.statusCode,
      });
    }
    
    originalEnd.call(this, chunk, encoding);
  };
  
  next();
});

// Health check endpoint (no auth required)
app.get('/health', async (req, res) => {
  try {
    // Basic health check
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: config.NODE_ENV,
      uptime: process.uptime(),
    };
    
    res.status(200).json(health);
  } catch (error) {
    logger.error('Health check failed', { error: error instanceof Error ? error.message : error });
    
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
    });
  }
});

// Detailed health check endpoint (protected)
app.get('/health/detailed', async (req, res) => {
  // Verify health check token
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (token !== config.HEALTH_CHECK_TOKEN) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  
  try {
    // Import health check function
    const { checkDatabaseHealth } = await import('./lib/database/connection');
    
    const dbHealth = await checkDatabaseHealth();
    
    const health = {
      status: dbHealth.healthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: config.NODE_ENV,
      uptime: process.uptime(),
      checks: {
        database: {
          status: dbHealth.healthy ? 'healthy' : 'unhealthy',
          latency: dbHealth.latency,
          activeConnections: dbHealth.activeConnections,
        },
        memory: {
          usage: process.memoryUsage(),
        },
      },
    };
    
    res.status(dbHealth.healthy ? 200 : 503).json(health);
  } catch (error) {
    logger.error('Detailed health check failed', { error: error instanceof Error ? error.message : error });
    
    res.status(503).json({
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Health check failed',
    });
  }
});

// Main tRPC API
app.use('/api/trpc', createExpressMiddleware({
  router: appRouter,
  createContext,
  onError: ({ error, type, path, input, ctx, req }) => {
    // Log errors with context
    logger.error('tRPC Error', {
      error: error.message,
      code: error.code,
      type,
      path,
      input: sanitizeInput(input),
      userId: ctx?.user?.id,
      factoryId: ctx?.user?.factoryId,
      ip: req?.ip,
      userAgent: req?.headers['user-agent'],
      requestId: (req as any)?.requestId,
    });
    
    // Don't expose internal errors in production
    if (!isDevelopment && error.code === 'INTERNAL_SERVER_ERROR') {
      error.message = 'An internal error occurred';
    }
  },
}));

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`,
    timestamp: new Date().toISOString(),
  });
});

// Global error handler
app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  const requestId = (req as any).requestId;
  
  logger.error('Unhandled error', {
    requestId,
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.headers['user-agent'],
  });
  
  if (res.headersSent) {
    return next(error);
  }
  
  const statusCode = (error as any).statusCode || 500;
  const message = isDevelopment ? error.message : 'Internal Server Error';
  
  res.status(statusCode).json({
    error: {
      message,
      requestId,
      timestamp: new Date().toISOString(),
    },
  });
});

// Initialize server
async function startServer() {
  try {
    // Initialize database connection
    await initializeDatabase();
    
    // Start server
    const server = app.listen(config.PORT, () => {
      logger.info(`🚀 FC-CHINA API Server running on port ${config.PORT}`);
      logger.info(`📊 Health check: http://localhost:${config.PORT}/health`);
      logger.info(`🔧 Environment: ${config.NODE_ENV}`);
      logger.info(`🌐 CORS origins: ${corsConfig.origin.join(', ')}`);
    });
    
    // Graceful shutdown handling
    const gracefulShutdown = async (signal: string) => {
      logger.info(`${signal} received, shutting down gracefully`);
      
      server.close(async () => {
        logger.info('HTTP server closed');
        
        try {
          await disconnectDatabase();
          logger.info('Database disconnected');
        } catch (error) {
          logger.error('Error disconnecting database:', error);
        }
        
        logger.info('Process terminated');
        process.exit(0);
      });
      
      // Force close after 10 seconds
      setTimeout(() => {
        logger.error('Could not close connections in time, forcefully shutting down');
        process.exit(1);
      }, 10000);
    };
    
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      process.exit(1);
    });
    
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      process.exit(1);
    });
    
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Utility function to sanitize input for logging
function sanitizeInput(input: any): any {
  if (!input) return input;
  
  const sanitized = { ...input };
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
  
  for (const field of sensitiveFields) {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]';
    }
  }
  
  return sanitized;
}

// Start the server
startServer();
