import { router } from '../lib/trpc';
import { authRouter } from './auth';
import { usersRouter } from './users';
import { factoriesRouter } from './factories';
import { productsRouter } from './products';
import { ordersRouter } from './orders';
import { messagesRouter } from './messages';
import { imageManagementRouter } from './image-management';
import { uploadsRouter } from './uploads';

// Main application router
export const appRouter = router({
  auth: authRouter,
  users: usersRouter,
  factories: factoriesRouter,
  products: productsRouter,
  orders: ordersRouter,
  messages: messagesRouter,
  imageManagement: imageManagementRouter,
  uploads: uploadsRouter,
});

// Export type definition for client
export type AppRouter = typeof appRouter;
