/**
 * Image Management tRPC Router
 *
 * Comprehensive API for managing product images with enterprise-level features:
 * - Add/remove images with audit trails
 * - Reorder images with drag-and-drop support
 * - Set main image with validation
 * - Update image metadata and alt text
 * - Bulk operations with transaction support
 * - Complete audit history tracking
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, protectedProcedure } from '../lib/trpc';
import { prisma } from '../lib/database/connection';

// Input validation schemas
const ImageInputSchema = z.object({
  url: z.string().url('Invalid image URL'),
  originalName: z.string().optional(),
  fileSize: z.number().int().positive().optional(),
  mimeType: z.string().regex(/^image\//, 'Must be an image MIME type').optional(),
  altText: z.string().max(500, 'Alt text too long').optional(),
  caption: z.string().max(1000, 'Caption too long').optional(),
});

const ProductAccessSchema = z.object({
  productId: z.string().cuid('Invalid product ID'),
  factoryId: z.string().cuid('Invalid factory ID'),
});

const ImageReorderSchema = z.object({
  imageId: z.string().cuid('Invalid image ID'),
  sortOrder: z.number().int().min(0, 'Sort order must be non-negative'),
});

// Helper function to validate factory access
async function validateFactoryAccess(productId: string, factoryId: string, userId: string) {
  const product = await prisma.product.findFirst({
    where: {
      id: productId,
      factoryId: factoryId
    },
    select: {
      id: true,
      name: true,
      factoryId: true
    }
  });

  if (!product) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: 'Product not found or access denied'
    });
  }

  return product;
}

export const imageManagementRouter = router({
  /**
   * Add new images to an existing product
   * Supports single or multiple image uploads with metadata
   */
  addImages: protectedProcedure
    .input(z.object({
      ...ProductAccessSchema.shape,
      images: z.array(ImageInputSchema).min(1, 'At least one image required').max(10, 'Maximum 10 images per request'),
    }))
    .mutation(async ({ input, ctx }) => {
      const { productId, factoryId, images } = input;
      const userId = ctx.user.id;

      // Validate factory access
      const product = await validateFactoryAccess(productId, factoryId, userId);

      // Get current max sort order
      const maxSortOrder = await prisma.productImage.findFirst({
        where: { productId },
        orderBy: { sortOrder: 'desc' },
        select: { sortOrder: true }
      });

      let nextSortOrder = (maxSortOrder?.sortOrder || 0) + 1;

      // Create new image records in transaction
      const newImages = await prisma.$transaction(async (tx) => {
        const createdImages = await Promise.all(
          images.map(async (image, index) => {
            const imageRecord = await tx.productImage.create({
              data: {
                productId,
                url: image.url,
                originalName: image.originalName,
                fileSize: image.fileSize,
                mimeType: image.mimeType,
                altText: image.altText,
                caption: image.caption,
                isMain: false, // New images are never main by default
                sortOrder: nextSortOrder + index,
                status: 'ACTIVE',
                uploadedAt: new Date(),
              }
            });

            return imageRecord;
          })
        );

        // Log the operation
        await tx.imageOperation.create({
          data: {
            operationType: images.length > 1 ? 'BULK_UPLOAD' : 'ADD',
            operationData: {
              imageCount: images.length,
              imageIds: createdImages.map(img => img.id),
              urls: images.map(img => img.url),
            },
            productId,
            userId,
          }
        });

        return createdImages;
      });

      console.log(`✅ Added ${newImages.length} image(s) to product ${product.name}`);

      return {
        success: true,
        images: newImages,
        message: `Successfully added ${newImages.length} image(s)`,
      };
    }),

  /**
   * Remove an image from a product
   * Includes validation to prevent removing the last main image
   */
  removeImage: protectedProcedure
    .input(z.object({
      ...ProductAccessSchema.shape,
      imageId: z.string().cuid('Invalid image ID'),
    }))
    .mutation(async ({ input, ctx }) => {
      const { productId, factoryId, imageId } = input;
      const userId = ctx.user.id;

      // Validate factory access
      await validateFactoryAccess(productId, factoryId, userId);

      // Get image details and validate ownership
      const image = await prisma.productImage.findFirst({
        where: {
          id: imageId,
          productId: productId
        }
      });

      if (!image) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Image not found'
        });
      }

      // Check if this is the only main image
      const imageCount = await prisma.productImage.count({
        where: { productId, status: 'ACTIVE' }
      });

      if (imageCount === 1 && image.isMain) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Cannot remove the only main image. Add another image first.'
        });
      }

      // Remove image and handle main image reassignment
      await prisma.$transaction(async (tx) => {
        // If removing main image, set another image as main
        if (image.isMain && imageCount > 1) {
          const nextMainImage = await tx.productImage.findFirst({
            where: {
              productId,
              id: { not: imageId },
              status: 'ACTIVE'
            },
            orderBy: { sortOrder: 'asc' }
          });

          if (nextMainImage) {
            await tx.productImage.update({
              where: { id: nextMainImage.id },
              data: { isMain: true }
            });
          }
        }

        // Soft delete the image
        await tx.productImage.update({
          where: { id: imageId },
          data: { status: 'DELETED' }
        });

        // Log the operation
        await tx.imageOperation.create({
          data: {
            operationType: 'REMOVE',
            operationData: {
              imageId,
              url: image.url,
              wasMain: image.isMain,
            },
            productId,
            userId,
          }
        });
      });

      console.log(`✅ Removed image ${imageId} from product ${productId}`);

      return {
        success: true,
        message: 'Image removed successfully',
      };
    }),

  /**
   * Reorder images with drag-and-drop support
   * Updates sort order for multiple images atomically
   */
  reorderImages: protectedProcedure
    .input(z.object({
      ...ProductAccessSchema.shape,
      imageOrder: z.array(ImageReorderSchema).min(1, 'At least one image required'),
    }))
    .mutation(async ({ input, ctx }) => {
      const { productId, factoryId, imageOrder } = input;
      const userId = ctx.user.id;

      // Validate factory access
      await validateFactoryAccess(productId, factoryId, userId);

      // Validate all images belong to the product
      const imageIds = imageOrder.map(item => item.imageId);
      const existingImages = await prisma.productImage.findMany({
        where: {
          id: { in: imageIds },
          productId: productId,
          status: 'ACTIVE'
        },
        select: { id: true }
      });

      if (existingImages.length !== imageIds.length) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'One or more images not found or not active'
        });
      }

      // Update sort orders in transaction
      await prisma.$transaction(async (tx) => {
        await Promise.all(
          imageOrder.map(({ imageId, sortOrder }) =>
            tx.productImage.update({
              where: { id: imageId },
              data: { sortOrder }
            })
          )
        );

        // Log the operation
        await tx.imageOperation.create({
          data: {
            operationType: 'REORDER',
            operationData: {
              newOrder: imageOrder,
              imageCount: imageOrder.length,
            },
            productId,
            userId,
          }
        });
      });

      console.log(`✅ Reordered ${imageOrder.length} images for product ${productId}`);

      return {
        success: true,
        message: 'Images reordered successfully',
      };
    }),

  /**
   * Reorder images by image IDs (simplified version)
   * Updates sort order based on array position
   */
  reorderImagesByIds: protectedProcedure
    .input(z.object({
      ...ProductAccessSchema.shape,
      imageIds: z.array(z.string().cuid()).min(1, 'At least one image ID required'),
    }))
    .mutation(async ({ input, ctx }) => {
      const { productId, factoryId, imageIds } = input;
      const userId = ctx.user.id;

      // Validate factory access
      await validateFactoryAccess(productId, factoryId, userId);

      // Validate all images belong to the product
      const existingImages = await prisma.productImage.findMany({
        where: {
          id: { in: imageIds },
          productId: productId,
          status: 'ACTIVE'
        },
        select: { id: true }
      });

      if (existingImages.length !== imageIds.length) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'One or more images not found or not active'
        });
      }

      // Update sort orders in transaction
      const updatedImages = await prisma.$transaction(async (tx) => {
        const updates = await Promise.all(
          imageIds.map((imageId, index) =>
            tx.productImage.update({
              where: { id: imageId },
              data: { sortOrder: index }
            })
          )
        );

        // Log the operation
        await tx.imageOperation.create({
          data: {
            operationType: 'REORDER',
            operationData: {
              imageIds,
              newOrder: imageIds.map((id, index) => ({ id, sortOrder: index })),
            },
            productId,
            userId,
          }
        });

        return updates;
      });

      console.log(`✅ Reordered ${updatedImages.length} images for product ${productId}`);

      return {
        success: true,
        images: updatedImages,
        message: 'Images reordered successfully',
      };
    }),

  /**
   * Set main image for a product
   * Ensures only one main image per product
   */
  setMainImage: protectedProcedure
    .input(z.object({
      ...ProductAccessSchema.shape,
      imageId: z.string().cuid('Invalid image ID'),
    }))
    .mutation(async ({ input, ctx }) => {
      const { productId, factoryId, imageId } = input;
      const userId = ctx.user.id;

      // Validate factory access
      await validateFactoryAccess(productId, factoryId, userId);

      // Validate image exists and belongs to product
      const image = await prisma.productImage.findFirst({
        where: {
          id: imageId,
          productId: productId,
          status: 'ACTIVE'
        }
      });

      if (!image) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Image not found or not active'
        });
      }

      if (image.isMain) {
        return {
          success: true,
          message: 'Image is already the main image',
        };
      }

      // Update main image in transaction
      await prisma.$transaction(async (tx) => {
        // Unset current main image
        await tx.productImage.updateMany({
          where: { productId, isMain: true },
          data: { isMain: false }
        });

        // Set new main image
        await tx.productImage.update({
          where: { id: imageId },
          data: { isMain: true }
        });

        // Log the operation
        await tx.imageOperation.create({
          data: {
            operationType: 'SET_MAIN',
            operationData: {
              imageId,
              url: image.url,
            },
            productId,
            userId,
          }
        });
      });

      console.log(`✅ Set main image ${imageId} for product ${productId}`);

      return {
        success: true,
        message: 'Main image updated successfully',
      };
    }),

  /**
   * Update image metadata (alt text, caption)
   * Allows updating accessibility and display information
   */
  updateImageMetadata: protectedProcedure
    .input(z.object({
      ...ProductAccessSchema.shape,
      imageId: z.string().cuid('Invalid image ID'),
      altText: z.string().max(500, 'Alt text too long').optional(),
      caption: z.string().max(1000, 'Caption too long').optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      const { productId, factoryId, imageId, altText, caption } = input;
      const userId = ctx.user.id;

      // Validate factory access
      await validateFactoryAccess(productId, factoryId, userId);

      // Validate image exists and belongs to product
      const existingImage = await prisma.productImage.findFirst({
        where: {
          id: imageId,
          productId: productId,
          status: 'ACTIVE'
        }
      });

      if (!existingImage) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Image not found or not active'
        });
      }

      // Update image metadata
      const updatedImage = await prisma.productImage.update({
        where: { id: imageId },
        data: {
          altText: altText !== undefined ? altText : existingImage.altText,
          caption: caption !== undefined ? caption : existingImage.caption,
        }
      });

      // Log the operation
      await prisma.imageOperation.create({
        data: {
          operationType: 'UPDATE_METADATA',
          operationData: {
            imageId,
            oldAltText: existingImage.altText,
            newAltText: altText,
            oldCaption: existingImage.caption,
            newCaption: caption,
          },
          productId,
          userId,
        }
      });

      console.log(`✅ Updated metadata for image ${imageId}`);

      return {
        success: true,
        image: updatedImage,
        message: 'Image metadata updated successfully',
      };
    }),

  /**
   * Update image alt text for accessibility (legacy support)
   * Supports both alt and altText fields for backward compatibility
   */
  updateAltText: protectedProcedure
    .input(z.object({
      ...ProductAccessSchema.shape,
      imageId: z.string().cuid('Invalid image ID'),
      altText: z.string().max(500, 'Alt text too long'),
      caption: z.string().max(1000, 'Caption too long').optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      const { productId, factoryId, imageId, altText, caption } = input;
      const userId = ctx.user.id;

      // Validate factory access
      await validateFactoryAccess(productId, factoryId, userId);

      // Validate image exists and belongs to product
      const image = await prisma.productImage.findFirst({
        where: {
          id: imageId,
          productId: productId,
          status: 'ACTIVE'
        }
      });

      if (!image) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Image not found or not active'
        });
      }

      // Update alt text and caption
      await prisma.$transaction(async (tx) => {
        await tx.productImage.update({
          where: { id: imageId },
          data: {
            altText,
            alt: altText, // Update both fields for backward compatibility
            ...(caption !== undefined && { caption })
          }
        });

        // Log the operation
        await tx.imageOperation.create({
          data: {
            operationType: 'UPDATE_ALT_TEXT',
            operationData: {
              imageId,
              oldAltText: image.altText,
              newAltText: altText,
              oldCaption: image.caption,
              newCaption: caption,
            },
            productId,
            userId,
          }
        });
      });

      console.log(`✅ Updated alt text for image ${imageId}`);

      return {
        success: true,
        message: 'Alt text updated successfully',
      };
    }),

  /**
   * Get image operation history for audit purposes
   * Returns paginated list of operations for a product
   */
  getImageHistory: protectedProcedure
    .input(z.object({
      ...ProductAccessSchema.shape,
      limit: z.number().int().min(1).max(100).default(20),
      offset: z.number().int().min(0).default(0),
    }))
    .query(async ({ input, ctx }) => {
      const { productId, factoryId, limit, offset } = input;
      const userId = ctx.user.id;

      // Validate factory access
      await validateFactoryAccess(productId, factoryId, userId);

      // Get operation history with user details
      const [operations, totalCount] = await Promise.all([
        prisma.imageOperation.findMany({
          where: { productId },
          orderBy: { createdAt: 'desc' },
          take: limit,
          skip: offset,
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true
              }
            }
          }
        }),
        prisma.imageOperation.count({
          where: { productId }
        })
      ]);

      console.log(`📋 Retrieved ${operations.length} image operations for product ${productId}`);

      return {
        operations,
        totalCount,
        hasMore: offset + limit < totalCount,
        nextOffset: offset + limit < totalCount ? offset + limit : null,
      };
    }),

  /**
   * Get current images for a product
   * Returns active images with metadata, sorted by sortOrder
   */
  getProductImages: protectedProcedure
    .input(ProductAccessSchema)
    .query(async ({ input, ctx }) => {
      const { productId, factoryId } = input;
      const userId = ctx.user.id;

      // Validate factory access
      await validateFactoryAccess(productId, factoryId, userId);

      // Get active images
      const images = await prisma.productImage.findMany({
        where: {
          productId,
          status: 'ACTIVE'
        },
        orderBy: { sortOrder: 'asc' },
        select: {
          id: true,
          url: true,
          alt: true,
          altText: true,
          caption: true,
          sortOrder: true,
          isMain: true,
          uploadedAt: true,
          fileSize: true,
          mimeType: true,
          originalName: true,
          status: true,
          createdAt: true,
          updatedAt: true,
        }
      });

      console.log(`🖼️ Retrieved ${images.length} images for product ${productId}`);

      return {
        images,
        totalCount: images.length,
        mainImage: images.find(img => img.isMain) || images[0] || null,
      };
    }),
});