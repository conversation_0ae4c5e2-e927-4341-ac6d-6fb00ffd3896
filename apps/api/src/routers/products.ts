import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, protectedProcedure, publicProcedure, requirePermission } from '../lib/trpc';
import { parse } from 'csv-parse/sync';
import { Prisma } from '@prisma/client';
import {
  validateHSCode,
  validateCountryCode,
  validateExportRestriction,
  generateComplianceChecklist,
  getCertificationStatus,
  COUNTRY_CODES,
  CERTIFICATION_TYPES,
  COMPLIANCE_STANDARDS,
} from '../lib/compliance';
import {
  calculateComprehensivePrice,
  calculateCostBreakdown,
  convertCurrency,
  validatePricingData,
  PRICING_REGIONS,
  ADJUSTMENT_TYPES,
  EXCHANGE_RATES,
} from '../lib/pricing';
import {
  InventoryLocationData,
  StockMovementData,
  ReservationData,
  calculateAvailableQuantity,
  calculateReorderQuantity,
  calculateInventoryTurnover,
  calculateDaysOfInventory,
  calculateStockoutRisk,
  validateInventoryLocation,
  validateStockMovement,
  validateReservation,
  generateInventoryAlerts,
  calculateInventoryMetrics,
  calculateLocationPerformance,
} from '../lib/inventory';

// Helper function to validate and transform CSV record to product data
async function validateAndTransformRecord(
  record: any,
  rowIndex: number,
  categoryMap: Map<string, string>,
  factoryId: string
) {
  const errors: string[] = [];

  // Required fields validation
  if (!record.name || typeof record.name !== 'string' || record.name.trim().length === 0) {
    errors.push('name is required');
  }

  if (!record.basePrice || isNaN(parseFloat(record.basePrice))) {
    errors.push('basePrice must be a valid number');
  }

  if (!record.categoryName || typeof record.categoryName !== 'string') {
    errors.push('categoryName is required');
  }

  // Validate category exists
  const categoryId = categoryMap.get(record.categoryName?.toLowerCase());
  if (!categoryId) {
    errors.push(`Category "${record.categoryName}" not found. Please create the category first.`);
  }

  if (errors.length > 0) {
    throw new Error(`Row ${rowIndex}: ${errors.join(', ')}`);
  }

  // Generate slug from name
  const slug = record.name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '')
    .substring(0, 50);

  // Transform and validate data
  const productData = {
    name: record.name.trim(),
    slug: `${slug}-${Date.now()}`, // Ensure uniqueness
    description: record.description?.trim() || null,
    shortDescription: record.shortDescription?.trim() || null,
    basePrice: parseFloat(record.basePrice),
    currency: record.currency || 'USD',
    minOrderQty: record.minOrderQty ? parseInt(record.minOrderQty) : 1,
    maxOrderQty: record.maxOrderQty ? parseInt(record.maxOrderQty) : null,
    sku: record.sku?.trim() || null,
    model: record.model?.trim() || null,
    brand: record.brand?.trim() || null,
    weight: record.weight ? parseFloat(record.weight) : null,
    materials: record.materials ? record.materials.split(',').map((m: string) => m.trim()) : [],
    colors: record.colors ? record.colors.split(',').map((c: string) => c.trim()) : [],
    tags: record.tags ? record.tags.split(',').map((t: string) => t.trim()) : [],
    stockQuantity: record.stockQuantity ? parseInt(record.stockQuantity) : 0,
    categoryId: categoryId!,
    metaTitle: record.metaTitle?.trim() || null,
    metaDescription: record.metaDescription?.trim() || null,
    status: 'ACTIVE' as const,
    isActive: true,
    isFeatured: false,
  };

  // Additional validations
  if (productData.basePrice <= 0) {
    throw new Error(`Row ${rowIndex}: basePrice must be greater than 0`);
  }

  if (productData.minOrderQty <= 0) {
    throw new Error(`Row ${rowIndex}: minOrderQty must be greater than 0`);
  }

  if (productData.maxOrderQty && productData.maxOrderQty < productData.minOrderQty) {
    throw new Error(`Row ${rowIndex}: maxOrderQty must be greater than or equal to minOrderQty`);
  }

  if (productData.weight && productData.weight < 0) {
    throw new Error(`Row ${rowIndex}: weight must be non-negative`);
  }

  if (productData.stockQuantity < 0) {
    throw new Error(`Row ${rowIndex}: stockQuantity must be non-negative`);
  }

  return productData;
}

// Helper function to format product response
function formatProductResponse(product: any) {
  return {
    id: product.id,
    name: product.name,
    slug: product.slug,
    description: product.description,
    shortDescription: product.shortDescription,
    basePrice: product.basePrice.toNumber(),
    currency: product.currency,
    minOrderQty: product.minOrderQty,
    maxOrderQty: product.maxOrderQty,
    sku: product.sku,
    model: product.model,
    brand: product.brand,
    weight: product.weight?.toNumber(),
    dimensions: product.dimensions,
    materials: product.materials,
    colors: product.colors,
    tags: product.tags,
    stockQuantity: product.stockQuantity,
    status: product.status,
    isActive: product.isActive,
    isFeatured: product.isFeatured,
    metaTitle: product.metaTitle,
    metaDescription: product.metaDescription,
    // Enterprise fields
    specifications: product.specifications,
    compliance: product.compliance,
    qualityData: product.qualityData,
    manufacturingData: product.manufacturingData,
    tradingData: product.tradingData,
    marketingData: product.marketingData,
    // Relations
    category: product.category,
    factory: product.factory,
    images: product.images || [],
    priceBreaks: product.priceBreaks || [],
    inventoryLocations: product.inventoryLocations || [],
    // Timestamps
    createdAt: product.createdAt,
    updatedAt: product.updatedAt,
    createdBy: product.createdBy,
    updatedBy: product.updatedBy,
  };
}

export const productsRouter = router({
  // Get all products (public for browsing)
  getAll: publicProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      search: z.string().optional(),
      categoryId: z.string().cuid().optional(),
      factoryId: z.string().cuid().optional(),
      priceRange: z.object({
        min: z.number().min(0),
        max: z.number().min(0),
      }).optional(),
      sortBy: z.enum(['name', 'price', 'created', 'updated']).default('created'),
      sortOrder: z.enum(['asc', 'desc']).default('desc'),
    }))
    .query(async ({ input, ctx }) => {
      const { page, limit, search, categoryId, factoryId, priceRange, sortBy, sortOrder } = input;
      const skip = (page - 1) * limit;
      
      // Build where clause
      const where: any = {
        isActive: true,
        status: 'ACTIVE',
      };
      
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { tags: { hasSome: [search] } },
        ];
      }
      
      if (categoryId) where.categoryId = categoryId;
      if (factoryId) where.factoryId = factoryId;
      
      if (priceRange) {
        where.basePrice = {
          gte: priceRange.min,
          lte: priceRange.max,
        };
      }
      
      // Build order by clause
      const orderBy: any = {};
      switch (sortBy) {
        case 'name':
          orderBy.name = sortOrder;
          break;
        case 'price':
          orderBy.basePrice = sortOrder;
          break;
        case 'created':
          orderBy.createdAt = sortOrder;
          break;
        case 'updated':
          orderBy.updatedAt = sortOrder;
          break;
      }
      
      const [products, total] = await Promise.all([
        ctx.db.product.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          include: {
            images: {
              where: { isMain: true },
              take: 1,
            },
            category: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            factory: {
              select: {
                id: true,
                name: true,
                slug: true,
                verificationStatus: true,
                addressCountry: true,
              },
            },
            _count: {
              select: {
                reviews: { where: { isApproved: true } },
              },
            },
          },
        }),
        ctx.db.product.count({ where }),
      ]);
      
      return {
        data: products.map(product => ({
          id: product.id,
          name: product.name,
          slug: product.slug,
          description: product.description,
          shortDescription: product.shortDescription,
          basePrice: Number(product.basePrice),
          currency: product.currency,
          minOrderQty: product.minOrderQty,
          maxOrderQty: product.maxOrderQty,
          sku: product.sku,
          model: product.model,
          brand: product.brand,
          weight: product.weight ? Number(product.weight) : null,
          dimensions: product.dimensions,
          materials: product.materials,
          colors: product.colors,
          tags: product.tags,
          stockStatus: product.stockStatus,
          isFeatured: product.isFeatured,
          mainImage: product.images[0]?.url || null,
          category: product.category,
          factory: product.factory,
          reviewCount: product._count.reviews,
          createdAt: product.createdAt,
          updatedAt: product.updatedAt,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1,
        },
      };
    }),
  
  // Get product by ID or slug
  getByIdOrSlug: publicProcedure
    .input(z.object({
      identifier: z.string(), // Can be ID or slug
      factoryId: z.string().cuid().optional(),
    }))
    .query(async ({ input, ctx }) => {
      const product = await ctx.db.product.findFirst({
        where: {
          OR: [
            { id: input.identifier },
            { slug: input.identifier },
          ],
          ...(input.factoryId && { factoryId: input.factoryId }),
          isActive: true,
          status: 'ACTIVE',
        },
        include: {
          images: {
            orderBy: { sortOrder: 'asc' },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
              verificationStatus: true,
              addressCountry: true,
              email: true,
              phone: true,
              website: true,
            },
          },
          priceBreaks: {
            orderBy: { minQuantity: 'asc' },
          },
          inventoryLocations: true,
        },
      });
      
      if (!product) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Product not found',
        });
      }
      
      return formatProductResponse(product);
    }),
  
  // Create product (factory users only)
  create: protectedProcedure
    .use(requirePermission('PRODUCT_WRITE'))
    .input(z.object({
      name: z.string().min(1),
      description: z.string().optional(),
      shortDescription: z.string().optional(),
      basePrice: z.number().min(0),
      currency: z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']).default('USD'),
      minOrderQty: z.number().min(1).default(1),
      maxOrderQty: z.number().min(1).optional(),
      categoryId: z.string().cuid(),
      sku: z.string().optional(),
      model: z.string().optional(),
      brand: z.string().optional(),
      weight: z.number().min(0).optional(),
      dimensions: z.object({
        length: z.number(),
        width: z.number(),
        height: z.number(),
        unit: z.string(),
      }).optional(),
      materials: z.array(z.string()).default([]),
      colors: z.array(z.string()).default([]),
      tags: z.array(z.string()).default([]),
      stockQuantity: z.number().min(0).default(0),
      metaTitle: z.string().optional(),
      metaDescription: z.string().optional(),
      images: z.array(z.object({
        url: z.string().url(),
        alt: z.string().optional(),
        caption: z.string().optional(),
        isMain: z.boolean().default(false),
      })).default([]),
    }))
    .mutation(async ({ input, ctx }) => {
      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }
      
      // Verify category belongs to factory
      const category = await ctx.db.category.findFirst({
        where: {
          id: input.categoryId,
          factoryId: ctx.user.factoryId,
        },
      });
      
      if (!category) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Category not found or access denied',
        });
      }
      
      // Generate unique slug
      const baseSlug = input.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-|-$/g, '');
      
      let slug = baseSlug;
      let counter = 1;
      
      while (await ctx.db.product.findFirst({
        where: { factoryId: ctx.user.factoryId, slug }
      })) {
        slug = `${baseSlug}-${counter}`;
        counter++;
      }
      
      // Create product with images
      const { images, ...productData } = input;

      const product = await ctx.db.product.create({
        data: {
          name: productData.name,
          description: productData.description || null,
          shortDescription: productData.shortDescription || null,
          basePrice: productData.basePrice,
          currency: productData.currency,
          minOrderQty: productData.minOrderQty,
          maxOrderQty: productData.maxOrderQty || null,
          categoryId: productData.categoryId,
          sku: productData.sku || null,
          model: productData.model || null,
          brand: productData.brand || null,
          weight: productData.weight || null,
          dimensions: productData.dimensions || Prisma.JsonNull,
          materials: productData.materials,
          colors: productData.colors,
          tags: productData.tags,
          stockQuantity: productData.stockQuantity,
          metaTitle: productData.metaTitle || null,
          metaDescription: productData.metaDescription || null,
          slug,
          factoryId: ctx.user.factoryId!,
          createdBy: ctx.user.id,
          updatedBy: ctx.user.id,
          images: {
            create: images.map((image, index) => ({
              url: image.url,
              alt: image.alt || null,
              caption: image.caption || null,
              isMain: image.isMain || false,
              sortOrder: index,
            })),
          },
        },
        include: {
          images: true,
          category: true,
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });
      
      // Log product creation
      await ctx.db.auditLog.create({
        data: {
          type: 'BUSINESS',
          event: 'PRODUCT_CREATED',
          entityType: 'product',
          entityId: product.id,
          userId: ctx.user.id,
          factoryId: ctx.user.factoryId,
          ipAddress: ctx.ip,
          userAgent: ctx.userAgent,
          metadata: {
            productName: product.name,
            productSlug: product.slug,
            basePrice: product.basePrice.toString(),
            currency: product.currency,
          },
        },
      });
      
      return formatProductResponse(product);
    }),
  
  // Get products for factory management
  getFactoryProducts: protectedProcedure
    .use(requirePermission('PRODUCT_READ'))
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      search: z.string().optional(),
      status: z.enum(['DRAFT', 'ACTIVE', 'INACTIVE', 'OUT_OF_STOCK', 'DISCONTINUED']).optional(),
      categoryId: z.string().cuid().optional(),
    }))
    .query(async ({ input, ctx }) => {
      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }
      
      const { page, limit, search, status, categoryId } = input;
      const skip = (page - 1) * limit;
      
      const where: any = {
        factoryId: ctx.user.factoryId,
      };
      
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { sku: { contains: search, mode: 'insensitive' } },
        ];
      }
      
      if (status) where.status = status;
      if (categoryId) where.categoryId = categoryId;
      
      const [products, total] = await Promise.all([
        ctx.db.product.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            images: {
              where: { isMain: true },
              take: 1,
            },
            category: {
              select: {
                id: true,
                name: true,
              },
            },
            _count: {
              select: {
                orderItems: true,
                reviews: true,
              },
            },
          },
        }),
        ctx.db.product.count({ where }),
      ]);
      
      return {
        data: products.map(product => ({
          id: product.id,
          name: product.name,
          slug: product.slug,
          basePrice: Number(product.basePrice),
          currency: product.currency,
          stockQuantity: product.stockQuantity,
          stockStatus: product.stockStatus,
          status: product.status,
          isActive: product.isActive,
          isFeatured: product.isFeatured,
          mainImage: product.images[0]?.url || null,
          category: product.category,
          orderCount: product._count.orderItems,
          reviewCount: product._count.reviews,
          createdAt: product.createdAt,
          updatedAt: product.updatedAt,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1,
        },
      };
    }),

  // Bulk import products from CSV
  bulkImport: protectedProcedure
    .use(requirePermission('PRODUCT_WRITE'))
    .input(z.object({
      csvData: z.string().min(1),
      options: z.object({
        skipErrors: z.boolean().default(true),
        validateOnly: z.boolean().default(false),
        updateExisting: z.boolean().default(false),
        batchSize: z.number().min(1).max(100).default(50),
      }).default({}),
    }))
    .mutation(async ({ input, ctx }) => {
      const { csvData, options } = input;
      const { skipErrors, validateOnly, updateExisting, batchSize } = options;

      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }

      try {
        // Parse CSV data
        const records = parse(csvData, {
          columns: true,
          skip_empty_lines: true,
          trim: true,
        });

        if (records.length === 0) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'CSV file is empty or has no valid records',
          });
        }

        if (records.length > 1000) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Maximum 1000 products allowed per import',
          });
        }

        const results = {
          totalRecords: records.length,
          processedRecords: 0,
          successCount: 0,
          errorCount: 0,
          errors: [] as Array<{
            row: number;
            field?: string;
            message: string;
            data: any;
          }>,
          createdProducts: [] as any[],
        };

        // Get factory categories for validation
        const factoryCategories = await ctx.db.category.findMany({
          where: { factoryId: ctx.user.factoryId },
          select: { id: true, name: true },
        });

        const categoryMap = new Map(
          factoryCategories.map(cat => [cat.name.toLowerCase(), cat.id])
        );

        // Process records in batches
        for (let i = 0; i < records.length; i += batchSize) {
          const batch = records.slice(i, i + batchSize);

          for (let j = 0; j < batch.length; j++) {
            const rowIndex = i + j + 2; // +2 for header row and 1-based indexing
            const record = batch[j];

            try {
              // Validate and transform record
              const productData = await validateAndTransformRecord(
                record,
                rowIndex,
                categoryMap,
                ctx.user.factoryId
              );

              if (!validateOnly) {
                // Check if product exists (by SKU or name)
                let existingProduct = null;
                if (productData.sku) {
                  existingProduct = await ctx.db.product.findFirst({
                    where: {
                      factoryId: ctx.user.factoryId,
                      sku: productData.sku,
                    },
                  });
                }

                if (!existingProduct) {
                  existingProduct = await ctx.db.product.findFirst({
                    where: {
                      factoryId: ctx.user.factoryId,
                      name: productData.name,
                    },
                  });
                }

                if (existingProduct && !updateExisting) {
                  results.errors.push({
                    row: rowIndex,
                    message: `Product already exists: ${productData.name}`,
                    data: record,
                  });
                  results.errorCount++;
                } else if (existingProduct && updateExisting) {
                  // Update existing product
                  const updatedProduct = await ctx.db.product.update({
                    where: { id: existingProduct.id },
                    data: {
                      ...productData,
                      updatedBy: ctx.user.id,
                    },
                    include: {
                      category: true,
                      factory: {
                        select: { id: true, name: true, slug: true },
                      },
                    },
                  });
                  results.createdProducts.push(updatedProduct);
                  results.successCount++;
                } else {
                  // Create new product
                  const newProduct = await ctx.db.product.create({
                    data: {
                      ...productData,
                      factoryId: ctx.user.factoryId,
                      createdBy: ctx.user.id,
                      updatedBy: ctx.user.id,
                    },
                    include: {
                      category: true,
                      factory: {
                        select: { id: true, name: true, slug: true },
                      },
                    },
                  });
                  results.createdProducts.push(newProduct);
                  results.successCount++;
                }
              } else {
                // Validation only mode
                results.successCount++;
              }

              results.processedRecords++;
            } catch (error) {
              results.errorCount++;
              results.errors.push({
                row: rowIndex,
                message: error instanceof Error ? error.message : 'Unknown error',
                data: record,
              });

              if (!skipErrors) {
                throw error;
              }
            }
          }
        }

        return results;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Bulk import failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }),

  // Update product (factory users only)
  update: protectedProcedure
    .use(requirePermission('PRODUCT_WRITE'))
    .input(z.object({
      id: z.string().cuid(),
      name: z.string().min(1).optional(),
      description: z.string().optional(),
      shortDescription: z.string().optional(),
      basePrice: z.number().min(0).optional(),
      currency: z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']).optional(),
      minOrderQty: z.number().min(1).optional(),
      maxOrderQty: z.number().min(1).optional(),
      categoryId: z.string().cuid().optional(),
      sku: z.string().optional(),
      model: z.string().optional(),
      brand: z.string().optional(),
      weight: z.number().min(0).optional(),
      dimensions: z.object({
        length: z.number(),
        width: z.number(),
        height: z.number(),
        unit: z.string(),
        weightUnit: z.string().optional(),
      }).optional(),
      materials: z.array(z.string()).optional(),
      colors: z.array(z.string()).optional(),
      tags: z.array(z.string()).optional(),
      stockQuantity: z.number().min(0).optional(),
      status: z.enum(['DRAFT', 'ACTIVE', 'INACTIVE', 'OUT_OF_STOCK', 'DISCONTINUED']).optional(),
      isFeatured: z.boolean().optional(),
      metaTitle: z.string().optional(),
      metaDescription: z.string().optional(),
      // Enterprise fields
      specifications: z.object({
        manufacturingTime: z.number().int().positive().optional(), // days
        productionCapacity: z.number().int().positive().optional(), // units per month
        leadTime: z.number().int().positive().optional(), // days
        customizationOptions: z.array(z.string()).optional(),
        materialComposition: z.string().optional(),
        technicalDataSheet: z.string().url().optional(),
        userManual: z.string().url().optional(),
        warrantyTerms: z.string().optional(),
      }).optional(),
      compliance: z.object({
        certifications: z.array(z.string()).optional(), // CE, FDA, ISO, etc.
        complianceStandards: z.array(z.string()).optional(),
        exportRestrictions: z.array(z.string()).optional(),
        hsCode: z.string().regex(/^\d{6,10}$/).optional(), // Harmonized System code
        countryOfOrigin: z.string().length(2).optional(), // ISO country code
        exportLicense: z.boolean().optional(),
        restrictedCountries: z.array(z.string()).optional(),
        tariffClassification: z.string().optional(),
      }).optional(),
      qualityData: z.object({
        qualityGrade: z.enum(['A', 'B', 'C']).optional(),
        defectRate: z.number().min(0).max(100).optional(), // percentage
        qualityControlProcess: z.string().optional(),
        testingCertificates: z.array(z.string()).optional(),
        inspectionProtocol: z.string().optional(),
        qualityStandards: z.array(z.string()).optional(),
        lastTestDate: z.string().datetime().optional(),
        nextTestDue: z.string().datetime().optional(),
      }).optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      const { id, ...updateData } = input;

      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }

      try {
        // Check if product exists and belongs to factory
        const existingProduct = await ctx.db.product.findFirst({
          where: {
            id,
            factoryId: ctx.user.factoryId,
          },
        });

        if (!existingProduct) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Product not found or access denied',
          });
        }

        // Update product with enterprise fields
        const updateFields: any = {
          updatedBy: ctx.user.id,
          updatedAt: new Date(),
        };

        // Only include fields that are actually provided
        Object.keys(updateData).forEach(key => {
          const value = (updateData as any)[key];
          if (value !== undefined) {
            updateFields[key] = value;
          }
        });

        const updatedProduct = await ctx.db.product.update({
          where: { id },
          data: updateFields,
          include: {
            category: true,
            images: {
              orderBy: { sortOrder: 'asc' },
            },
            factory: {
              select: {
                id: true,
                name: true,
                verificationStatus: true,
              },
            },
          },
        });

        return formatProductResponse(updatedProduct);
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to update product: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }),

  // Manage product price breaks
  updatePriceBreaks: protectedProcedure
    .use(requirePermission('PRODUCT_WRITE'))
    .input(z.object({
      productId: z.string().cuid(),
      priceBreaks: z.array(z.object({
        minQuantity: z.number().int().positive(),
        unitPrice: z.number().positive(),
        currency: z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']).default('USD'),
      })),
    }))
    .mutation(async ({ input, ctx }) => {
      const { productId, priceBreaks } = input;

      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }

      try {
        // Verify product belongs to factory
        const product = await ctx.db.product.findFirst({
          where: {
            id: productId,
            factoryId: ctx.user.factoryId,
          },
        });

        if (!product) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Product not found or access denied',
          });
        }

        // Delete existing price breaks and create new ones
        await ctx.db.$transaction(async (tx) => {
          await tx.productPriceBreak.deleteMany({
            where: { productId },
          });

          if (priceBreaks.length > 0) {
            await tx.productPriceBreak.createMany({
              data: priceBreaks.map(pb => ({
                productId,
                minQuantity: pb.minQuantity,
                unitPrice: pb.unitPrice,
                currency: pb.currency,
              })),
            });
          }
        });

        return { success: true, message: 'Price breaks updated successfully' };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to update price breaks: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }),

  // Manage product inventory locations
  updateInventoryLocations: protectedProcedure
    .use(requirePermission('PRODUCT_WRITE'))
    .input(z.object({
      productId: z.string().cuid(),
      locations: z.array(z.object({
        locationName: z.string().min(1),
        stockQuantity: z.number().int().min(0),
        reservedQuantity: z.number().int().min(0).default(0),
        reorderPoint: z.number().int().min(0).optional(),
        maxStockLevel: z.number().int().min(0).optional(),
      })),
    }))
    .mutation(async ({ input, ctx }) => {
      const { productId, locations } = input;

      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }

      try {
        // Verify product belongs to factory
        const product = await ctx.db.product.findFirst({
          where: {
            id: productId,
            factoryId: ctx.user.factoryId,
          },
        });

        if (!product) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Product not found or access denied',
          });
        }

        // Delete existing locations and create new ones
        await ctx.db.$transaction(async (tx) => {
          await tx.productInventoryLocation.deleteMany({
            where: { productId },
          });

          if (locations.length > 0) {
            await tx.productInventoryLocation.createMany({
              data: locations.map(loc => ({
                productId,
                factoryId: ctx.user.factoryId!,
                locationName: loc.locationName,
                stockQuantity: loc.stockQuantity,
                reservedQuantity: loc.reservedQuantity,
                availableQuantity: loc.stockQuantity - loc.reservedQuantity,
                reorderPoint: loc.reorderPoint || null,
                maxStockLevel: loc.maxStockLevel || null,
              })),
            });
          }
        });

        return { success: true, message: 'Inventory locations updated successfully' };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to update inventory locations: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }),

  // Compliance & Certification Management
  updateCompliance: protectedProcedure
    .use(requirePermission('PRODUCT_WRITE'))
    .input(z.object({
      productId: z.string().cuid(),
      compliance: z.object({
        certifications: z.array(z.object({
          name: z.string().min(1), // e.g., "CE", "FDA", "ISO 9001"
          number: z.string().optional(), // Certificate number
          issuedBy: z.string().optional(), // Issuing authority
          issuedDate: z.string().datetime().optional(),
          expiryDate: z.string().datetime().optional(),
          documentUrl: z.string().url().optional(), // Link to certificate document
          status: z.enum(['ACTIVE', 'EXPIRED', 'PENDING', 'REVOKED']).default('ACTIVE'),
        })).optional(),
        complianceStandards: z.array(z.object({
          standard: z.string().min(1), // e.g., "RoHS", "REACH", "FCC"
          version: z.string().optional(),
          complianceDate: z.string().datetime().optional(),
          testingLab: z.string().optional(),
          reportNumber: z.string().optional(),
          reportUrl: z.string().url().optional(),
        })).optional(),
        exportRestrictions: z.array(z.object({
          country: z.string().length(2), // ISO country code
          restriction: z.string().min(1), // Description of restriction
          restrictionType: z.enum(['PROHIBITED', 'RESTRICTED', 'LICENSE_REQUIRED', 'QUOTA']),
          licenseRequired: z.boolean().default(false),
          notes: z.string().optional(),
        })).optional(),
        hsCode: z.string().regex(/^\d{6,10}$/).optional(), // Harmonized System code
        countryOfOrigin: z.string().length(2).optional(), // ISO country code
        exportLicense: z.object({
          required: z.boolean(),
          licenseNumber: z.string().optional(),
          issuingAuthority: z.string().optional(),
          validFrom: z.string().datetime().optional(),
          validUntil: z.string().datetime().optional(),
          documentUrl: z.string().url().optional(),
        }).optional(),
        restrictedCountries: z.array(z.string().length(2)).optional(), // ISO country codes
        tariffClassification: z.object({
          code: z.string().min(1),
          description: z.string().optional(),
          dutyRate: z.number().min(0).max(100).optional(), // Percentage
          preferentialTreatment: z.boolean().default(false),
        }).optional(),
        regulatoryApprovals: z.array(z.object({
          agency: z.string().min(1), // e.g., "EPA", "OSHA", "CPSC"
          approvalType: z.string().min(1),
          approvalNumber: z.string().optional(),
          approvalDate: z.string().datetime().optional(),
          expiryDate: z.string().datetime().optional(),
          documentUrl: z.string().url().optional(),
        })).optional(),
      }),
    }))
    .mutation(async ({ input, ctx }) => {
      const { productId, compliance } = input;

      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }

      try {
        // Verify product belongs to factory
        const product = await ctx.db.product.findFirst({
          where: {
            id: productId,
            factoryId: ctx.user.factoryId,
          },
        });

        if (!product) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Product not found or access denied',
          });
        }

        // Update product compliance data
        const updatedProduct = await ctx.db.product.update({
          where: { id: productId },
          data: {
            compliance: compliance as any,
            updatedBy: ctx.user.id,
            updatedAt: new Date(),
          },
        });

        return {
          success: true,
          message: 'Compliance data updated successfully',
          compliance: updatedProduct.compliance,
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to update compliance data: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }),

  // Get compliance data for a product
  getCompliance: protectedProcedure
    .use(requirePermission('PRODUCT_READ'))
    .input(z.object({
      productId: z.string().cuid(),
    }))
    .query(async ({ input, ctx }) => {
      const { productId } = input;

      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }

      try {
        const product = await ctx.db.product.findFirst({
          where: {
            id: productId,
            factoryId: ctx.user.factoryId,
          },
          select: {
            id: true,
            name: true,
            compliance: true,
          },
        });

        if (!product) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Product not found or access denied',
          });
        }

        return {
          productId: product.id,
          productName: product.name,
          compliance: product.compliance || {},
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to get compliance data: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }),

  // Validate HS Code
  validateHSCode: protectedProcedure
    .use(requirePermission('PRODUCT_READ'))
    .input(z.object({
      hsCode: z.string().min(1),
    }))
    .query(async ({ input }) => {
      const { hsCode } = input;

      try {
        const validation = validateHSCode(hsCode);

        return {
          ...validation,
          hsCode: validation.valid ? hsCode.replace(/[\s-]/g, '') : hsCode,
        };
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to validate HS Code: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }),

  // Get compliance summary for factory
  getComplianceSummary: protectedProcedure
    .use(requirePermission('PRODUCT_READ'))
    .query(async ({ ctx }) => {
      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }

      try {
        const products = await ctx.db.product.findMany({
          where: {
            factoryId: ctx.user.factoryId,
            status: 'ACTIVE',
          },
          select: {
            id: true,
            name: true,
            compliance: true,
          },
        });

        // Analyze compliance data
        let totalProducts = products.length;
        let productsWithCompliance = 0;
        let productsWithCertifications = 0;
        let productsWithHSCode = 0;
        let productsWithExportRestrictions = 0;
        let expiringSoon = 0;

        const certificationTypes = new Set<string>();
        const complianceStandards = new Set<string>();
        const countriesOfOrigin = new Set<string>();

        products.forEach(product => {
          const compliance = product.compliance as any;

          if (compliance && Object.keys(compliance).length > 0) {
            productsWithCompliance++;
          }

          if (compliance?.certifications?.length > 0) {
            productsWithCertifications++;
            compliance.certifications.forEach((cert: any) => {
              certificationTypes.add(cert.name);

              // Check for expiring certificates (within 30 days)
              if (cert.expiryDate) {
                const expiryDate = new Date(cert.expiryDate);
                const thirtyDaysFromNow = new Date();
                thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

                if (expiryDate <= thirtyDaysFromNow) {
                  expiringSoon++;
                }
              }
            });
          }

          if (compliance?.hsCode) {
            productsWithHSCode++;
          }

          if (compliance?.exportRestrictions?.length > 0) {
            productsWithExportRestrictions++;
          }

          if (compliance?.complianceStandards?.length > 0) {
            compliance.complianceStandards.forEach((standard: any) => {
              complianceStandards.add(standard.standard);
            });
          }

          if (compliance?.countryOfOrigin) {
            countriesOfOrigin.add(compliance.countryOfOrigin);
          }
        });

        return {
          summary: {
            totalProducts,
            productsWithCompliance,
            productsWithCertifications,
            productsWithHSCode,
            productsWithExportRestrictions,
            expiringSoon,
            complianceRate: totalProducts > 0 ? (productsWithCompliance / totalProducts) * 100 : 0,
          },
          insights: {
            certificationTypes: Array.from(certificationTypes),
            complianceStandards: Array.from(complianceStandards),
            countriesOfOrigin: Array.from(countriesOfOrigin),
          },
        };
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to get compliance summary: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }),

  // Validate country code
  validateCountryCode: protectedProcedure
    .use(requirePermission('PRODUCT_READ'))
    .input(z.object({
      countryCode: z.string().length(2),
    }))
    .query(async ({ input }) => {
      const { countryCode } = input;

      try {
        return validateCountryCode(countryCode);
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to validate country code: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }),

  // Check export restrictions for target country
  checkExportRestrictions: protectedProcedure
    .use(requirePermission('PRODUCT_READ'))
    .input(z.object({
      productId: z.string().cuid(),
      targetCountry: z.string().length(2),
    }))
    .query(async ({ input, ctx }) => {
      const { productId, targetCountry } = input;

      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }

      try {
        const product = await ctx.db.product.findFirst({
          where: {
            id: productId,
            factoryId: ctx.user.factoryId,
          },
          select: {
            id: true,
            name: true,
            compliance: true,
          },
        });

        if (!product) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Product not found or access denied',
          });
        }

        const compliance = product.compliance as any;
        const exportRestrictions = compliance?.exportRestrictions || [];

        const restrictionCheck = validateExportRestriction(targetCountry, exportRestrictions);
        const countryValidation = validateCountryCode(targetCountry);

        return {
          productId: product.id,
          productName: product.name,
          targetCountry,
          targetCountryName: countryValidation.countryName,
          ...restrictionCheck,
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to check export restrictions: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }),

  // Generate compliance checklist for target markets
  generateComplianceChecklist: protectedProcedure
    .use(requirePermission('PRODUCT_READ'))
    .input(z.object({
      targetMarkets: z.array(z.string().length(2)),
      productCategory: z.string().optional(),
    }))
    .query(async ({ input }) => {
      const { targetMarkets, productCategory } = input;

      try {
        // Validate all country codes
        const invalidCountries = targetMarkets.filter(country => {
          const validation = validateCountryCode(country);
          return !validation.valid;
        });

        if (invalidCountries.length > 0) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: `Invalid country codes: ${invalidCountries.join(', ')}`,
          });
        }

        const checklist = generateComplianceChecklist(targetMarkets, productCategory);

        return {
          targetMarkets: targetMarkets.map(country => ({
            code: country,
            name: COUNTRY_CODES[country as keyof typeof COUNTRY_CODES] || country,
          })),
          checklist,
          generatedAt: new Date().toISOString(),
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to generate compliance checklist: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }),

  // Get available certification types
  getCertificationTypes: protectedProcedure
    .use(requirePermission('PRODUCT_READ'))
    .query(async () => {
      return {
        certificationTypes: Object.entries(CERTIFICATION_TYPES).map(([key, description]) => ({
          code: key,
          name: key.replace(/_/g, ' '),
          description,
        })),
      };
    }),

  // Get available compliance standards
  getComplianceStandards: protectedProcedure
    .use(requirePermission('PRODUCT_READ'))
    .query(async () => {
      return {
        complianceStandards: Object.entries(COMPLIANCE_STANDARDS).map(([key, description]) => ({
          code: key,
          name: key.replace(/_/g, ' '),
          description,
        })),
      };
    }),

  // Get supported countries
  getSupportedCountries: protectedProcedure
    .use(requirePermission('PRODUCT_READ'))
    .query(async () => {
      return {
        countries: Object.entries(COUNTRY_CODES).map(([code, name]) => ({
          code,
          name,
        })),
      };
    }),

  // Advanced Pricing Structure Management

  // Update regional pricing for a product
  updateRegionalPricing: protectedProcedure
    .use(requirePermission('PRODUCT_WRITE'))
    .input(z.object({
      productId: z.string().cuid(),
      regionalPricing: z.array(z.object({
        region: z.string().min(1),
        country: z.string().length(2).optional(),
        basePrice: z.number().positive(),
        currency: z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']).default('USD'),
        markup: z.number().min(-100).max(1000).optional(), // Percentage markup/discount
        fixedAdjustment: z.number().optional(), // Fixed amount adjustment
        validFrom: z.string().datetime(),
        validUntil: z.string().datetime().optional(),
      })),
    }))
    .mutation(async ({ input, ctx }) => {
      const { productId, regionalPricing } = input;

      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }

      try {
        // Verify product belongs to factory
        const product = await ctx.db.product.findFirst({
          where: {
            id: productId,
            factoryId: ctx.user.factoryId,
          },
        });

        if (!product) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Product not found or access denied',
          });
        }

        // Validate pricing data
        const validation = validatePricingData({
          regionalPricing: regionalPricing.map(rp => ({
            region: rp.region,
            basePrice: rp.basePrice,
          })),
        });

        if (!validation.valid) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: `Invalid pricing data: ${validation.errors.join(', ')}`,
          });
        }

        // Delete existing regional pricing and create new ones
        await ctx.db.$transaction(async (tx) => {
          await tx.productRegionalPricing.deleteMany({
            where: { productId },
          });

          if (regionalPricing.length > 0) {
            await tx.productRegionalPricing.createMany({
              data: regionalPricing.map(rp => ({
                productId,
                region: rp.region,
                country: rp.country || null,
                basePrice: rp.basePrice,
                currency: rp.currency,
                markup: rp.markup || null,
                fixedAdjustment: rp.fixedAdjustment || null,
                validFrom: new Date(rp.validFrom),
                validUntil: rp.validUntil ? new Date(rp.validUntil) : null,
                createdBy: ctx.user.id,
              })),
            });
          }
        });

        return { success: true, message: 'Regional pricing updated successfully' };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to update regional pricing: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }),

  // Update seasonal pricing for a product
  updateSeasonalPricing: protectedProcedure
    .use(requirePermission('PRODUCT_WRITE'))
    .input(z.object({
      productId: z.string().cuid(),
      seasonalPricing: z.array(z.object({
        name: z.string().min(1),
        description: z.string().optional(),
        startDate: z.string().datetime(),
        endDate: z.string().datetime(),
        adjustmentType: z.enum(['PERCENTAGE', 'FIXED']),
        adjustmentValue: z.number(),
        minQuantity: z.number().int().positive().optional(),
        maxQuantity: z.number().int().positive().optional(),
        isActive: z.boolean().default(true),
        priority: z.number().int().min(0).default(0),
      })),
    }))
    .mutation(async ({ input, ctx }) => {
      const { productId, seasonalPricing } = input;

      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }

      try {
        // Verify product belongs to factory
        const product = await ctx.db.product.findFirst({
          where: {
            id: productId,
            factoryId: ctx.user.factoryId,
          },
        });

        if (!product) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Product not found or access denied',
          });
        }

        // Validate date ranges
        for (const sp of seasonalPricing) {
          const startDate = new Date(sp.startDate);
          const endDate = new Date(sp.endDate);

          if (endDate <= startDate) {
            throw new TRPCError({
              code: 'BAD_REQUEST',
              message: `Invalid date range for "${sp.name}": End date must be after start date`,
            });
          }

          if (sp.minQuantity && sp.maxQuantity && sp.maxQuantity <= sp.minQuantity) {
            throw new TRPCError({
              code: 'BAD_REQUEST',
              message: `Invalid quantity range for "${sp.name}": Max quantity must be greater than min quantity`,
            });
          }
        }

        // Delete existing seasonal pricing and create new ones
        await ctx.db.$transaction(async (tx) => {
          await tx.productSeasonalPricing.deleteMany({
            where: { productId },
          });

          if (seasonalPricing.length > 0) {
            await tx.productSeasonalPricing.createMany({
              data: seasonalPricing.map(sp => ({
                productId,
                name: sp.name,
                description: sp.description || null,
                startDate: new Date(sp.startDate),
                endDate: new Date(sp.endDate),
                adjustmentType: sp.adjustmentType,
                adjustmentValue: sp.adjustmentValue,
                minQuantity: sp.minQuantity || null,
                maxQuantity: sp.maxQuantity || null,
                isActive: sp.isActive,
                priority: sp.priority,
                createdBy: ctx.user.id,
              })),
            });
          }
        });

        return { success: true, message: 'Seasonal pricing updated successfully' };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to update seasonal pricing: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }),

  // Update cost breakdown for a product
  updateCostBreakdown: protectedProcedure
    .use(requirePermission('PRODUCT_WRITE'))
    .input(z.object({
      productId: z.string().cuid(),
      costBreakdown: z.object({
        materialCost: z.number().min(0),
        laborCost: z.number().min(0),
        overheadCost: z.number().min(0),
        packagingCost: z.number().min(0).optional(),
        shippingCost: z.number().min(0).optional(),
        marketingCost: z.number().min(0).optional(),
        targetMargin: z.number().min(0).max(1000), // Percentage
        currency: z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']).default('USD'),
        quantityBasis: z.number().int().positive().default(1),
        validFrom: z.string().datetime(),
        validUntil: z.string().datetime().optional(),
      }),
    }))
    .mutation(async ({ input, ctx }) => {
      const { productId, costBreakdown } = input;

      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }

      try {
        // Verify product belongs to factory
        const product = await ctx.db.product.findFirst({
          where: {
            id: productId,
            factoryId: ctx.user.factoryId,
          },
        });

        if (!product) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Product not found or access denied',
          });
        }

        // Calculate cost breakdown
        const calculatedBreakdown = calculateCostBreakdown({
          materialCost: costBreakdown.materialCost,
          laborCost: costBreakdown.laborCost,
          overheadCost: costBreakdown.overheadCost,
          packagingCost: costBreakdown.packagingCost || 0,
          shippingCost: costBreakdown.shippingCost || 0,
          marketingCost: costBreakdown.marketingCost || 0,
          targetMargin: costBreakdown.targetMargin,
          quantityBasis: costBreakdown.quantityBasis,
        });

        // Delete existing cost breakdown and create new one
        await ctx.db.$transaction(async (tx) => {
          await tx.productCostBreakdown.deleteMany({
            where: { productId },
          });

          await tx.productCostBreakdown.create({
            data: {
              productId,
              materialCost: costBreakdown.materialCost,
              laborCost: costBreakdown.laborCost,
              overheadCost: costBreakdown.overheadCost,
              packagingCost: costBreakdown.packagingCost || null,
              shippingCost: costBreakdown.shippingCost || null,
              marketingCost: costBreakdown.marketingCost || null,
              targetMargin: costBreakdown.targetMargin,
              actualMargin: calculatedBreakdown.actualMargin,
              totalCost: calculatedBreakdown.totalCost,
              suggestedPrice: calculatedBreakdown.suggestedPrice,
              currency: costBreakdown.currency,
              quantityBasis: costBreakdown.quantityBasis,
              validFrom: new Date(costBreakdown.validFrom),
              validUntil: costBreakdown.validUntil ? new Date(costBreakdown.validUntil) : null,
              createdBy: ctx.user.id,
            },
          });
        });

        return {
          success: true,
          message: 'Cost breakdown updated successfully',
          breakdown: calculatedBreakdown,
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to update cost breakdown: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }),

  // Calculate comprehensive pricing with all adjustments
  calculatePrice: protectedProcedure
    .use(requirePermission('PRODUCT_READ'))
    .input(z.object({
      productId: z.string().cuid(),
      quantity: z.number().int().positive(),
      region: z.string().optional(),
      country: z.string().length(2).optional(),
      currency: z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']).optional(),
      calculationDate: z.string().datetime().optional(),
    }))
    .query(async ({ input, ctx }) => {
      const { productId, quantity, region, country, currency, calculationDate } = input;

      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }

      try {
        // Get product with all pricing data
        const product = await ctx.db.product.findFirst({
          where: {
            id: productId,
            factoryId: ctx.user.factoryId,
          },
          include: {
            priceBreaks: {
              orderBy: { minQuantity: 'asc' },
            },
            regionalPricing: {
              where: {
                AND: [
                  { validFrom: { lte: calculationDate ? new Date(calculationDate) : new Date() } },
                  {
                    OR: [
                      { validUntil: null },
                      { validUntil: { gte: calculationDate ? new Date(calculationDate) : new Date() } },
                    ],
                  },
                  region ? { region } : {},
                  country ? { country } : {},
                ],
              },
            },
            seasonalPricing: {
              where: {
                isActive: true,
                startDate: { lte: calculationDate ? new Date(calculationDate) : new Date() },
                endDate: { gte: calculationDate ? new Date(calculationDate) : new Date() },
              },
              orderBy: { priority: 'desc' },
            },
          },
        });

        if (!product) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Product not found or access denied',
          });
        }

        // Convert base price to requested currency if needed
        let basePrice = Number(product.basePrice);
        if (currency && currency !== product.currency) {
          basePrice = convertCurrency(basePrice, product.currency, currency);
        }

        // Prepare pricing options
        const pricingOptions: {
          priceBreaks?: Array<{ minQuantity: number; unitPrice: number }>;
          regionalPricing?: {
            basePrice?: number;
            markup?: number;
            fixedAdjustment?: number;
          };
          seasonalPricing?: Array<{
            adjustmentType: string;
            adjustmentValue: number;
            minQuantity?: number;
            maxQuantity?: number;
            priority: number;
            startDate: Date;
            endDate: Date;
            isActive: boolean;
          }>;
          currentDate?: Date;
        } = {};

        if (product.priceBreaks.length > 0) {
          pricingOptions.priceBreaks = product.priceBreaks.map(pb => ({
            minQuantity: pb.minQuantity,
            unitPrice: currency && currency !== pb.currency
              ? convertCurrency(Number(pb.unitPrice), pb.currency, currency)
              : Number(pb.unitPrice),
          }));
        }

        if (product.regionalPricing[0]) {
          const rp = product.regionalPricing[0];
          pricingOptions.regionalPricing = {
            basePrice: currency && currency !== rp.currency
              ? convertCurrency(Number(rp.basePrice), rp.currency, currency)
              : Number(rp.basePrice),
          };

          if (rp.markup) {
            pricingOptions.regionalPricing.markup = Number(rp.markup);
          }

          if (rp.fixedAdjustment) {
            pricingOptions.regionalPricing.fixedAdjustment = currency && currency !== rp.currency
              ? convertCurrency(Number(rp.fixedAdjustment), rp.currency, currency)
              : Number(rp.fixedAdjustment);
          }
        }

        if (product.seasonalPricing.length > 0) {
          pricingOptions.seasonalPricing = product.seasonalPricing.map(sp => {
            const seasonalItem: {
              adjustmentType: string;
              adjustmentValue: number;
              minQuantity?: number;
              maxQuantity?: number;
              priority: number;
              startDate: Date;
              endDate: Date;
              isActive: boolean;
            } = {
              adjustmentType: sp.adjustmentType,
              adjustmentValue: Number(sp.adjustmentValue),
              priority: sp.priority,
              startDate: sp.startDate,
              endDate: sp.endDate,
              isActive: sp.isActive,
            };

            if (sp.minQuantity) {
              seasonalItem.minQuantity = sp.minQuantity;
            }

            if (sp.maxQuantity) {
              seasonalItem.maxQuantity = sp.maxQuantity;
            }

            return seasonalItem;
          });
        }

        if (calculationDate) {
          pricingOptions.currentDate = new Date(calculationDate);
        }

        // Calculate comprehensive pricing
        const pricingResult = calculateComprehensivePrice(basePrice, quantity, pricingOptions);

        return {
          productId,
          productName: product.name,
          quantity,
          currency: currency || product.currency,
          calculationDate: calculationDate || new Date().toISOString(),
          pricing: pricingResult,
          appliedPricing: {
            region: product.regionalPricing[0]?.region,
            country: product.regionalPricing[0]?.country,
            seasonalAdjustments: product.seasonalPricing.length,
            priceBreaks: product.priceBreaks.length,
          },
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to calculate pricing: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }),

  // Get pricing analytics for factory
  getPricingAnalytics: protectedProcedure
    .use(requirePermission('PRODUCT_READ'))
    .query(async ({ ctx }) => {
      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }

      try {
        const [
          totalProducts,
          productsWithPriceBreaks,
          productsWithRegionalPricing,
          productsWithSeasonalPricing,
          productsWithCostBreakdown,
          avgMargin,
        ] = await Promise.all([
          ctx.db.product.count({
            where: { factoryId: ctx.user.factoryId },
          }),
          ctx.db.product.count({
            where: {
              factoryId: ctx.user.factoryId,
              priceBreaks: { some: {} },
            },
          }),
          ctx.db.product.count({
            where: {
              factoryId: ctx.user.factoryId,
              regionalPricing: { some: {} },
            },
          }),
          ctx.db.product.count({
            where: {
              factoryId: ctx.user.factoryId,
              seasonalPricing: { some: {} },
            },
          }),
          ctx.db.product.count({
            where: {
              factoryId: ctx.user.factoryId,
              costBreakdowns: { some: {} },
            },
          }),
          ctx.db.productCostBreakdown.aggregate({
            where: {
              product: { factoryId: ctx.user.factoryId },
            },
            _avg: { actualMargin: true },
          }),
        ]);

        return {
          summary: {
            totalProducts,
            productsWithPriceBreaks,
            productsWithRegionalPricing,
            productsWithSeasonalPricing,
            productsWithCostBreakdown,
            averageMargin: avgMargin._avg.actualMargin ? Number(avgMargin._avg.actualMargin) : null,
            pricingCoverage: {
              priceBreaks: totalProducts > 0 ? (productsWithPriceBreaks / totalProducts) * 100 : 0,
              regionalPricing: totalProducts > 0 ? (productsWithRegionalPricing / totalProducts) * 100 : 0,
              seasonalPricing: totalProducts > 0 ? (productsWithSeasonalPricing / totalProducts) * 100 : 0,
              costBreakdown: totalProducts > 0 ? (productsWithCostBreakdown / totalProducts) * 100 : 0,
            },
          },
        };
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to get pricing analytics: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      }
    }),

  // Get supported pricing regions
  getPricingRegions: protectedProcedure
    .query(async () => {
      return {
        regions: Object.entries(PRICING_REGIONS).map(([code, name]) => ({
          code,
          name,
        })),
      };
    }),

  // Get exchange rates
  getExchangeRates: protectedProcedure
    .query(async () => {
      return {
        rates: EXCHANGE_RATES,
        baseCurrency: 'USD',
        lastUpdated: new Date().toISOString(),
      };
    }),

  // Convert currency amount
  convertCurrencyAmount: protectedProcedure
    .input(z.object({
      amount: z.number(),
      fromCurrency: z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']),
      toCurrency: z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']),
    }))
    .query(async ({ input }) => {
      const { amount, fromCurrency, toCurrency } = input;

      const convertedAmount = convertCurrency(amount, fromCurrency, toCurrency);

      return {
        originalAmount: amount,
        convertedAmount,
        fromCurrency,
        toCurrency,
        exchangeRate: EXCHANGE_RATES[toCurrency] / EXCHANGE_RATES[fromCurrency],
      };
    }),

  // ============================================================================
  // INVENTORY MANAGEMENT PROCEDURES
  // ============================================================================

  // Get inventory locations for a product
  getInventoryLocations: protectedProcedure
    .use(requirePermission('PRODUCT_READ'))
    .input(z.object({
      productId: z.string().cuid(),
    }))
    .query(async ({ input, ctx }) => {
      const { productId } = input;

      // Ensure user has factoryId
      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'User must be associated with a factory',
        });
      }

      // Verify product exists and user has access
      const product = await ctx.db.product.findFirst({
        where: {
          id: productId,
          factoryId: ctx.user.factoryId,
        },
      });

      if (!product) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Product not found or access denied',
        });
      }

      const locations = await ctx.db.productInventoryLocation.findMany({
        where: {
          productId,
          factoryId: ctx.user.factoryId,
        },
        include: {
          reservations: {
            where: {
              status: 'ACTIVE',
              reservedUntil: {
                gte: new Date(),
              },
            },
          },
          movements: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 10,
          },
          alerts: {
            where: {
              status: 'ACTIVE',
            },
            orderBy: {
              createdAt: 'desc',
            },
          },
        },
        orderBy: {
          locationName: 'asc',
        },
      });

      // Calculate metrics for each location
      const locationsWithMetrics = locations.map(location => {
        const activeReservations = location.reservations.reduce((sum: number, res) => sum + res.quantity, 0);
        const availableQuantity = calculateAvailableQuantity(location.stockQuantity, activeReservations);

        // Calculate performance metrics
        const performance = calculateLocationPerformance(location.movements);

        // Generate alerts
        const alertData: {
          stockQuantity: number;
          reservedQuantity: number;
          reorderPoint?: number;
          minStockLevel?: number;
          maxStockLevel?: number;
          locationName: string;
          productName: string;
        } = {
          stockQuantity: location.stockQuantity,
          reservedQuantity: activeReservations,
          locationName: location.locationName,
          productName: product.name,
        };
        if (location.reorderPoint !== null) alertData.reorderPoint = location.reorderPoint;
        if (location.minStockLevel !== null) alertData.minStockLevel = location.minStockLevel;
        if (location.maxStockLevel !== null) alertData.maxStockLevel = location.maxStockLevel;

        const alerts = generateInventoryAlerts(alertData);

        return {
          ...location,
          availableQuantity,
          activeReservations,
          performance,
          generatedAlerts: alerts,
        };
      });

      // Convert locations for metrics calculation
      const locationsForMetrics = locations.map(loc => {
        const result: { stockQuantity: number; reservedQuantity: number; averageCost?: number; lastCost?: number } = {
          stockQuantity: loc.stockQuantity,
          reservedQuantity: loc.reservedQuantity,
        };
        if (loc.averageCost) result.averageCost = Number(loc.averageCost);
        if (loc.lastCost) result.lastCost = Number(loc.lastCost);
        return result;
      });

      return {
        productId,
        productName: product.name,
        locations: locationsWithMetrics,
        summary: calculateInventoryMetrics(locationsForMetrics),
      };
    }),

  // Create or update inventory location
  updateInventoryLocation: protectedProcedure
    .use(requirePermission('PRODUCT_WRITE'))
    .input(z.object({
      productId: z.string().cuid(),
      locationId: z.string().cuid().optional(),
      locationData: z.object({
        locationName: z.string().min(1),
        locationCode: z.string().optional(),
        locationAddress: z.string().optional(),
        locationManager: z.string().optional(),
        stockQuantity: z.number().int().min(0),
        reorderPoint: z.number().int().min(0).optional(),
        maxStockLevel: z.number().int().min(0).optional(),
        minStockLevel: z.number().int().min(0).optional(),
        safetyStock: z.number().int().min(0).optional(),
        averageCost: z.number().min(0).optional(),
        lastCost: z.number().min(0).optional(),
        currency: z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']).optional(),
        isActive: z.boolean().optional(),
        allowBackorders: z.boolean().optional(),
        trackingEnabled: z.boolean().optional(),
        leadTime: z.number().int().min(0).optional(),
        supplierLeadTime: z.number().int().min(0).optional(),
      }),
    }))
    .mutation(async ({ input, ctx }) => {
      const { productId, locationId, locationData } = input;

      // Ensure user has factoryId
      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'User must be associated with a factory',
        });
      }

      // Verify product exists and user has access
      const product = await ctx.db.product.findFirst({
        where: {
          id: productId,
          factoryId: ctx.user.factoryId,
        },
      });

      if (!product) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Product not found or access denied',
        });
      }

      // Validate inventory location data
      const validationData: InventoryLocationData = {
        locationName: locationData.locationName,
        stockQuantity: locationData.stockQuantity,
      };
      if (locationData.locationCode) validationData.locationCode = locationData.locationCode;
      if (locationData.locationAddress) validationData.locationAddress = locationData.locationAddress;
      if (locationData.locationManager) validationData.locationManager = locationData.locationManager;
      if (locationData.reorderPoint !== undefined) validationData.reorderPoint = locationData.reorderPoint;
      if (locationData.maxStockLevel !== undefined) validationData.maxStockLevel = locationData.maxStockLevel;
      if (locationData.minStockLevel !== undefined) validationData.minStockLevel = locationData.minStockLevel;
      if (locationData.safetyStock !== undefined) validationData.safetyStock = locationData.safetyStock;
      if (locationData.averageCost !== undefined) validationData.averageCost = locationData.averageCost;
      if (locationData.lastCost !== undefined) validationData.lastCost = locationData.lastCost;
      if (locationData.currency) validationData.currency = locationData.currency;
      if (locationData.isActive !== undefined) validationData.isActive = locationData.isActive;
      if (locationData.allowBackorders !== undefined) validationData.allowBackorders = locationData.allowBackorders;
      if (locationData.trackingEnabled !== undefined) validationData.trackingEnabled = locationData.trackingEnabled;
      if (locationData.leadTime !== undefined) validationData.leadTime = locationData.leadTime;
      if (locationData.supplierLeadTime !== undefined) validationData.supplierLeadTime = locationData.supplierLeadTime;

      const validationErrors = validateInventoryLocation(validationData);
      if (validationErrors.length > 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: `Validation failed: ${validationErrors.join(', ')}`,
        });
      }

      // Check for duplicate location code within the same factory
      if (locationData.locationCode) {
        const existingLocation = await ctx.db.productInventoryLocation.findFirst({
          where: {
            productId,
            locationCode: locationData.locationCode,
            factoryId: ctx.user.factoryId,
            ...(locationId ? { id: { not: locationId } } : {}),
          },
        });

        if (existingLocation) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: 'Location code already exists for this product',
          });
        }
      }

      const availableQuantity = calculateAvailableQuantity(locationData.stockQuantity, 0);

      if (locationId) {
        // Update existing location
        const updateData: any = {
          locationName: locationData.locationName,
          stockQuantity: locationData.stockQuantity,
          availableQuantity,
          updatedAt: new Date(),
        };

        // Only include defined optional fields
        if (locationData.locationCode !== undefined) updateData.locationCode = locationData.locationCode;
        if (locationData.locationAddress !== undefined) updateData.locationAddress = locationData.locationAddress;
        if (locationData.locationManager !== undefined) updateData.locationManager = locationData.locationManager;
        if (locationData.reorderPoint !== undefined) updateData.reorderPoint = locationData.reorderPoint;
        if (locationData.maxStockLevel !== undefined) updateData.maxStockLevel = locationData.maxStockLevel;
        if (locationData.minStockLevel !== undefined) updateData.minStockLevel = locationData.minStockLevel;
        if (locationData.safetyStock !== undefined) updateData.safetyStock = locationData.safetyStock;
        if (locationData.averageCost !== undefined) updateData.averageCost = locationData.averageCost;
        if (locationData.lastCost !== undefined) updateData.lastCost = locationData.lastCost;
        if (locationData.currency !== undefined) updateData.currency = locationData.currency;
        if (locationData.isActive !== undefined) updateData.isActive = locationData.isActive;
        if (locationData.allowBackorders !== undefined) updateData.allowBackorders = locationData.allowBackorders;
        if (locationData.trackingEnabled !== undefined) updateData.trackingEnabled = locationData.trackingEnabled;
        if (locationData.leadTime !== undefined) updateData.leadTime = locationData.leadTime;
        if (locationData.supplierLeadTime !== undefined) updateData.supplierLeadTime = locationData.supplierLeadTime;

        const updatedLocation = await ctx.db.productInventoryLocation.update({
          where: {
            id: locationId,
          },
          data: updateData,
        });

        return {
          success: true,
          message: 'Inventory location updated successfully',
          location: updatedLocation,
        };
      } else {
        // Create new location
        const createData: any = {
          locationName: locationData.locationName,
          stockQuantity: locationData.stockQuantity,
          productId,
          factoryId: ctx.user.factoryId,
          availableQuantity,
          createdBy: ctx.user.id,
        };

        // Only include defined optional fields
        if (locationData.locationCode !== undefined) createData.locationCode = locationData.locationCode;
        if (locationData.locationAddress !== undefined) createData.locationAddress = locationData.locationAddress;
        if (locationData.locationManager !== undefined) createData.locationManager = locationData.locationManager;
        if (locationData.reorderPoint !== undefined) createData.reorderPoint = locationData.reorderPoint;
        if (locationData.maxStockLevel !== undefined) createData.maxStockLevel = locationData.maxStockLevel;
        if (locationData.minStockLevel !== undefined) createData.minStockLevel = locationData.minStockLevel;
        if (locationData.safetyStock !== undefined) createData.safetyStock = locationData.safetyStock;
        if (locationData.averageCost !== undefined) createData.averageCost = locationData.averageCost;
        if (locationData.lastCost !== undefined) createData.lastCost = locationData.lastCost;
        if (locationData.currency !== undefined) createData.currency = locationData.currency;
        if (locationData.isActive !== undefined) createData.isActive = locationData.isActive;
        if (locationData.allowBackorders !== undefined) createData.allowBackorders = locationData.allowBackorders;
        if (locationData.trackingEnabled !== undefined) createData.trackingEnabled = locationData.trackingEnabled;
        if (locationData.leadTime !== undefined) createData.leadTime = locationData.leadTime;
        if (locationData.supplierLeadTime !== undefined) createData.supplierLeadTime = locationData.supplierLeadTime;

        const newLocation = await ctx.db.productInventoryLocation.create({
          data: createData,
        });

        return {
          success: true,
          message: 'Inventory location created successfully',
          location: newLocation,
        };
      }
    }),

  // Record stock movement
  recordStockMovement: protectedProcedure
    .use(requirePermission('PRODUCT_WRITE'))
    .input(z.object({
      inventoryLocationId: z.string().cuid(),
      movementData: z.object({
        movementType: z.enum(['INBOUND', 'OUTBOUND', 'ADJUSTMENT', 'TRANSFER', 'RETURN', 'DAMAGED', 'EXPIRED']),
        quantity: z.number().int(),
        reason: z.string().optional(),
        notes: z.string().optional(),
        batchNumber: z.string().optional(),
        expiryDate: z.string().datetime().optional(),
        unitCost: z.number().min(0).optional(),
        totalCost: z.number().min(0).optional(),
        currency: z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']).optional(),
        referenceId: z.string().optional(),
        referenceType: z.string().optional(),
        fromLocationId: z.string().cuid().optional(),
        toLocationId: z.string().cuid().optional(),
      }),
    }))
    .mutation(async ({ input, ctx }) => {
      const { inventoryLocationId, movementData } = input;

      // Ensure user has factoryId
      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'User must be associated with a factory',
        });
      }

      // Get current inventory location
      const location = await ctx.db.productInventoryLocation.findFirst({
        where: {
          id: inventoryLocationId,
          factoryId: ctx.user.factoryId,
        },
      });

      if (!location) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Inventory location not found or access denied',
        });
      }

      // Validate stock movement
      const validationMovementData: StockMovementData = {
        movementType: movementData.movementType,
        quantity: movementData.quantity,
      };
      if (movementData.reason) validationMovementData.reason = movementData.reason;
      if (movementData.notes) validationMovementData.notes = movementData.notes;
      if (movementData.batchNumber) validationMovementData.batchNumber = movementData.batchNumber;
      if (movementData.expiryDate) validationMovementData.expiryDate = new Date(movementData.expiryDate);
      if (movementData.unitCost !== undefined) validationMovementData.unitCost = movementData.unitCost;
      if (movementData.totalCost !== undefined) validationMovementData.totalCost = movementData.totalCost;
      if (movementData.currency) validationMovementData.currency = movementData.currency;
      if (movementData.referenceId) validationMovementData.referenceId = movementData.referenceId;
      if (movementData.referenceType) validationMovementData.referenceType = movementData.referenceType;
      if (movementData.fromLocationId) validationMovementData.fromLocationId = movementData.fromLocationId;
      if (movementData.toLocationId) validationMovementData.toLocationId = movementData.toLocationId;

      const validationErrors = validateStockMovement(validationMovementData, location.stockQuantity);
      if (validationErrors.length > 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: `Validation failed: ${validationErrors.join(', ')}`,
        });
      }

      // Calculate new quantities
      const previousQuantity = location.stockQuantity;
      let quantityChange = movementData.quantity;

      // For outbound movements, make quantity negative
      if (['OUTBOUND', 'TRANSFER', 'DAMAGED', 'EXPIRED'].includes(movementData.movementType)) {
        quantityChange = -Math.abs(quantityChange);
      } else if (['INBOUND', 'RETURN'].includes(movementData.movementType)) {
        quantityChange = Math.abs(quantityChange);
      }
      // ADJUSTMENT can be positive or negative as provided

      const newQuantity = Math.max(0, previousQuantity + quantityChange);
      const newAvailableQuantity = calculateAvailableQuantity(newQuantity, location.reservedQuantity);

      // Start transaction
      const result = await ctx.db.$transaction(async (tx) => {
        // Record the movement
        const movementCreateData: any = {
          movementType: movementData.movementType,
          quantity: quantityChange,
          previousQuantity,
          newQuantity,
          inventoryLocationId,
          createdBy: ctx.user.id,
        };

        // Only include defined optional fields
        if (movementData.reason !== undefined) movementCreateData.reason = movementData.reason;
        if (movementData.notes !== undefined) movementCreateData.notes = movementData.notes;
        if (movementData.batchNumber !== undefined) movementCreateData.batchNumber = movementData.batchNumber;
        if (movementData.expiryDate !== undefined) movementCreateData.expiryDate = new Date(movementData.expiryDate);
        if (movementData.unitCost !== undefined) movementCreateData.unitCost = movementData.unitCost;
        if (movementData.totalCost !== undefined) movementCreateData.totalCost = movementData.totalCost;
        if (movementData.currency !== undefined) movementCreateData.currency = movementData.currency;
        if (movementData.referenceId !== undefined) movementCreateData.referenceId = movementData.referenceId;
        if (movementData.referenceType !== undefined) movementCreateData.referenceType = movementData.referenceType;
        if (movementData.fromLocationId !== undefined) movementCreateData.fromLocationId = movementData.fromLocationId;
        if (movementData.toLocationId !== undefined) movementCreateData.toLocationId = movementData.toLocationId;

        const movement = await tx.inventoryMovement.create({
          data: movementCreateData,
        });

        // Calculate new average cost if unit cost is provided for inbound movements
        let newAverageCost = location.averageCost;
        if (movementData.unitCost && movementData.movementType === 'INBOUND' && newQuantity > 0) {
          const currentAvgCost = location.averageCost ? Number(location.averageCost) : 0;
          const calculatedCost = ((currentAvgCost * previousQuantity) + (movementData.unitCost * Math.abs(quantityChange))) / newQuantity;
          newAverageCost = new Prisma.Decimal(calculatedCost);
        }

        // Update inventory location
        const updatedLocation = await tx.productInventoryLocation.update({
          where: { id: inventoryLocationId },
          data: {
            stockQuantity: newQuantity,
            availableQuantity: newAvailableQuantity,
            // Update average cost if unit cost is provided
            ...(movementData.unitCost && movementData.movementType === 'INBOUND' ? {
              averageCost: newAverageCost,
              lastCost: movementData.unitCost,
            } : {}),
          },
        });

        return { movement, updatedLocation };
      });

      return {
        success: true,
        message: 'Stock movement recorded successfully',
        movement: result.movement,
        updatedLocation: result.updatedLocation,
        quantityChange,
      };
    }),

  // Create inventory reservation
  createInventoryReservation: protectedProcedure
    .use(requirePermission('PRODUCT_WRITE'))
    .input(z.object({
      inventoryLocationId: z.string().cuid(),
      reservationData: z.object({
        reservationType: z.enum(['ORDER', 'QUOTE', 'MANUAL', 'TRANSFER', 'QUALITY_HOLD']),
        quantity: z.number().int().positive(),
        reservedUntil: z.string().datetime(),
        referenceId: z.string().optional(),
        referenceType: z.string().optional(),
        notes: z.string().optional(),
      }),
    }))
    .mutation(async ({ input, ctx }) => {
      const { inventoryLocationId, reservationData } = input;

      // Ensure user has factoryId
      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'User must be associated with a factory',
        });
      }

      // Get current inventory location with active reservations
      const location = await ctx.db.productInventoryLocation.findFirst({
        where: {
          id: inventoryLocationId,
          factoryId: ctx.user.factoryId,
        },
        include: {
          reservations: {
            where: {
              status: 'ACTIVE',
              reservedUntil: {
                gte: new Date(),
              },
            },
          },
        },
      });

      if (!location) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Inventory location not found or access denied',
        });
      }

      // Calculate current available quantity
      const currentReserved = location.reservations.reduce((sum: number, res) => sum + res.quantity, 0);
      const availableQuantity = calculateAvailableQuantity(location.stockQuantity, currentReserved);

      // Validate reservation
      const validationReservationData: ReservationData = {
        reservationType: reservationData.reservationType,
        quantity: reservationData.quantity,
        reservedUntil: new Date(reservationData.reservedUntil),
      };
      if (reservationData.referenceId) validationReservationData.referenceId = reservationData.referenceId;
      if (reservationData.referenceType) validationReservationData.referenceType = reservationData.referenceType;
      if (reservationData.notes) validationReservationData.notes = reservationData.notes;

      const validationErrors = validateReservation(validationReservationData, availableQuantity);

      if (validationErrors.length > 0) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: `Validation failed: ${validationErrors.join(', ')}`,
        });
      }

      // Create reservation
      const reservationCreateData: any = {
        reservationType: reservationData.reservationType,
        quantity: reservationData.quantity,
        reservedUntil: new Date(reservationData.reservedUntil),
        inventoryLocationId,
        createdBy: ctx.user.id,
      };

      // Only include defined optional fields
      if (reservationData.referenceId !== undefined) reservationCreateData.referenceId = reservationData.referenceId;
      if (reservationData.referenceType !== undefined) reservationCreateData.referenceType = reservationData.referenceType;
      if (reservationData.notes !== undefined) reservationCreateData.notes = reservationData.notes;

      const reservation = await ctx.db.inventoryReservation.create({
        data: reservationCreateData,
      });

      // Update location's reserved quantity
      const newReservedQuantity = currentReserved + reservationData.quantity;
      const newAvailableQuantity = calculateAvailableQuantity(location.stockQuantity, newReservedQuantity);

      await ctx.db.productInventoryLocation.update({
        where: { id: inventoryLocationId },
        data: {
          reservedQuantity: newReservedQuantity,
          availableQuantity: newAvailableQuantity,
        },
      });

      return {
        success: true,
        message: 'Inventory reservation created successfully',
        reservation,
        newAvailableQuantity,
      };
    }),

  // Release inventory reservation
  releaseInventoryReservation: protectedProcedure
    .use(requirePermission('PRODUCT_WRITE'))
    .input(z.object({
      reservationId: z.string().cuid(),
      status: z.enum(['FULFILLED', 'CANCELLED']).optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      const { reservationId, status = 'CANCELLED' } = input;

      // Ensure user has factoryId
      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'User must be associated with a factory',
        });
      }

      // Get reservation with location info
      const reservation = await ctx.db.inventoryReservation.findFirst({
        where: {
          id: reservationId,
        },
        include: {
          inventoryLocation: true,
        },
      });

      // Check if reservation exists and user has access to the factory
      if (!reservation || reservation.inventoryLocation.factoryId !== ctx.user.factoryId) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Reservation not found or access denied',
        });
      }



      if (reservation.status !== 'ACTIVE') {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Reservation is not active',
        });
      }

      // Update reservation status
      const updatedReservation = await ctx.db.inventoryReservation.update({
        where: { id: reservationId },
        data: { status },
      });

      // Update location's reserved quantity
      const location = reservation.inventoryLocation;
      const newReservedQuantity = Math.max(0, location.reservedQuantity - reservation.quantity);
      const newAvailableQuantity = calculateAvailableQuantity(location.stockQuantity, newReservedQuantity);

      await ctx.db.productInventoryLocation.update({
        where: { id: location.id },
        data: {
          reservedQuantity: newReservedQuantity,
          availableQuantity: newAvailableQuantity,
        },
      });

      return {
        success: true,
        message: `Reservation ${status.toLowerCase()} successfully`,
        reservation: updatedReservation,
        newAvailableQuantity,
      };
    }),

  // Get inventory analytics
  getInventoryAnalytics: protectedProcedure
    .use(requirePermission('PRODUCT_READ'))
    .input(z.object({
      productId: z.string().cuid().optional(),
      locationId: z.string().cuid().optional(),
      days: z.number().int().min(1).max(365).default(30),
    }))
    .query(async ({ input, ctx }) => {
      const { productId, locationId, days } = input;

      // Ensure user has factoryId
      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'User must be associated with a factory',
        });
      }

      const whereClause: any = {
        factoryId: ctx.user.factoryId,
      };

      if (productId) {
        whereClause.productId = productId;
      }

      if (locationId) {
        whereClause.id = locationId;
      }

      // Get inventory locations with movements and reservations
      const locations = await ctx.db.productInventoryLocation.findMany({
        where: whereClause,
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
            },
          },
          movements: {
            where: {
              createdAt: {
                gte: new Date(Date.now() - days * 24 * 60 * 60 * 1000),
              },
            },
            orderBy: {
              createdAt: 'desc',
            },
          },
          reservations: {
            where: {
              status: 'ACTIVE',
            },
          },
          alerts: {
            where: {
              status: 'ACTIVE',
            },
          },
        },
      });

      // Calculate overall metrics
      const locationsForMetrics = locations.map(loc => {
        const result: { stockQuantity: number; reservedQuantity: number; averageCost?: number; lastCost?: number } = {
          stockQuantity: loc.stockQuantity,
          reservedQuantity: loc.reservedQuantity,
        };
        if (loc.averageCost) result.averageCost = Number(loc.averageCost);
        if (loc.lastCost) result.lastCost = Number(loc.lastCost);
        return result;
      });
      const overallMetrics = calculateInventoryMetrics(locationsForMetrics);

      // Calculate location-specific analytics
      const locationAnalytics = locations.map(location => {
        const performance = calculateLocationPerformance(location.movements, days);
        const activeReservations = location.reservations.reduce((sum, res) => sum + res.quantity, 0);
        const availableQuantity = calculateAvailableQuantity(location.stockQuantity, activeReservations);

        // Calculate inventory turnover
        const avgCost = location.averageCost ? Number(location.averageCost) : 0;
        const totalValue = location.stockQuantity * avgCost;
        const costOfGoodsSold = location.movements
          .filter(m => m.movementType === 'OUTBOUND')
          .reduce((sum, m) => {
            const unitCost = m.unitCost ? Number(m.unitCost) : avgCost;
            return sum + (Math.abs(m.quantity) * unitCost);
          }, 0);

        const turnover = calculateInventoryTurnover(costOfGoodsSold, totalValue);
        const daysOfInventory = calculateDaysOfInventory(totalValue, costOfGoodsSold);

        // Calculate stockout risk
        const stockoutRisk = calculateStockoutRisk(
          location.stockQuantity,
          performance.averageDailyUsage,
          location.leadTime || 7,
          location.safetyStock || 0
        );

        return {
          locationId: location.id,
          locationName: location.locationName,
          productId: location.productId,
          productName: location.product.name,
          stockQuantity: location.stockQuantity,
          reservedQuantity: activeReservations,
          availableQuantity,
          performance,
          turnover,
          daysOfInventory,
          stockoutRisk,
          totalValue,
          activeAlerts: location.alerts.length,
        };
      });

      // Calculate summary statistics
      const totalProducts = new Set(locations.map(l => l.productId)).size;
      const totalLocations = locations.length;
      const lowStockLocations = locationAnalytics.filter(l => l.stockoutRisk === 'HIGH' || l.stockoutRisk === 'CRITICAL').length;
      const averageTurnover = locationAnalytics.reduce((sum, l) => sum + l.turnover, 0) / locationAnalytics.length;

      return {
        summary: {
          totalProducts,
          totalLocations,
          lowStockLocations,
          averageTurnover: isNaN(averageTurnover) ? 0 : averageTurnover,
          ...overallMetrics,
        },
        locations: locationAnalytics,
        period: {
          days,
          startDate: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString(),
          endDate: new Date().toISOString(),
        },
      };
    }),

  // Get inventory alerts
  getInventoryAlerts: protectedProcedure
    .use(requirePermission('PRODUCT_READ'))
    .input(z.object({
      status: z.enum(['ACTIVE', 'ACKNOWLEDGED', 'RESOLVED', 'DISMISSED']).optional(),
      severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).optional(),
      alertType: z.enum(['LOW_STOCK', 'OUT_OF_STOCK', 'OVERSTOCK', 'REORDER_POINT', 'EXPIRY_WARNING', 'SLOW_MOVING', 'FAST_MOVING', 'COST_VARIANCE']).optional(),
      limit: z.number().int().min(1).max(100).default(50),
    }))
    .query(async ({ input, ctx }) => {
      const { status, severity, alertType, limit } = input;

      // Ensure user has factoryId
      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'User must be associated with a factory',
        });
      }

      const alerts = await ctx.db.inventoryAlert.findMany({
        where: {
          inventoryLocation: {
            factoryId: ctx.user.factoryId,
          },
          ...(status ? { status } : {}),
          ...(severity ? { severity } : {}),
          ...(alertType ? { alertType } : {}),
        },
        include: {
          inventoryLocation: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                },
              },
            },
          },
        },
        orderBy: [
          { severity: 'desc' },
          { createdAt: 'desc' },
        ],
        take: limit,
      });

      // Group alerts by severity for summary
      const alertSummary = alerts.reduce((acc, alert) => {
        acc[alert.severity] = (acc[alert.severity] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return {
        alerts: alerts.map(alert => ({
          ...alert,
          productName: alert.inventoryLocation?.product?.name || 'Unknown Product',
          locationName: alert.inventoryLocation?.locationName || 'Unknown Location',
        })),
        summary: {
          total: alerts.length,
          bySeverity: alertSummary,
        },
      };
    }),

  // Get stock movements for a product
  getStockMovements: protectedProcedure
    .use(requirePermission('PRODUCT_READ'))
    .input(z.object({
      productId: z.string().cuid(),
      factoryId: z.string().cuid(),
      limit: z.number().int().min(1).max(100).default(50),
    }))
    .query(async ({ input, ctx }) => {
      const { productId, factoryId, limit } = input;

      // Ensure user has access to this factory
      if (ctx.user.factoryId !== factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied to this factory',
        });
      }

      const movements = await ctx.db.inventoryMovement.findMany({
        where: {
          inventoryLocation: {
            productId,
            factoryId,
          },
        },
        include: {
          inventoryLocation: {
            select: {
              locationName: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
      });

      return movements;
    }),

  // Get inventory reservations for a product
  getInventoryReservations: protectedProcedure
    .use(requirePermission('PRODUCT_READ'))
    .input(z.object({
      productId: z.string().cuid(),
      factoryId: z.string().cuid(),
      status: z.enum(['ACTIVE', 'FULFILLED', 'CANCELLED', 'EXPIRED']).optional(),
      limit: z.number().int().min(1).max(100).default(50),
    }))
    .query(async ({ input, ctx }) => {
      const { productId, factoryId, status, limit } = input;

      // Ensure user has access to this factory
      if (ctx.user.factoryId !== factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied to this factory',
        });
      }

      const reservations = await ctx.db.inventoryReservation.findMany({
        where: {
          inventoryLocation: {
            productId,
            factoryId,
          },
          ...(status ? { status } : {}),
        },
        include: {
          inventoryLocation: {
            select: {
              locationName: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
      });

      return reservations;
    }),

  // Acknowledge inventory alert
  acknowledgeInventoryAlert: protectedProcedure
    .use(requirePermission('PRODUCT_WRITE'))
    .input(z.object({
      alertId: z.string().cuid(),
      factoryId: z.string().cuid(),
    }))
    .mutation(async ({ input, ctx }) => {
      const { alertId, factoryId } = input;

      // Ensure user has access to this factory
      if (ctx.user.factoryId !== factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied to this factory',
        });
      }

      const alert = await ctx.db.inventoryAlert.findFirst({
        where: {
          id: alertId,
          inventoryLocation: {
            factoryId,
          },
        },
      });

      if (!alert) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alert not found or access denied',
        });
      }

      const updatedAlert = await ctx.db.inventoryAlert.update({
        where: { id: alertId },
        data: {
          status: 'ACKNOWLEDGED',
          acknowledgedAt: new Date(),
          acknowledgedBy: ctx.user.id,
        },
      });

      return {
        success: true,
        message: 'Alert acknowledged successfully',
        alert: updatedAlert,
      };
    }),
});
