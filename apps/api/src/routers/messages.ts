import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, protectedProcedure } from '../lib/trpc';
import { messagingService } from '../lib/realtime/messaging-service';
import { logger } from '../lib/logging/logger';
import { supabaseStorage } from '../lib/storage/supabase-storage';

// Input validation schemas
const MessageTypeSchema = z.enum(['TEXT', 'IMAGE', 'FILE', 'VOICE', 'VIDEO', 'LOCATION', 'CONTACT', 'SYSTEM']);
const ConversationTypeSchema = z.enum(['DIRECT', 'GROUP', 'SUPPORT']);
const ParticipantRoleSchema = z.enum(['OWNER', 'ADMIN', 'MEMBER', 'READONLY']);

export const messagesRouter = router({
  // Create a new conversation
  createConversation: protectedProcedure
    .input(z.object({
      type: ConversationTypeSchema.default('DIRECT'),
      subject: z.string().optional(),
      participantIds: z.array(z.string().cuid()).min(1),
      contextType: z.enum(['quote', 'order', 'inquiry', 'product']).optional(),
      contextId: z.string().cuid().optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        // Validate factory access for all participants
        if (!ctx.user.factoryId) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'User must belong to a factory',
          });
        }

        const participants = await ctx.db.user.findMany({
          where: {
            id: { in: input.participantIds },
            factoryId: ctx.user.factoryId,
          },
        });

        if (participants.length !== input.participantIds.length) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Some participants are not in your factory',
          });
        }

        // Validate context access if provided
        if (input.contextType && input.contextId) {
          await validateContextAccess(ctx, input.contextType, input.contextId);
        }

        const conversationId = await messagingService.createConversation({
          type: input.type,
          ...(input.subject && { subject: input.subject }),
          factoryId: ctx.user.factoryId!,
          participantIds: [...input.participantIds, ctx.user.id],
          createdBy: ctx.user.id,
          ...(input.contextId && { contextId: input.contextId }),
          ...(input.contextType && { contextType: input.contextType }),
        });

        // Get the created conversation with participants
        const conversation = await ctx.db.conversation.findUnique({
          where: { id: conversationId },
          include: {
            participants: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    avatar: true,
                    role: true,
                  },
                },
              },
            },
          },
        });

        logger.info('Conversation created', {
          conversationId,
          userId: ctx.user.id,
          participantCount: input.participantIds.length,
        });

        return conversation;
      } catch (error) {
        logger.error('Failed to create conversation', {
          userId: ctx.user.id,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        throw error;
      }
    }),

  // Get user's conversations
  getConversations: protectedProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      type: ConversationTypeSchema.optional(),
    }))
    .query(async ({ input, ctx }) => {
      const { page, limit, type } = input;
      const skip = (page - 1) * limit;

      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'User must belong to a factory',
        });
      }

      const where = {
        participants: {
          some: {
            userId: ctx.user.id,
            leftAt: null,
          },
        },
        factoryId: ctx.user.factoryId,
        isActive: true,
        ...(type && { type }),
      };

      const [conversations, total] = await Promise.all([
        ctx.db.conversation.findMany({
          where,
          skip,
          take: limit,
          orderBy: { lastMessageAt: 'desc' },
          include: {
            participants: {
              where: { leftAt: null },
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    avatar: true,
                    role: true,
                  },
                },
              },
            },
            messages: {
              take: 1,
              orderBy: { createdAt: 'desc' },
              include: {
                sender: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    avatar: true,
                  },
                },
              },
            },
          },
        }),
        ctx.db.conversation.count({ where }),
      ]);

      return {
        data: conversations.map(conv => ({
          id: conv.id,
          type: conv.type,
          subject: conv.subject,
          messageCount: conv.messageCount,
          lastMessageAt: conv.lastMessageAt,
          participants: conv.participants.map((p: any) => ({
            id: p.id,
            role: p.role,
            user: p.user,
            unreadCount: p.unreadCount,
            lastReadAt: p.lastReadAt,
          })),
          lastMessage: (conv as any).messages[0] || null,
          createdAt: conv.createdAt,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1,
        },
      };
    }),

  // Get messages in a conversation
  getMessages: protectedProcedure
    .input(z.object({
      conversationId: z.string().cuid(),
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(50),
    }))
    .query(async ({ input, ctx }) => {
      const { conversationId, page, limit } = input;
      const skip = (page - 1) * limit;

      // Verify user has access to this conversation
      const participant = await ctx.db.conversationParticipant.findUnique({
        where: {
          conversationId_userId: {
            conversationId,
            userId: ctx.user.id,
          },
        },
      });

      if (!participant || participant.leftAt) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied to conversation',
        });
      }

      const [messages, total] = await Promise.all([
        ctx.db.message.findMany({
          where: {
            conversationId,
            isDeleted: false,
          },
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            sender: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
                role: true,
              },
            },
            messageAttachments: {
              select: {
                id: true,
                filename: true,
                originalName: true,
                mimeType: true,
                fileSize: true,
                fileUrl: true,
              },
            },
            readReceipts: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    avatar: true,
                  },
                },
              },
            },
            replyTo: {
              select: {
                id: true,
                content: true,
                sender: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        }),
        ctx.db.message.count({
          where: {
            conversationId,
            isDeleted: false,
          },
        }),
      ]);

      return {
        data: messages.reverse().map(message => ({
          id: message.id,
          content: message.content,
          messageType: message.messageType,
          sender: (message as any).sender,
          attachments: (message as any).messageAttachments,
          readReceipts: (message as any).readReceipts,
          replyTo: (message as any).replyTo,
          isEdited: message.isEdited,
          editedAt: message.editedAt,
          createdAt: message.createdAt,
          updatedAt: message.updatedAt,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1,
        },
      };
    }),

  // Send a message
  sendMessage: protectedProcedure
    .input(z.object({
      conversationId: z.string().cuid(),
      content: z.string().min(1),
      messageType: MessageTypeSchema.default('TEXT'),
      replyToId: z.string().cuid().optional(),
      attachmentIds: z.array(z.string().cuid()).optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        // Verify user can write to this conversation
        const participant = await ctx.db.conversationParticipant.findUnique({
          where: {
            conversationId_userId: {
              conversationId: input.conversationId,
              userId: ctx.user.id,
            },
          },
        });

        if (!participant || participant.leftAt || !participant.canWrite) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Cannot send message to this conversation',
          });
        }

        // Validate reply-to message if provided
        if (input.replyToId) {
          const replyToMessage = await ctx.db.message.findFirst({
            where: {
              id: input.replyToId,
              conversationId: input.conversationId,
              isDeleted: false,
            },
          });

          if (!replyToMessage) {
            throw new TRPCError({
              code: 'BAD_REQUEST',
              message: 'Reply-to message not found',
            });
          }
        }

        // Create message
        const message = await ctx.db.message.create({
          data: {
            content: input.content,
            messageType: input.messageType,
            conversationId: input.conversationId,
            senderId: ctx.user.id,
            factoryId: ctx.user.factoryId!,
            ...(input.replyToId && { replyToId: input.replyToId }),
          },
          include: {
            sender: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
                role: true,
              },
            },
            replyTo: {
              select: {
                id: true,
                content: true,
                sender: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
            messageAttachments: {
              orderBy: {
                createdAt: 'asc',
              },
            },
          },
        });

        // Handle attachments if provided
        if (input.attachmentIds && input.attachmentIds.length > 0) {
          await ctx.db.messageAttachment.updateMany({
            where: {
              id: { in: input.attachmentIds },
              factoryId: ctx.user.factoryId!,
            },
            data: {
              messageId: message.id,
            },
          });
        }

        // Handle real-time updates
        await messagingService.handleNewMessage({
          id: message.id,
          content: message.content,
          conversationId: input.conversationId,
          senderId: ctx.user.id,
          messageType: input.messageType,
        });

        logger.info('Message sent', {
          messageId: message.id,
          conversationId: input.conversationId,
          userId: ctx.user.id,
        });

        return message;
      } catch (error) {
        logger.error('Failed to send message', {
          conversationId: input.conversationId,
          userId: ctx.user.id,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        throw error;
      }
    }),

  // Edit a message
  editMessage: protectedProcedure
    .input(z.object({
      messageId: z.string().cuid(),
      content: z.string().min(1),
    }))
    .mutation(async ({ input, ctx }) => {
      // Verify user owns this message
      const message = await ctx.db.message.findFirst({
        where: {
          id: input.messageId,
          senderId: ctx.user.id,
          isDeleted: false,
        },
      });

      if (!message) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Message not found or access denied',
        });
      }

      const updatedMessage = await ctx.db.message.update({
        where: { id: input.messageId },
        data: {
          content: input.content,
          isEdited: true,
          editedAt: new Date(),
        },
        include: {
          sender: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true,
            },
          },
        },
      });

      logger.info('Message edited', {
        messageId: input.messageId,
        userId: ctx.user.id,
      });

      return updatedMessage;
    }),

  // Delete a message
  deleteMessage: protectedProcedure
    .input(z.object({
      messageId: z.string().cuid(),
    }))
    .mutation(async ({ input, ctx }) => {
      // Verify user owns this message
      const message = await ctx.db.message.findFirst({
        where: {
          id: input.messageId,
          senderId: ctx.user.id,
          isDeleted: false,
        },
      });

      if (!message) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Message not found or access denied',
        });
      }

      await ctx.db.message.update({
        where: { id: input.messageId },
        data: {
          isDeleted: true,
          deletedAt: new Date(),
        },
      });

      logger.info('Message deleted', {
        messageId: input.messageId,
        userId: ctx.user.id,
      });

      return { success: true };
    }),

  // Mark message as read
  markMessageRead: protectedProcedure
    .input(z.object({
      messageId: z.string().cuid(),
      conversationId: z.string().cuid(),
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        await messagingService.handleMessageRead(
          input.messageId,
          ctx.user.id,
          input.conversationId
        );

        return { success: true };
      } catch (error) {
        logger.error('Failed to mark message as read', {
          messageId: input.messageId,
          userId: ctx.user.id,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to mark message as read',
        });
      }
    }),

  // Get unread message count
  getUnreadCount: protectedProcedure
    .query(async ({ ctx }) => {
      const totalUnread = await ctx.db.conversationParticipant.aggregate({
        where: {
          userId: ctx.user.id,
          leftAt: null,
        },
        _sum: {
          unreadCount: true,
        },
      });

      return { count: totalUnread._sum.unreadCount || 0 };
    }),

  // Add participant to conversation
  addParticipant: protectedProcedure
    .input(z.object({
      conversationId: z.string().cuid(),
      userId: z.string().cuid(),
      role: ParticipantRoleSchema.default('MEMBER'),
    }))
    .mutation(async ({ input, ctx }) => {
      // Verify user has admin access to this conversation
      const userParticipant = await ctx.db.conversationParticipant.findUnique({
        where: {
          conversationId_userId: {
            conversationId: input.conversationId,
            userId: ctx.user.id,
          },
        },
      });

      if (!userParticipant || !['OWNER', 'ADMIN'].includes(userParticipant.role)) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Insufficient permissions to add participants',
        });
      }

      // Verify new participant is in same factory
      const newUser = await ctx.db.user.findFirst({
        where: {
          id: input.userId,
          factoryId: ctx.user.factoryId!,
        },
      });

      if (!newUser) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'User not found in your factory',
        });
      }

      // Add participant
      const participant = await ctx.db.conversationParticipant.create({
        data: {
          conversationId: input.conversationId,
          userId: input.userId,
          role: input.role,
          canWrite: true,
          canRead: true,
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              avatar: true,
              role: true,
            },
          },
        },
      });

      logger.info('Participant added to conversation', {
        conversationId: input.conversationId,
        newUserId: input.userId,
        addedBy: ctx.user.id,
      });

      return participant;
    }),

  // Remove participant from conversation
  removeParticipant: protectedProcedure
    .input(z.object({
      conversationId: z.string().cuid(),
      userId: z.string().cuid(),
    }))
    .mutation(async ({ input, ctx }) => {
      // Verify user has admin access to this conversation
      const userParticipant = await ctx.db.conversationParticipant.findUnique({
        where: {
          conversationId_userId: {
            conversationId: input.conversationId,
            userId: ctx.user.id,
          },
        },
      });

      if (!userParticipant || !['OWNER', 'ADMIN'].includes(userParticipant.role)) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Insufficient permissions to remove participants',
        });
      }

      // Update participant to mark as left
      await ctx.db.conversationParticipant.update({
        where: {
          conversationId_userId: {
            conversationId: input.conversationId,
            userId: input.userId,
          },
        },
        data: {
          leftAt: new Date(),
        },
      });

      logger.info('Participant removed from conversation', {
        conversationId: input.conversationId,
        removedUserId: input.userId,
        removedBy: ctx.user.id,
      });

      return { success: true };
    }),

  // Generate upload URL for message attachment
  generateAttachmentUploadUrl: protectedProcedure
    .input(z.object({
      fileName: z.string().min(1).max(255),
      fileSize: z.number().positive().max(50 * 1024 * 1024), // 50MB max for messages
      mimeType: z.string().min(1),
      conversationId: z.string().cuid(),
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        const { fileName, fileSize, mimeType, conversationId } = input;
        const factoryId = ctx.user.factoryId;

        if (!factoryId) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'User must be associated with a factory',
          });
        }

        // Verify user can write to this conversation
        const participant = await ctx.db.conversationParticipant.findUnique({
          where: {
            conversationId_userId: {
              conversationId,
              userId: ctx.user.id,
            },
          },
        });

        if (!participant || participant.leftAt || !participant.canWrite) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Cannot upload attachments to this conversation',
          });
        }

        logger.info('Generating message attachment upload URL', {
          userId: ctx.user.id,
          factoryId,
          conversationId,
          fileName,
          fileSize,
          mimeType,
        });

        const result = await supabaseStorage.generateUploadUrl(
          factoryId,
          'message',
          fileName,
          fileSize,
          mimeType,
          { conversationId, originalName: fileName }
        );

        return {
          success: true,
          ...result,
        };

      } catch (error) {
        logger.error('Failed to generate message attachment upload URL', {
          error,
          userId: ctx.user.id,
          input,
        });
        throw error;
      }
    }),

  // Confirm message attachment upload and create attachment record
  confirmAttachmentUpload: protectedProcedure
    .input(z.object({
      filePath: z.string().min(1),
      conversationId: z.string().cuid(),
      originalName: z.string().min(1),
      messageId: z.string().cuid().optional(), // Optional - can be linked later
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        const { filePath, conversationId, originalName, messageId } = input;
        const factoryId = ctx.user.factoryId;

        if (!factoryId) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'User must be associated with a factory',
          });
        }

        // Verify the file path belongs to this factory
        if (!filePath.includes(factoryId)) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Access denied to file',
          });
        }

        // Verify user can write to this conversation
        const participant = await ctx.db.conversationParticipant.findUnique({
          where: {
            conversationId_userId: {
              conversationId,
              userId: ctx.user.id,
            },
          },
        });

        if (!participant || participant.leftAt || !participant.canWrite) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Cannot upload attachments to this conversation',
          });
        }

        logger.info('Confirming message attachment upload', {
          userId: ctx.user.id,
          factoryId,
          conversationId,
          filePath,
        });

        // Confirm upload with storage service
        const result = await supabaseStorage.confirmUpload(
          filePath,
          factoryId,
          'message',
          { conversationId, originalName }
        );

        // Create message attachment record
        const attachment = await ctx.db.messageAttachment.create({
          data: {
            filename: result.fileInfo.path.split('/').pop() || 'unknown',
            originalName,
            mimeType: result.fileInfo.mimeType,
            fileSize: result.fileInfo.size,
            fileUrl: result.fileInfo.publicUrl,
            thumbnailUrl: result.fileInfo.thumbnailUrl,
            storageProvider: 'supabase',
            storagePath: result.fileInfo.path,
            bucketName: 'fc-china-uploads',
            factoryId,
            uploadStatus: 'COMPLETED',
            uploadProgress: 100,
            ...(messageId && { messageId }),
            // Extract image dimensions from metadata if available
            ...(result.fileInfo.metadata?.width && { width: result.fileInfo.metadata.width }),
            ...(result.fileInfo.metadata?.height && { height: result.fileInfo.metadata.height }),
          },
        });

        logger.info('Message attachment created', {
          attachmentId: attachment.id,
          filePath,
          size: result.fileInfo.size,
        });

        return {
          success: true,
          attachment,
          ...result.fileInfo,
        };

      } catch (error) {
        logger.error('Failed to confirm message attachment upload', {
          error,
          userId: ctx.user.id,
          input,
        });
        throw error;
      }
    }),

  // Link existing attachments to a message
  linkAttachmentsToMessage: protectedProcedure
    .input(z.object({
      messageId: z.string().cuid(),
      attachmentIds: z.array(z.string().cuid()).min(1).max(10), // Max 10 attachments per message
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        const { messageId, attachmentIds } = input;
        const factoryId = ctx.user.factoryId;

        if (!factoryId) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'User must be associated with a factory',
          });
        }

        // Verify user owns the message
        const message = await ctx.db.message.findFirst({
          where: {
            id: messageId,
            senderId: ctx.user.id,
            factoryId,
          },
        });

        if (!message) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Message not found or access denied',
          });
        }

        // Verify attachments belong to this factory and are not already linked
        const attachments = await ctx.db.messageAttachment.findMany({
          where: {
            id: { in: attachmentIds },
            factoryId,
            messageId: null, // Only unlinked attachments
          },
        });

        if (attachments.length !== attachmentIds.length) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Some attachments not found or already linked',
          });
        }

        // Link attachments to message
        await ctx.db.messageAttachment.updateMany({
          where: {
            id: { in: attachmentIds },
            factoryId,
          },
          data: {
            messageId,
          },
        });

        logger.info('Attachments linked to message', {
          messageId,
          attachmentIds,
          userId: ctx.user.id,
        });

        return {
          success: true,
          linkedCount: attachments.length,
        };

      } catch (error) {
        logger.error('Failed to link attachments to message', {
          error,
          userId: ctx.user.id,
          input,
        });
        throw error;
      }
    }),

  // Get message attachments
  getMessageAttachments: protectedProcedure
    .input(z.object({
      messageId: z.string().cuid(),
    }))
    .query(async ({ input, ctx }) => {
      try {
        const { messageId } = input;
        const factoryId = ctx.user.factoryId;

        if (!factoryId) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'User must be associated with a factory',
          });
        }

        // Verify user has access to this message
        const message = await ctx.db.message.findFirst({
          where: {
            id: messageId,
            factoryId,
          },
          include: {
            conversation: {
              include: {
                participants: {
                  where: {
                    userId: ctx.user.id,
                    leftAt: null,
                  },
                },
              },
            },
          },
        });

        if (!message || !message.conversation.participants.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Message not found or access denied',
          });
        }

        // Get attachments
        const attachments = await ctx.db.messageAttachment.findMany({
          where: {
            messageId,
            factoryId,
          },
          orderBy: {
            createdAt: 'asc',
          },
        });

        return {
          success: true,
          attachments,
        };

      } catch (error) {
        logger.error('Failed to get message attachments', {
          error,
          userId: ctx.user.id,
          input,
        });
        throw error;
      }
    }),

  // Delete message attachment
  deleteMessageAttachment: protectedProcedure
    .input(z.object({
      attachmentId: z.string().cuid(),
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        const { attachmentId } = input;
        const factoryId = ctx.user.factoryId;

        if (!factoryId) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'User must be associated with a factory',
          });
        }

        // Verify attachment exists and user has access
        const attachment = await ctx.db.messageAttachment.findFirst({
          where: {
            id: attachmentId,
            factoryId,
          },
          include: {
            message: {
              select: {
                id: true,
                senderId: true,
              },
            },
          },
        });

        if (!attachment) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Attachment not found or access denied',
          });
        }

        // Only message sender can delete attachments
        if (attachment.message?.senderId !== ctx.user.id) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Only message sender can delete attachments',
          });
        }

        logger.info('Deleting message attachment', {
          attachmentId,
          userId: ctx.user.id,
          storagePath: attachment.storagePath,
        });

        // Delete from storage
        await supabaseStorage.deleteFile(attachment.storagePath, factoryId);

        // Delete from database
        await ctx.db.messageAttachment.delete({
          where: {
            id: attachmentId,
          },
        });

        logger.info('Message attachment deleted successfully', {
          attachmentId,
        });

        return {
          success: true,
          message: 'Attachment deleted successfully',
        };

      } catch (error) {
        logger.error('Failed to delete message attachment', {
          error,
          userId: ctx.user.id,
          input,
        });
        throw error;
      }
    }),

  // Get unlinked attachments for a conversation (for cleanup or linking)
  getUnlinkedAttachments: protectedProcedure
    .input(z.object({
      conversationId: z.string().cuid(),
      limit: z.number().min(1).max(50).default(20),
    }))
    .query(async ({ input, ctx }) => {
      try {
        const { conversationId, limit } = input;
        const factoryId = ctx.user.factoryId;

        if (!factoryId) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'User must be associated with a factory',
          });
        }

        // Verify user has access to this conversation
        const participant = await ctx.db.conversationParticipant.findUnique({
          where: {
            conversationId_userId: {
              conversationId,
              userId: ctx.user.id,
            },
          },
        });

        if (!participant || participant.leftAt) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Access denied to conversation',
          });
        }

        // Get unlinked attachments (created in the last 24 hours)
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

        const attachments = await ctx.db.messageAttachment.findMany({
          where: {
            factoryId,
            messageId: null, // Unlinked
            createdAt: {
              gte: oneDayAgo,
            },
            // Filter by conversation context if stored in metadata
            // This would require storing conversationId in metadata during upload
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: limit,
        });

        return {
          success: true,
          attachments,
        };

      } catch (error) {
        logger.error('Failed to get unlinked attachments', {
          error,
          userId: ctx.user.id,
          input,
        });
        throw error;
      }
    }),
});

// Helper function to validate context access
async function validateContextAccess(
  ctx: any,
  contextType: 'quote' | 'order' | 'inquiry' | 'product',
  contextId: string
) {
  switch (contextType) {
    case 'quote':
      const quote = await ctx.db.quote.findFirst({
        where: {
          id: contextId,
          factoryId: ctx.user.factoryId,
        },
      });
      if (!quote) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Quote not found or access denied',
        });
      }
      break;

    case 'order':
      const order = await ctx.db.order.findFirst({
        where: {
          id: contextId,
          OR: [
            { factoryId: ctx.user.factoryId },
            { customerEmail: ctx.user.email },
          ],
        },
      });
      if (!order) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Order not found or access denied',
        });
      }
      break;

    case 'inquiry':
      const inquiry = await ctx.db.inquiry.findFirst({
        where: {
          id: contextId,
          OR: [
            { factoryId: ctx.user.factoryId },
            { customerEmail: ctx.user.email },
          ],
        },
      });
      if (!inquiry) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Inquiry not found or access denied',
        });
      }
      break;

    case 'product':
      const product = await ctx.db.product.findFirst({
        where: {
          id: contextId,
          factoryId: ctx.user.factoryId,
        },
      });
      if (!product) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Product not found or access denied',
        });
      }
      break;

    default:
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'Invalid context type',
      });
  }
}
