import { z } from 'zod';
import { router, protectedProcedure } from '../lib/trpc';
import { supabaseStorage } from '../lib/storage/supabase-storage';
import { logger } from '../lib/logging/logger';
import { TRPCError } from '@trpc/server';

// Input validation schemas
const generateUploadUrlSchema = z.object({
  fileName: z.string().min(1).max(255),
  fileSize: z.number().positive().max(100 * 1024 * 1024), // 100MB max
  mimeType: z.string().min(1),
  uploadType: z.enum(['message', 'product', 'factory', 'document']),
  metadata: z.record(z.any()).optional(),
});

const confirmUploadSchema = z.object({
  filePath: z.string().min(1),
  uploadType: z.string().min(1),
  metadata: z.record(z.any()).optional(),
});

const deleteFileSchema = z.object({
  filePath: z.string().min(1),
});

const getFileInfoSchema = z.object({
  filePath: z.string().min(1),
});

export const uploadsRouter = router({
  /**
   * Generate a signed upload URL for direct client uploads
   */
  generateUploadUrl: protectedProcedure
    .input(generateUploadUrlSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { fileName, fileSize, mimeType, uploadType, metadata } = input;
        const factoryId = ctx.user.factoryId;

        if (!factoryId) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'User must be associated with a factory',
          });
        }

        logger.info('Generating upload URL', {
          userId: ctx.user.id,
          factoryId,
          fileName,
          fileSize,
          mimeType,
          uploadType,
        });

        const result = await supabaseStorage.generateUploadUrl(
          factoryId,
          uploadType,
          fileName,
          fileSize,
          mimeType,
          metadata
        );

        // Log the upload request for audit purposes
        await ctx.db.uploadLog.create({
          data: {
            userId: ctx.user.id,
            factoryId,
            fileName,
            fileSize,
            mimeType,
            uploadType,
            filePath: result.filePath,
            uploadId: result.uploadId,
            status: 'URL_GENERATED',
            metadata: metadata || {},
          },
        });

        return {
          success: true,
          ...result,
        };

      } catch (error) {
        logger.error('Failed to generate upload URL', {
          error,
          userId: ctx.user.id,
          input,
        });
        throw error;
      }
    }),

  /**
   * Confirm file upload and process the file
   */
  confirmUpload: protectedProcedure
    .input(confirmUploadSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { filePath, uploadType, metadata } = input;
        const factoryId = ctx.user.factoryId;

        if (!factoryId) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'User must be associated with a factory',
          });
        }

        // Verify the file path belongs to this factory
        if (!filePath.includes(factoryId)) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Access denied to file',
          });
        }

        logger.info('Confirming file upload', {
          userId: ctx.user.id,
          factoryId,
          filePath,
          uploadType,
        });

        const result = await supabaseStorage.confirmUpload(
          filePath,
          factoryId,
          uploadType,
          metadata
        );

        // Update upload log
        await ctx.db.uploadLog.updateMany({
          where: {
            filePath,
            factoryId,
          },
          data: {
            status: 'COMPLETED',
            completedAt: new Date(),
            fileSize: result.fileInfo.size,
            publicUrl: result.fileInfo.publicUrl,
            thumbnailUrl: result.fileInfo.thumbnailUrl,
          },
        });

        // Create file record in database
        const fileRecord = await ctx.db.file.create({
          data: {
            path: result.fileInfo.path,
            publicUrl: result.fileInfo.publicUrl,
            thumbnailUrl: result.fileInfo.thumbnailUrl,
            fileName: result.fileInfo.path.split('/').pop() || 'unknown',
            originalName: metadata?.originalName || result.fileInfo.path.split('/').pop() || 'unknown',
            mimeType: result.fileInfo.mimeType,
            fileSize: result.fileInfo.size,
            uploadType,
            factoryId,
            uploadedBy: ctx.user.id,
            metadata: result.fileInfo.metadata || {},
          },
        });

        logger.info('File upload confirmed and recorded', {
          fileId: fileRecord.id,
          filePath,
          size: result.fileInfo.size,
        });

        return {
          success: true,
          file: fileRecord,
          ...result.fileInfo,
        };

      } catch (error) {
        logger.error('Failed to confirm upload', {
          error,
          userId: ctx.user.id,
          input,
        });

        // Update upload log with error
        await ctx.db.uploadLog.updateMany({
          where: {
            filePath: input.filePath,
            factoryId: ctx.user.factoryId,
          },
          data: {
            status: 'FAILED',
            error: error instanceof Error ? error.message : 'Unknown error',
          },
        });

        throw error;
      }
    }),

  /**
   * Delete a file from storage and database
   */
  deleteFile: protectedProcedure
    .input(deleteFileSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { filePath } = input;
        const factoryId = ctx.user.factoryId;

        if (!factoryId) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'User must be associated with a factory',
          });
        }

        // Verify the file exists and belongs to this factory
        const fileRecord = await ctx.db.file.findFirst({
          where: {
            path: filePath,
            factoryId,
          },
        });

        if (!fileRecord) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'File not found or access denied',
          });
        }

        logger.info('Deleting file', {
          userId: ctx.user.id,
          factoryId,
          filePath,
          fileId: fileRecord.id,
        });

        // Delete from storage
        await supabaseStorage.deleteFile(filePath, factoryId);

        // Delete from database
        await ctx.db.file.delete({
          where: {
            id: fileRecord.id,
          },
        });

        logger.info('File deleted successfully', {
          fileId: fileRecord.id,
          filePath,
        });

        return {
          success: true,
          message: 'File deleted successfully',
        };

      } catch (error) {
        logger.error('Failed to delete file', {
          error,
          userId: ctx.user.id,
          input,
        });
        throw error;
      }
    }),

  /**
   * Get file information and metadata
   */
  getFileInfo: protectedProcedure
    .input(getFileInfoSchema)
    .query(async ({ input, ctx }) => {
      try {
        const { filePath } = input;
        const factoryId = ctx.user.factoryId;

        if (!factoryId) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'User must be associated with a factory',
          });
        }

        // Get file info from database
        const fileRecord = await ctx.db.file.findFirst({
          where: {
            path: filePath,
            factoryId,
          },
          include: {
            uploadedByUser: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        });

        if (!fileRecord) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'File not found or access denied',
          });
        }

        // Get current storage info
        const storageInfo = await supabaseStorage.getFileInfo(filePath, factoryId);

        return {
          success: true,
          file: {
            ...fileRecord,
            storageInfo,
          },
        };

      } catch (error) {
        logger.error('Failed to get file info', {
          error,
          userId: ctx.user.id,
          input,
        });
        throw error;
      }
    }),

  /**
   * List files for a factory with pagination and filtering
   */
  listFiles: protectedProcedure
    .input(z.object({
      uploadType: z.enum(['message', 'product', 'factory', 'document']).optional(),
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
      search: z.string().optional(),
    }))
    .query(async ({ input, ctx }) => {
      try {
        const { uploadType, limit, offset, search } = input;
        const factoryId = ctx.user.factoryId;

        if (!factoryId) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'User must be associated with a factory',
          });
        }

        const where: any = {
          factoryId,
        };

        if (uploadType) {
          where.uploadType = uploadType;
        }

        if (search) {
          where.OR = [
            { fileName: { contains: search, mode: 'insensitive' } },
            { originalName: { contains: search, mode: 'insensitive' } },
          ];
        }

        const [files, total] = await Promise.all([
          ctx.db.file.findMany({
            where,
            include: {
              uploadedByUser: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
            orderBy: {
              createdAt: 'desc',
            },
            take: limit,
            skip: offset,
          }),
          ctx.db.file.count({ where }),
        ]);

        return {
          success: true,
          files,
          pagination: {
            total,
            limit,
            offset,
            hasMore: offset + limit < total,
          },
        };

      } catch (error) {
        logger.error('Failed to list files', {
          error,
          userId: ctx.user.id,
          input,
        });
        throw error;
      }
    }),

  /**
   * Get upload statistics for the factory
   */
  getUploadStats: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        const factoryId = ctx.user.factoryId;

        if (!factoryId) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'User must be associated with a factory',
          });
        }

        const [totalFiles, totalSize, filesByType] = await Promise.all([
          ctx.db.file.count({
            where: { factoryId },
          }),
          ctx.db.file.aggregate({
            where: { factoryId },
            _sum: { fileSize: true },
          }),
          ctx.db.file.groupBy({
            by: ['uploadType'],
            where: { factoryId },
            _count: true,
            _sum: { fileSize: true },
          }),
        ]);

        return {
          success: true,
          stats: {
            totalFiles,
            totalSize: totalSize._sum.fileSize || 0,
            filesByType: filesByType.map(item => ({
              type: item.uploadType,
              count: item._count,
              size: item._sum.fileSize || 0,
            })),
          },
        };

      } catch (error) {
        logger.error('Failed to get upload stats', {
          error,
          userId: ctx.user.id,
        });
        throw error;
      }
    }),
});