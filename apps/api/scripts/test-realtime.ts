#!/usr/bin/env ts-node

import { testRealtimeInfrastructure } from '../src/lib/realtime/realtime-test';

async function runTest() {
  console.log('🚀 Starting Realtime Infrastructure Test...\n');
  
  try {
    const result = await testRealtimeInfrastructure();
    
    console.log('📊 Test Results:');
    console.log('================');
    console.log(`✅ Connection: ${result.results.connection ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Tables Enabled: ${result.results.tablesEnabled ? 'PASS' : 'FAIL'}`);
    console.log(`✅ RLS Policies: ${result.results.rlsPolicies ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Subscription Test: ${result.results.subscriptionTest ? 'PASS' : 'FAIL'}`);
    console.log(`\n🎯 Overall: ${result.success ? 'SUCCESS' : 'FAILED'}`);
    
    if (result.errors.length > 0) {
      console.log('\n❌ Errors:');
      result.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }
    
    process.exit(result.success ? 0 : 1);
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  }
}

runTest();
