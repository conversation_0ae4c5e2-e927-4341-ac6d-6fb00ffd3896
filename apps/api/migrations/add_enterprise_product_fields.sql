-- Add enterprise-level JSONB fields to products table
ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "specifications" JSONB;
ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "compliance" JSONB;
ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "qualityData" JSONB;
ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "manufacturingData" JSONB;
ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "tradingData" JSONB;
ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "marketingData" JSONB;

-- Create product price breaks table
CREATE TABLE IF NOT EXISTS "product_price_breaks" (
  "id" TEXT PRIMARY KEY,
  "min_quantity" INTEGER NOT NULL,
  "unit_price" DECIMAL(10,2) NOT NULL,
  "currency" VARCHAR(3) NOT NULL DEFAULT 'USD',
  "product_id" TEXT NOT NULL,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE
);

-- Create indexes for product price breaks
CREATE INDEX IF NOT EXISTS "idx_product_price_breaks_product_id" ON "product_price_breaks"("product_id");
CREATE INDEX IF NOT EXISTS "idx_product_price_breaks_min_quantity" ON "product_price_breaks"("min_quantity");

-- Create product inventory locations table
CREATE TABLE IF NOT EXISTS "product_inventory_locations" (
  "id" TEXT PRIMARY KEY,
  "location_name" TEXT NOT NULL,
  "stock_quantity" INTEGER DEFAULT 0,
  "reserved_quantity" INTEGER DEFAULT 0,
  "available_quantity" INTEGER DEFAULT 0,
  "reorder_point" INTEGER,
  "max_stock_level" INTEGER,
  "product_id" TEXT NOT NULL,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE
);

-- Create indexes for product inventory locations
CREATE INDEX IF NOT EXISTS "idx_product_inventory_locations_product_id" ON "product_inventory_locations"("product_id");
CREATE INDEX IF NOT EXISTS "idx_product_inventory_locations_location_name" ON "product_inventory_locations"("location_name");
CREATE INDEX IF NOT EXISTS "idx_product_inventory_locations_stock_quantity" ON "product_inventory_locations"("stock_quantity");

-- Add comments for documentation
COMMENT ON COLUMN "products"."specifications" IS 'Manufacturing details, technical specs, customization options (JSONB)';
COMMENT ON COLUMN "products"."compliance" IS 'Certifications, HS codes, export restrictions, country of origin (JSONB)';
COMMENT ON COLUMN "products"."qualityData" IS 'Quality grade, defect rate, testing protocols, supplier info (JSONB)';
COMMENT ON COLUMN "products"."manufacturingData" IS 'Production capacity, lead time, manufacturing process (JSONB)';
COMMENT ON COLUMN "products"."tradingData" IS 'Price breaks, regional pricing, seasonal adjustments (JSONB)';
COMMENT ON COLUMN "products"."marketingData" IS 'SEO, positioning, competitive advantages (JSONB)';

COMMENT ON TABLE "product_price_breaks" IS 'Volume pricing tiers for products';
COMMENT ON TABLE "product_inventory_locations" IS 'Multi-location inventory tracking for products';
